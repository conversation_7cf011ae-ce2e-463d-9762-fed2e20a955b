import {NgModule} from '@angular/core';
import {CommonModule} from '@angular/common';
import {systemsComponents, SystemsRoutes} from './systems.routes';
import {
    MatButtonModule,
    MatCardModule,
    MatCheckboxModule,
    MatDatepickerModule,
    MatDialogModule,
    MatExpansionModule,
    MatFormFieldModule,
    MatIconModule,
    MatInputModule,
    MatNativeDateModule,
    MatPaginatorModule,
    MatRadioModule,
    MatSelectModule,
    MatTableModule,
    MatToolbarModule,
    MatTooltipModule
} from '@angular/material';
import {FormsModule, ReactiveFormsModule} from '@angular/forms';
import {SharedModule} from '../shared/shared.module';
import {FlexLayoutModule} from '@angular/flex-layout';
import {NgxEchartsModule} from 'ngx-echarts';
import {SystemListService} from './system-list/system-list.service';
import {ViewDialogComponent} from './system-list/view-dialog/view-dialog.component';


@NgModule({
    imports: [
        CommonModule,
        FormsModule,
        SystemsRoutes,
        MatCardModule,
        MatTableModule,
        MatToolbarModule,
        MatIconModule,
        MatButtonModule,
        MatExpansionModule,
        FlexLayoutModule,
        MatFormFieldModule,
        MatInputModule,
        MatRadioModule,
        MatSelectModule,
        MatButtonModule,
        MatCheckboxModule,
        MatPaginatorModule,
        MatNativeDateModule,
        MatDatepickerModule,
        ReactiveFormsModule,
        MatTooltipModule,
        MatDialogModule,
        NgxEchartsModule,
        SharedModule,
    ],
    declarations: [
        ...systemsComponents,
        ViewDialogComponent
    ],
    entryComponents: [
        ViewDialogComponent
    ],
    providers: [
        SystemListService
    ]
})


export class SystemsModule {
}
