// The file contents for the current environment will overwrite these during build.
// The build system defaults to the dev environment which uses `environment.ts`, but if you do
// `ng build --env=prod` then `environment.prod.ts` will be used instead.
// The list of which env maps to which file can be found in `.angular-cli.json`.




import {IEnvironment} from './environment.types';
import {CONTEXTS_LIST, LOG_IN_DATA} from './environment.const';

export const environment: IEnvironment = {
    production: true,
    loginThroughDataBase: true,
    isAlertPollingDisabled: true,
    alertPollIntervalInMin: 200,
    isAwsS3Used: true,
    contextsList: CONTEXTS_LIST,
    isWebSocketEnabled: false,
    baseUrl: {
        frontEnd: {
            main: 'http://localhost:3000/'
        },
        backend: {
            main: 'http://localhost:3000/'
        },
        notificationManager: {
            host: 'http://127.0.0.1',
            port: 3000
        },
        ageops: {
            main: 'https://ageops.net/'
        }
    }
};
