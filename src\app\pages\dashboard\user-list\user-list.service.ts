import { Injectable } from '@angular/core';
import { IComplaintList, ITableHeader } from './user-list.types';
import { BehaviorSubject, Observable } from 'rxjs';
import { HttpService } from '../../../services/http.service';
import { AuthService } from '../../../services/auth.service';

@Injectable()

export class UserListService {
  private _slugLabelMap: any = {};
  private _slugClassMap: any = {};
  private _slugWidthMultiplicationFactorMap: any = {};
  data = new BehaviorSubject<IComplaintList>(undefined);
  index = 1;

  constructor(private _httpService: HttpService,
    public authService: AuthService) {
  }

  list ({pageIndex, pageSize}): Observable<any> {
    return this._httpService.get(`api/users?pageIndex=${pageIndex}&pageSize=${pageSize}`, {
      observe: 'response'
    });
  }

  entities (): Observable<any> {
    return this._httpService.getExternal(`https://cdm.ageops.af/api/specific/sectorAndProcurementEntity/procurementEntity`, {
      observe: 'response'
    });
  }

  sectors (): Observable<any> {
    return this._httpService.getExternal(`https://cdm.ageops.af/api/specific/sectorAndProcurementEntity/sector`, {
      observe: 'response'
    });
  }

  roles (): Observable<any> {
    return this._httpService.get(`api/roles`, {
      observe: 'response'
    });
  }
  systems (): Observable<any> {
    return this._httpService.get(`api/systems`, {
      observe: 'response'
    });
  }

  store (data, systemSlug): Observable<any> {

    return this._httpService._post(`api/users/create-${systemSlug}-user`, data, {
      observe: 'response'
    });
  }

  storeUser (data): Observable<any> {
    return this._httpService._post(`api/users/create-user`, data, {
      observe: 'response'
    });
  }

  show (): Observable<IComplaintList> {
    return this.data;

  }

  update (data, userRoleId): Observable<IComplaintList> {
    return this._httpService._put(`api/users?user_id=${userRoleId}`, data, {
      observe: 'response'
    });
  }

  getClass (slug: string) {
    if (!this._slugClassMap.init_marker) {
      CONTRACT_LIST_TITLES.forEach((current: ITableHeader) => {
        this._slugClassMap[current.slug] = `widthUnit${current.width_multiplication_factor}`;
      });
    }
    return this._slugClassMap[slug];
  }

  getLabel (slug: string) {
    if (!this._slugLabelMap.init_marker) {
      CONTRACT_LIST_TITLES.forEach((current: ITableHeader) => {
        this._slugLabelMap[current.slug] = current.label_da;
      });
    }
    return this._slugLabelMap[slug];
  }

  getWidthMultiplicationFactor (slug: string) {
    if (!this._slugWidthMultiplicationFactorMap.init_marker) {
      CONTRACT_LIST_TITLES.forEach((current: ITableHeader) => {
        this._slugWidthMultiplicationFactorMap[current.slug] = current.width_multiplication_factor;
      });
    }
    return this._slugWidthMultiplicationFactorMap[slug];
  }

  getDisplayedColumns (userRole: string, loggedInUserRole: string) {
    return [
      'options',
      'user_name',
      'email',
      'phone_number'
    ];
  }
}

export const CONTRACT_LIST_TITLES: ITableHeader[] = [

];
