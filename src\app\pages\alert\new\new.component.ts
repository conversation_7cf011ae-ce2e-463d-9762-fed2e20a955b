import {Component, OnInit} from '@angular/core';
import {MatDialog} from '@angular/material';
import {NPA_COMMON_DIALOG_WIDTH} from '../../shared/consts/sizes';
import {IAlertListItem} from '../alert.types';
import {NpaDatabase, NpaDataSource} from '../../shared/classes/npa-table';
import {AlertViewDialogComponent} from '../alert-view-dialog/alert-view-dialog.component';
import {AlertService} from '../alert.service';
import {HttpErrorResponse} from '@angular/common/http';
import {DropDownsService} from '../../../services/dropDowns.service';
import {IDropdown} from '../../shared/types/general.types';
import {Location} from '@angular/common';
import {Subscription} from 'rxjs';
import {NotificationsService} from '../../shared/services/notifications.service';
import {TranslateService} from '../../shared/services/translate.service';

@Component({
    selector: 'npa-new',
    templateUrl: './new.component.html',
    styleUrls: ['./new.component.styl']
})
export class NewComponent implements OnInit {

    displayedColumns = ['id', 'status', 'created_at', 'contents', 'options'];
    dataSource: NpaDataSource;
    database: NpaDatabase;
    archivedConfirmation;
    alertSubjectSubscription: Subscription;

    constructor(public dialog: MatDialog,
                private _alertService: AlertService,
                private _dropDownsService: DropDownsService,
                private _location: Location,
                private _notificationsService: NotificationsService,
                private tranlsate: TranslateService
    ) {
    }

    ngOnInit() {

        // this.load();
        //
        // this.alertSubjectSubscription = this._alertService.alertSubject.subscribe((alert: IAlert) => {
        //     if (!!alert) {
        //         this.load();
        //     }
        // });
    }

    private isThisCurrentPage(): boolean {
        return (this._location.path() + '').indexOf('alert/new') > 0;
    }

    private load() {
        this._alertService.index('working').subscribe((data: IAlertListItem[]) => {
            data.forEach((current: IAlertListItem) => {
                const matchedStatus: IDropdown = this._dropDownsService.get(
                    'alertsAndRemarks/alertStatus',
                    current.status_id
                );
                current.status_name_da = matchedStatus.name_da;
                current.status_slug = matchedStatus.slug;
            });
            this.database = new NpaDatabase(data);
            if (!this._alertService.hasDatabaseUnreadAlertItem(this.database)) {
                this._alertService.alertSubject.next(null);
            }
            this.dataSource = new NpaDataSource(this.database);
        }, (error: HttpErrorResponse) => {
            console.error(error);
        });
    }

    openViewDialog(alert: IAlertListItem): void {
        const dialogRef = this.dialog.open(AlertViewDialogComponent, {
            width: NPA_COMMON_DIALOG_WIDTH,
            data: alert.instance_id
        });

        dialogRef.afterClosed().subscribe((result: IAlertListItem) => {
            this.database.edit(result);
            if (!this._alertService.hasDatabaseUnreadAlertItem(this.database)) {
                this._alertService.alertSubject.next(null);
            }
        });
    }

}
