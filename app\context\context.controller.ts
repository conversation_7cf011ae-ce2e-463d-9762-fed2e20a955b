import {Request, Response} from 'express';
import {InstanceService} from '../services/instance.service';
import {createQueryBuilder} from 'typeorm';


export class ContextController {
    public static instance: ContextController;

    public static getInstance(): ContextController {
        return InstanceService.getInstance(ContextController);

    }

    public async getRoleDashboards(req: Request, res: Response) {

        try {
            const data = <any>await createQueryBuilder('ContextAccess')
                .select('ContextAccess.id')
                .leftJoin('ContextAccess.role', 'role')
                .leftJoin('ContextAccess.context', 'context')
                .leftJoin('role.userRole', 'userRole')
                .where(`userRole.id=:user_role_id`, {user_role_id: req.query['user_role_id']})
                .andWhere(`context.is_dashboard=true`)
                .andWhere(`ContextAccess.is_enabled=true`)
                .addSelect('context')
                .getMany();
            return res.status(200).json(data);
        } catch (e) {
            console.log(e);
            return res.status(500).json(e);
        }
    }

    public async getRoleNonDashboards(req: Request, res: Response) {
        try {
            if (!req.query['user_role_id']) {
                throw new Error('`user_role_id` is required!');
            }
            const data = <any>await createQueryBuilder('ContextAccess')
                .select('ContextAccess.id')
                .leftJoin('ContextAccess.role', 'role')
                .leftJoin('ContextAccess.context', 'context')
                .leftJoin('role.userRole', 'userRole')
                .where(`userRole.id=:user_role_id`, {user_role_id: req.query['user_role_id']})
                .andWhere(`context.is_dashboard=false`)
                .andWhere(`ContextAccess.is_enabled=true`)
                .addSelect('context')
                .getMany();
            return res.status(200).json(data);
        } catch (e) {
            console.log(e);
            return res.status(500).json(e.message);
        }
    }

    public async getPermissions(req: Request, res: Response) {
        try {
            const contextSlug = req.params['context'];

            if (contextSlug === undefined || contextSlug === null) {
                throw new Error('`context_slug` is not supplied.');
            }

            if (res.locals.user_role_id === undefined || res.locals.user_role_id === null) {
                throw new Error('`user_role_id` is not specified');
            }

            const contextSlugExists = <any>await createQueryBuilder('Context')
                .select('Context.id')
                .where(`Context.slug= :slug`, {slug: contextSlug})
                .getCount();

            if (contextSlugExists === 0) {
                throw new Error('The specified context does not exist');
            }

            const contextAccess = <any>await createQueryBuilder('ContextAccess')
                .innerJoin('ContextAccess.operation', 'operation')
                .innerJoin('ContextAccess.role', 'role')
                .innerJoin('ContextAccess.context', 'context')
                .leftJoin('role.userRole', 'userRole')
                .where(`context.slug= :context`, {context: contextSlug})
                .andWhere(`ContextAccess.is_enabled=true`)
                .andWhere(`userRole.id =:user_role_id`, {user_role_id: res.locals.user_role_id})
                .addSelect('operation.slug')
                .getMany();

            if (contextAccess === undefined || contextAccess === null) {
                return res.status(403).json({});
            }
            return res.status(200).json(contextAccess);
        } catch (e) {
            console.log(e);
            return res.status(400).json(e.message || e);
        }
    }

    public async getPermissionMap(req: Request, res: Response) {
        try {
            const userRoleId = req.query['user_role_id'];
            if (userRoleId === undefined || userRoleId === null) {
                throw new Error('`user_role_id` is not specified');
            }

            const contextAccess = <any>await createQueryBuilder('Context')
                .innerJoinAndSelect('Context.contextAccesses', 'contextAccess')
                .innerJoin('contextAccess.operation', 'operation')
                .innerJoin('contextAccess.role', 'role')
                .leftJoin('role.userRole', 'userRole')
                .andWhere(`contextAccess.is_enabled=true`)
                .andWhere(`userRole.id =:user_role_id`, {user_role_id: userRoleId})
                .addSelect('operation.slug')
                .getMany();

            if (contextAccess === undefined || contextAccess === null) {
                return res.status(403).json({});
            }
            return res.status(200).json(contextAccess);
        } catch (e) {
            console.log(e);
            return res.status(400).json(e.message || e);
        }
    }
}
