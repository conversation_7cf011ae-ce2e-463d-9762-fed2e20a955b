## usm

Afghanistan's Administrative Review System
# for creating user run the fowllowing api in postman with post request
// https://usm.ageops.af/api/users/create-appms-user
# for ACPMS
//https://usm.ageops.af/api/users/create-acpms-user

// in Headers Section in the key write Authorization and in value copy and paste value from usm.ageops.af request when you login as admin user.

and in the body write the following or as per user requirement.

{
 "name": "appms-procurement-plan-manager",
 "last_name": "plan",
 "username": "acpmsplan",
 "email": "<EMAIL>",
 "phone_number": "93771536415",
 "api_password": "AABBCC!!@@##112233",
 "status_slug": "active",
 "role_slug": "appms-procurement-plan-manager",
 "role_data": {
     "procurement_entities": [
     		{ "procurement_entity_id" : 145 }
     	],
		"sectors":[
			{ "sector_id" : 6 }
		]
 }
}

# after buid copy public sample into dist