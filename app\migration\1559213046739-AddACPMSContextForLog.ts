import {MigrationInterface, QueryRunner} from "typeorm";

export class AddACPMSContextForLog1559213046739 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<any> {
        await queryRunner.query(`
            INSERT INTO
              contexts (slug, url, description, system_id, is_dashboard)
            VALUES
            ('system-management','','', 2, false)
            `);
    }

    public async down(queryRunner: QueryRunner): Promise<any> {
    }

}
