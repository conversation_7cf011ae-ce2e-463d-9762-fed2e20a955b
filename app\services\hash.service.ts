import * as bcrypt from 'bcrypt';
import * as env from 'dotenv';
import * as path from 'path';

env.load({path: path.resolve(__dirname, '../.env')});

export class HashService {

    public static async verify(pass, hashedPass) {
        return await bcrypt.compare(pass, hashedPass);
    }

    public static async generate(pass: string) {
        return await bcrypt.hash(pass, +process.env.B_CRYPT_SALT_ROUNDS);
    }

}
export function generatePassword(length = 10) {
    const charset = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*()';
    let retVal = '';
    for (let i = 0, n = charset.length; i < length; ++i) {
        retVal += charset.charAt(Math.floor(Math.random() * n));
    }
    return retVal;
}
