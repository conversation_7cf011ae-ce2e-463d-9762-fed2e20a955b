import {NgModule} from '@angular/core';
import {CommonModule} from '@angular/common';
import {UserGuideComponent} from './user-guide.component';
import {RouterModule} from '@angular/router';
import {USER_GUIDE_ROUTES} from './user-guide-routes';
import {SystemInformationComponent} from './system-information/system-information.component';
import {ChangeLogComponent} from './change-log/change-log.component';
import {ContactInformationComponent} from './contact-information/contact-information.component';
import {UserManualComponent} from './user-manual/user-manual.component';
import {MatButtonModule, MatCardModule, MatIconModule} from '@angular/material';
import {SharedModule} from '../shared/shared.module';
import {FlexLayoutModule} from '@angular/flex-layout';
import {ResetSystemComponent} from './reset-system/reset-system.component';

@NgModule({
    imports: [
        CommonModule,
        MatCardModule,
        MatButtonModule,
        SharedModule,
        MatIconModule,
        FlexLayoutModule,
        RouterModule.forChild(USER_GUIDE_ROUTES)
    ],
    declarations: [
        UserGuideComponent,
        SystemInformationComponent,
        ChangeLogComponent,
        ContactInformationComponent,
        UserManualComponent,
        ResetSystemComponent,
    ]
})
export class UserGuideModule {
}
