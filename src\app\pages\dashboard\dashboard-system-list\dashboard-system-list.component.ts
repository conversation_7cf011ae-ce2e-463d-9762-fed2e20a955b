import {Component, OnInit, ViewChild} from '@angular/core';
import {ConfirmationAndJustificationService} from '../../shared/services/confirmation-and-justification.service';
import {HttpService} from '../../../services/http.service';
import {AddEditDialogComponent} from '../user-list/add-edit-dialog/add-edit-dialog.component';
import {ActivatedRoute, Router} from '@angular/router';
import {MatDialog, MatPaginator, MatPaginatorIntl} from '@angular/material';
import {FormBuilder, FormGroup} from '@angular/forms';
import {formatPaginator, NpaDatabase, NpaDataSource} from '../../shared/classes/npa-table';
import {AuthService} from '../../../services/auth.service';
import {FormService} from '../../shared/services/form.service';
import {NotificationsService} from '../../shared/services/notifications.service';
import {TranslateService} from '../../shared/services/translate.service';
import {NPA_COMMON_DIALOG_WIDTH} from '../../shared/consts/sizes';
import {ComplaintService} from '../../../services/complaint.service';
import {DashboardSystemListService} from './dashboard-system-list.service';
import {ViewDialogComponent} from './view-dialog/view-dialog.component';
import {HttpErrorResponse, HttpResponse} from '@angular/common/http';
import {NPA_ALERTS} from '../../shared/consts/messages';


@Component({
  selector: 'npa-dashboard-system-list',
  templateUrl: './dashboard-system-list.component.html',
  styleUrls: ['./dashboard-system-list.component.styl']
})

export class DashboardSystemListComponent implements OnInit {

  displayedData = [];
  dataSource: NpaDataSource;
  database: NpaDatabase;
  form: FormGroup;
  lang: string;
  referenceTitles: string[] = [];
  chosenTitles: string[] = [];
  @ViewChild(MatPaginator) paginator: MatPaginator;
  dir: string;

  constructor(private formBuilder: FormBuilder,
              public authService: AuthService,
              public formService: FormService,
              public dashboardSystemListService: DashboardSystemListService,
              private activatedRoute: ActivatedRoute,
              private router: Router,
              private dialog: MatDialog,
              private _matPaginatorIntl: MatPaginatorIntl,
              private complaintService: ComplaintService,
              private notificationsService: NotificationsService,
              private _httpService: HttpService,
              private confirmationAndJustificationService: ConfirmationAndJustificationService,
              private translateService: TranslateService) {
  }

  ngOnInit() {
    this.lang = this.translateService.lang;
    this.dir = this.translateService.dir;
    // this.initForm();
    this.notificationsService.dismissLoading();
    this.referenceTitles = this.dashboardSystemListService.getDisplayedColumns(
      this.authService.user.value.role.slug,
      this.authService.loggedInUser.role.slug
    );

    this.chosenTitles = this.referenceTitles.slice();

    // formatPaginator(this._matPaginatorIntl);

    // setTimeout(() => {
    //   this.paginator.pageSize = 5;
    //   this.paginator.pageSizeOptions = [5, 10, 50, 100];
    //   this.paginator.page.subscribe(() => {
    //   });
    // }, 0);

    this._loadList();

    // this.paginator.page.subscribe(() => {
    //   this._loadList();
    // });


  }

  // initForm() {
  //   this.form = this.formBuilder.group({
  //     user_identification_number: undefined,
  //     project_name: undefined,
  //   });
  // }

  // selectComplaint(data: IComplaintList) {
  //   this.complaintService.complaintInformation
  //     .next(
  //       {
  //         id: data.id,
  //         user_identification_number: data.user_identification_number,
  //         isComplaintSelected: true
  //       }
  //     );
  //   this.authService.selectedComplaintUrl = this.authService.defaultUserLoggedInUrl +
  //     `dashboard/user-list/${data.user_identification_number}`;
  //   this.router.navigate(
  //     [`${encodeURIComponent(data.user_identification_number)}/reports/generate-and-download-reports`],
  //     {relativeTo: this.activatedRoute}
  //   );
  // }

  private _loadList() {
    this.notificationsService.startLoading();
    this.dashboardSystemListService.index().subscribe(
      (data: HttpResponse<any>) => {
        let i;
        for (i = 0; i < data.body.length; i++) {
          this.displayedData[i] = data.body[i].role.system;
        }
        // this.paginator.length = +data.headers.get('x-pagination-size');
        this.database = new NpaDatabase(this.displayedData);
        this.dataSource = new NpaDataSource(this.database);
        if (data.body.length === 0) {
          this.notificationsService.info('هیچ سیستمی یافت نشد.');
        } else {
          this.notificationsService.dismissLoading();
        }
      },
      (error: HttpErrorResponse) => {
        this.notificationsService.error(NPA_ALERTS.ERROR);
        console.error(error);
      }
    );
  }

  getWidth() {
    let total = 0;
    this.chosenTitles.forEach((current: string) => {
      total += this.dashboardSystemListService.getWidthMultiplicationFactor(current);
    });
    return (total * 150) + 'px';
  }

  openAddEditDialog(data = {}) {
    const dialogRef = this.dialog.open(AddEditDialogComponent, {
      width: NPA_COMMON_DIALOG_WIDTH,
      data: data,
      disableClose: true,
    });
    dialogRef.afterClosed().subscribe(result => {
      if (Object.keys(result).length === 0) {
        return;
      }
      if (this.database.find(result.id)) {
        this.database.edit(result);
        return;
      }
      this.database.add(result);
    });
  }

  openViewDialog(data = {}) {
    this.dialog.open(ViewDialogComponent, {
      width: NPA_COMMON_DIALOG_WIDTH,
      data: data,
      disableClose: true,
    });
  }

}
