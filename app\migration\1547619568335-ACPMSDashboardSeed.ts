import {MigrationInterface, QueryRunner} from 'typeorm';

export class ACPMSDashboardSeed1547619568335 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<any> {
        await queryRunner.query(`
            INSERT INTO
              contexts (slug, url, description, system_id, is_dashboard)
            VALUES
            ('general-information-approved-contracts-count-widget','','', 2, true),
            ('general-information-not-approved-contracts-count-widget','','', 2, true),
            ('contracts-count-widget','','', 2, true),
            ('published-contracts-count-widget','','', 2, true),
            ('details-approved-contracts-total-value-widget','','', 2, true),
            ('contracts-total-payments-widget','','', 2, true),
            ('contracts-total-physical-progress-value-widget','','', 2, true),
            ('above-threshold-contracts-count-widget','','', 2, true),
            ('below-threshold-contracts-count-widget','','', 2, true),
            ('cpmd-specialists-remarks-count-widget','','', 2, true),
            ('contract-status-completed-count-widget','','', 2, true),
            ('details-approved-amended-contracts-count-widget','','', 2, true),
            ('contract-status-cancelled-count-widget','','', 2, true),
            ('analyzed-contracts-count-widget','','', 2, true),
            ('not-analyzed-contracts-count-widget','','', 2, true),
            ('contract-analysis-published-by-cpmd-managers-count-widget','','', 2, true),
            ('contract-analysis-prepare-to-publish-count-widget','','', 2, true),
            ('published-documents-percentage-widget','','', 2, true),
            ('change-request-contracts-count-widget','','', 2, true),
            ('contract-count-and-value-based-on-procurement-type','','', 2, true),
            ('published-and-unpublished-contracts-count','','', 2, true),
            ('published-and-signed-contracts-count','','', 2, true),
            ('contracts-related-to-contract-manager-count','','', 2, true),
            ('contracts-based-on-threshold-and-procurement-type-count','','', 2, true),
            ('published-contracts-based-on-threshold-count','','', 2, true),
            ('planned-and-actual-uploaded-documents-count','','', 2, true),
            ('consultancy-services-planned-and-actual-uploaded-documents-count','','', 2, true),
            ('contracts-based-on-procurement-method-count','','', 2, true),
            ('cpmd-manager-contracts-based-on-procurement-method-count','','', 2, true),
            ('contracts-based-on-challenges-count','','', 2, true),
            ('contracts-based-on-selection-method-count','','', 2, true),
            ('contracts-based-on-status-count','','', 2, true),
            ('contracts-value-based-on-status','','', 2, true),
            ('payments-percentage-in-comparison-with-plan','','', 2, true),
            ('complete-contracts-payments-value-based-on-procurement-types','','', 2, true),
            ('work-in-progress-contracts-payments-value','','', 2, true),
            ('work-in-progress-contracts-physical-progress-value','','', 2, true),
            ('contract-manager-contracts-time-and-cost-amendments-percentage-vs-origin-time-and-cost','','', 2, true),
            ('award-authority-contracts-time-and-cost-amendments-percentage-vs-origin-time-and-cost','','', 2, true),
            ('contracts-amendments-types-count-based-on-procurement-type','','', 2, true),
            ('contracts-implementations-challenges-count','','', 2, true),
            ('contracts-value-based-on-donor','','', 2, true),
            ('contracts-count-based-on-donor','','', 2, true),
            ('provinces-contracts-value-count-and-status','','', 2, true),
            ('transferred-contracts-to-contract-manager-status-count','','', 2, true),
            ('contract-manager-assigned-contracts-count','','', 2, true),
            ('contract-managers-assigned-contracts-status-percentage','','', 2, true),
            ('contract-managers-assigned-contracts-count-per-pes','','', 2, true),
            ('contract-manager-contracts-amendments-count-by-type','','', 2, true),
            ('contracts-amendments-count-by-type','','', 2, true),
            ('department-contracts-status-count','','', 2, true),
            ('department-specialists-analyzed-contracts-count','','', 2, true),
            ('pes-new-contracts-vs-transferred-contracts-count','','', 2, true),
            ('pes-contracts-value-percentage-count','','', 2, true),
            ('vendors-signed-contracts-value-and-number-count','','', 2, true),
            ('complete-contracts-payments-percentage','','', 2, true),
            ('specialist-contracts-analysis-status-count-in-pe','','', 2, true),
            ('specialist-contracts-analysis-status-count','','', 2, true),
            ('specialist-analysed-contracts-count','','', 2, true)`);
    }

    public async down(queryRunner: QueryRunner): Promise<any> {
    }

}
