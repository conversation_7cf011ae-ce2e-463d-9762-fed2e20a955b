import {Injectable} from '@angular/core';
import {MatDialog} from '@angular/material';


import {IConfirmationAndJustificationDialogInput, IConfirmationAndJustificationDialogOutput} from '../types/confirmation-and-justification.types';
import {ConfirmationAndJustificationDialogComponent} from '../confirmation-and-justification-dialog/confirmation-and-justification-dialog.component';
import {AuthService} from '../../../services/auth.service';
import {NotificationsService} from './notifications.service';
import {NPA_ALERTS} from '../consts/messages';
import {map} from 'rxjs/operators';

@Injectable()

export class ConfirmationAndJustificationService {

    constructor(private _dialog: MatDialog,
                public authService: AuthService,
                private _notificationsService: NotificationsService) {}

    openDialog(data: IConfirmationAndJustificationDialogInput): Promise<IConfirmationAndJustificationDialogOutput> {

        const dialogRef = this._dialog.open(ConfirmationAndJustificationDialogComponent, {
            width: '400px',
            height: '260px',
            data: data,
            disableClose: true,
        });

        return dialogRef
            .afterClosed()
            .pipe(map((result) => {
                return result;
            }))
            .toPromise()
            .catch(() => {
                this._notificationsService.error(NPA_ALERTS.ERROR);
            });

    }
}
