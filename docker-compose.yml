version: "3"

services:
    node:
        image: "node:14.21.1"
        user: "node"
        container_name: usm-node
        working_dir: /home/<USER>
        ports:
            - 5000:4200
            - 5001:3000
        volumes:
            - ./:/home/<USER>
        command: bash -c "yarn && yarn server"
        networks:
            - usm

    database:
        image: "mariadb"
        container_name: usm-sql
        command: --default-authentication-plugin=mysql_native_password
        environment:
            MARIADB_ALLOW_EMPTY_ROOT_PASSWORD: 'YES'
            MARIADB_ROOT_PASSWORD: ${TYPEORM_PASSWORD}
            MARIADB_PASSWORD: ${TYPEORM_PASSWORD}
            MARIADB_USER: ${TYPEORM_USERNAME}
            MARIADB_DATABASE: ${TYPEORM_DATABASE}
        ports:
            - 5002:3306
        volumes:
            - db-data:/var/lib/mysql
            - ./database:/var/db
        networks:
            - usm
        env_file:
            - .env

volumes:
    db-data:

networks:
    usm:
        driver: bridge

