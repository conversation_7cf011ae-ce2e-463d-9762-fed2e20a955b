<div class="right-sidebar" [ngClass]="isSidebarUtilitiesClose ? 'close' : 'open'">
    <div class="header" fxLayout="row" fxLayoutAlign="start center">
        <div class="title">{{'REMARKS' | translate}}</div>
        <button mat-icon-button  (click)="openAddDialog()">
            <mat-icon>add</mat-icon>
        </button>
        <button mat-icon-button>
            <mat-icon>refresh</mat-icon>
        </button>
    </div>
    <div class="remarks-wrapper">

        <mat-accordion>
            <mat-expansion-panel>
                <mat-expansion-panel-header fxLayout="row">
                    <mat-panel-title fxFlex="15">
                    </mat-panel-title>
                    <mat-panel-description fxFlex="85">
                    </mat-panel-description>
                </mat-expansion-panel-header>

                <div class="mini-container">
                    <div class="npa-remark-title1">اطلاعات کلی:</div>
                    <npa-view-element>
                        <div class="npa-label">عنوان ملاحظه:</div>
                        <div class="npa-value"></div>
                    </npa-view-element>
                    <npa-view-element>
                        <div class="npa-label">عنوان پروژه:</div>
                        <div class="npa-value"></div>
                    </npa-view-element>
                    <npa-view-element>
                        <div class="npa-label">نمبر تشخیصیه:</div>
                        <div class="npa-value" lang="en"></div>
                    </npa-view-element>
                    <npa-view-element>
                        <div class="npa-label">آی دی پروژه:</div>
                        <div class="npa-value"></div>
                    </npa-view-element>
                    <npa-view-element>
                        <div class="npa-label">نوعیت:</div>
                        <div class="npa-value"></div>
                    </npa-view-element>
                </div>
                <div class="mini-container">
                    <div class="npa-remark-title1">دریافت کننده ها:</div>
                    <npa-view-element>
                        <div class="npa-label"></div>
                        <div class="npa-value"></div>
                    </npa-view-element>
                </div>

                <div class="mini-container">
                    <div class="npa-remark-title1">پیام ها:</div>
                    <div class="remark">
                        <div class="summary">
                        </div>
                        <div class="content"></div>
                    </div>
                </div>

                <div class="actions" fxLayout="row" fxLayoutAlign="space-between center">
                    <a mat-icon-button color="primary">
                        <mat-icon>navigate_next</mat-icon>
                    </a>
                    <button mat-icon-button>
                        <mat-icon>reply</mat-icon>
                    </button>
                    <button mat-icon-button>
                        <mat-icon>archive</mat-icon>
                    </button>
                </div>
            </mat-expansion-panel>
            <div class="spacer"></div>
        </mat-accordion>
    </div>
</div>
