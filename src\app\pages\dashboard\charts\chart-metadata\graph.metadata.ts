export const SIMPLE_NON_RIBBON = {
    title: {
        text: '',
        top: 'top',
        left: 'right',
        textStyle: {
            fontFamily: 'XB Niloofar',
            color: '#333',
            fontWeight: 'normal',
            fontSize: 16,
            lineHeight: 20,
        }
    },
    color: ['#595b5d', '#e2d0ad', '#b3b3ff', '#000066'],
    tooltip: {},
    toolbox: {
        show: true,
        left: 'right',
        top: 'bottom',
        feature: {
            mark: {
                show: true,
                title: {
                    mark: 'Add Guide',
                    markUndo: 'Remove Guide',
                    markClear: 'Remove all Guide',
                },
            },
            dataView: {
                show: true,
                readOnly: false,
                optionToContent: null,
                contentToOption: null,
                title: 'Data View',
                lang: ['Data View', 'Shutdown', 'Refresh'],
                buttonColor: '#01579B'
            },
            restore: {
                show: true,
                title: 'Reset'
            },
            saveAsImage: {
                show: true,
                title: 'Save As Image'
            }
        }
    },
    legend: {
        left: 'left',
        align: 'right',
        bottom: 0,
        orient: 'vertical',
        textStyle: {
            fontFamily: 'XB Niloofar',
        },
        data: [],
        animation: true,
        animationDurationUpdate: 800
    },
    animationDuration: 1500,
    animationEasingUpdate: 'quinticInOut',
    series: []
};
