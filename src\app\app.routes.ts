import {RouterModule, Routes} from '@angular/router';
import {LoginComponent} from './login/login.component';
import {RecoverPasswordComponent} from './recover-password/recover-password.component';
import {RecoverPasswordFormComponent} from './recover-password-form/recover-password-form.component';
import {UnauthorizedAccessComponent} from './unauthorized-access/unauthorized-access.component';
import {VendorRegistrationFormComponent} from './vendor-registration-form/vendor-registration-form.component';
import {PageNotFoundComponent} from './page-not-found/page-not-found.component';
import {AppComponent} from './app.component';

export const appComponents = [
    PageNotFoundComponent,
    AppComponent,
    LoginComponent,
    RecoverPasswordComponent,
    RecoverPasswordFormComponent,
    UnauthorizedAccessComponent,
    VendorRegistrationFormComponent
];
const routes: Routes = [
    {
        path: ':lang/login',
        component: LoginComponent,
    },
    {
        path: ':lang/auth/recover-password',
        component: RecoverPasswordComponent,
    },
    {
        path: ':lang/auth/recover-password-form',
        component: RecoverPasswordFormComponent,
    },
    {
        path: ':lang/unauthorized-access',
        component: UnauthorizedAccessComponent,
    },
    {
        path: ':lang/vendor-registration-form',
        component: VendorRegistrationFormComponent,
    },
    {
        path: ':lang',
        loadChildren: 'app/pages/pages.module#PagesModule',
    },
    {
        path: ':lang/page-not-found',
        component: PageNotFoundComponent
    },
];

export const appRoutes = RouterModule.forRoot(routes);
