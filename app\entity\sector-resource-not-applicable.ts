import {BaseEntity, Column, <PERSON>tity, <PERSON><PERSON><PERSON><PERSON>umn, ManyToOne, PrimaryGeneratedColumn} from 'typeorm';
import {UserRoleSector} from './user-role-sector';

@Entity('sector_resource_not_applicables')

export class SectorResourceNotApplicable extends BaseEntity implements ISectorResourceNotApplicable {

    @PrimaryGeneratedColumn()
    id: number;

    @Column()
    user_role_sector_id: number;

    @ManyToOne(type => UserRoleSector, userRoleSector => userRoleSector.id)
    @JoinColumn({name: 'user_role_sector_id'})
    userRoleSector: UserRoleSector;
}

export interface ISectorResourceNotApplicable {
    id: number;
    user_role_sector_id: number;
    userRoleSector: any | UserRoleSector;
}



