@import url('https://fonts.googleapis.com/css?family=Roboto');
@import './definitions.styl'

html, body
  margin 0
  padding 0

*[lang='prs']
  font-family 'XB Niloofar' !important
  direction rtl
  text-align right

[lang='ps']
  font-family 'XB Niloofar' !important
  direction rtl
  text-align right

[lang='ps'] *:not(mat-icon),
[lang='prs'] *:not(mat-icon)
  font-family 'XB Niloofar'

  .element-wrapper
    *
      text-align right

  mat-dialog-container
    direction rtl !important

  .mat-paginator-navigation-previous.mat-icon-button
    transform: rotate(180deg)

  .mat-paginator-navigation-next.mat-icon-button
    transform: rotate(180deg)

.mat-card-title
  font-weight bold
  padding 0 0 4px
  border-bottom 1px solid colorLightGrayAlpha
  margin-bottom 8px


.mat-card-header-text
  width 100%
  margin 0 0 !important

*[lang='en']
  direction ltr
  font-family Roboto, Helvetica Neue Light, Helvetica Neue, Helvetica, Arial, Lucida Grande, sans-serif !important
  font-weight 400

  mat-form-field
    text-align left !important

.mat-card-header
  display block !important

mat-icon font
  font-family unset !important

.add-row-button
  left 30px !important

.add-row-button
  position fixed !important
  bottom 20px !important

  mat-icon
    display flex
    transform: rotate(-90deg);
    transition all 0.2s ease-in-out
    z-index 5

  &:hover mat-icon
    transform: rotate(90deg);
    transition all 0.2s ease-in-out

[mat-mini-fab]
  padding-right 12px
  padding-left 12px

.npa-number
  direction ltr
  text-align right
  display inline-block

mat-icon
  direction rtl !important

.no-select
  -webkit-touch-callout none
  -webkit-user-select none
  -khtml-user-select none
  -moz-user-select none
  -ms-user-select none
  user-select none

mat-icon.size-16
  height 16px
  width 16px
  font-size 16px

// Override default style of Jalali dataTimePicker
.dp-current-location-btn
  display none !important

.dp-input-container
  overflow hidden !important

.dp-picker-input
  border 0 !important
  font-size 12px !important
  background-color unset !important

.dp-popup.dp-material
  left 0 !important
  top 3px !important
  box-shadow 0 6px 6px #777 !important

mat-form-field
  text-align right !important
  padding 0 5px 0 5px !important

mat-option
  text-align right !important
  padding 0 5px 0 5px !important

.splitter
  flex: 1 1 auto

//mat-card->forms submit and reset button general position style
mat-card
  form
    margin-bottom -20px !important

    button[type='submit'], button.button-card-action
      margin-right 18px !important

npa-root > .scrollableContainer
  background-color colorContentBackground

.contents
  padding sizeContentItemsSpacing
  padding-top 0

  *
    mat-card, mat-expansion-panel
      margin sizeContentItemsSpacing

mat-spinner
  width 50px !important
  height 50px !important

  svg
    width 50px !important
    height 50px !important

.dialog-close-button
  float left !important

mat-error
  font-size 10.5px !important

.whiteSpaceNoWrap
  white-space nowrap

.margin-bottom-20px
  margin-bottom 20px

[fxLayout*='row'][not-in-card]
  margin-top 16px
  margin-right 12px
  margin-left 12px
  margin-bottom -8px

.margin-left-8px
  margin-left 8px

.margin-right-8px
  margin-right 8px

.advanced-pie
  float right !important
  direction ltr !important

::-webkit-scrollbar
  height 5px
  width 5px
  transition all 1s linear

::-webkit-scrollbar-thumb
  background colorDark
  border-radius: 10px

::-webkit-scrollbar-thumb:hover
  background-color #000

.mat-paginator-container
  padding-right 50px !important
  padding-left 50px !important

.mat-paginator-page-size
  .mat-select-trigger
    font-size 13px !important

.mat-dialog-content
  .dateTimePicker
    margin-top -5px !important

[mat-button], [mat-raised-button]
  background-color colorBasic

.exceptional-mat-button-wrapper
  [mat-button]
    background-color #fff
    text-align right

.mat-tab-group.mat-primary
.mat-tab-label:focus,
.mat-tab-group.mat-primary .mat-tab-link:focus,
.mat-tab-nav-bar.mat-primary .mat-tab-label:focus,
.mat-tab-nav-bar.mat-primary .mat-tab-link:focus
  background-color colorBasic

[mat-tab-nav-bar][not-in-card]
  border-bottom none
  padding 0 12px

  .mat-tab-link
    min-width 48px

  .mat-tab-link:focus
    background-color transparent

[npa-custom-nav]
  :not(.active)
    mat-icon
      color colorGray

  .active
    background-color colorLightGrayAlpha

    mat-icon
      color inherit

.main-page-bottom-border
  font-weight: bold;
  padding-bottom: 8px;
  border-bottom: 1px solid rgba(92, 118, 128, 1);
  margin-bottom 8px

.sub-page-bottom-border
  padding-bottom: 3px;
  border-bottom: 2px solid rgba(92, 118, 128, 1);

::ng-deep .npa-label
  margin-top 0 !important

.npa-line-separator
  border-bottom 1px dotted colorTextLighter

.npa-title1
  border-top 3px double colorTextLighter
  border-bottom 1px dotted colorTextLighter

.npa-title2
  border-top 1px solid colorTextLighter
  border-bottom 1px dotted colorTextLighter

::ng-deep .npa-label
  margin-top 0 !important

.npa-width-20-percent
  width 20%

table.report
  width 100%
  border-collapse collapse

  td, th
    color rgba(0, 0, 0, 0.87)
    font-weight normal
    text-align right
    border-bottom 1px solid rgba(0, 0, 0, 0.12)
    height 50px

.mat-input-element
  font-size 13px !important

mat-dialog-container npa-view-semi-dialog mat-card
  box-shadow: none !important;

mat-dialog-container npa-list mat-card
  box-shadow: none !important;

.tooltipCustomStyles
  white-space pre !important
  max-width 1000px !important

mat-table
  .mat-checkbox-ripple
    top: 40px !important

fieldset
  margin-bottom 24px;
  border-radius 4px;
  border-width 1px;
  border-color rgba(210, 208, 208, 0.7);
  padding-block-end 0;
  border-style solid;

.add-button
  mat-icon
    display flex
    transform: rotate(-90deg);
    transition all 0.2s ease-in-out
    z-index 5

  &:hover mat-icon
    transform: rotate(90deg);
    transition all 0.2s ease-in-out


.add-row-button-container
  margin-bottom -19px

.mat-dialog-actions .mat-button + .mat-button, .mat-dialog-actions .mat-button + .mat-raised-button, .mat-dialog-actions .mat-raised-button + .mat-button, .mat-dialog-actions .mat-raised-button + .mat-raised-button
  margin 0 8px;

html
  .right-sidebar
    .title
      margin-left 16px

.y-scrollable {
  overflow-y: scroll;
  height: calc(100vh - 192px);
}

mat-card, mat-dialog-content
  hr
    border none;
    border-top 1px solid #808080 30;
    margin 0

  mat-progress-bar
    margin-top -13px
    height 2px !important
    border-radius 3%


*[lang='en']
  .header .header-remaining-items
    margin-left unset
    margin-right 10px

  .header .sidebar-header-main .app-title
    text-align: left !important
  .right-sidebar.open
    left unset !important
    right 0 !important
    
.qZp31e
  display flex
  justify-content center

