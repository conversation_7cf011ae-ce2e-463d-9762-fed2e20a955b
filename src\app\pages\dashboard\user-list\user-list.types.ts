export interface ITableHeader {
    slug: string;
    label_da?: string;
    label_pa?: string;
    label_en?: string;
    width_multiplication_factor: number;
}

export interface IComplaintList {
    id?: number;
    username?: string;
    name?: string;
    last_name?: string;
    email?: string;
    phone_number?: string;
    api_password?: string;
    status_slug?: string;
    role_slug?: string;
    role_data?: any
}

export interface ISearchFieldType {
    complaint_number: string;
    project_name: string;

}
