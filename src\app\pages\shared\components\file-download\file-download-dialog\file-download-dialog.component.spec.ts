import {async, ComponentFixture, TestBed} from '@angular/core/testing';

import {FileDownloadDialogComponent} from './file-download-dialog.component';

xdescribe('FileUploaderDialogComponent', () => {
    let component: FileDownloadDialogComponent;
    let fixture: ComponentFixture<FileDownloadDialogComponent>;

    beforeEach(async(() => {
        TestBed.configureTestingModule({
            declarations: [FileDownloadDialogComponent]
        })
            .compileComponents();
    }));

    beforeEach(() => {
        fixture = TestBed.createComponent(FileDownloadDialogComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });
});
