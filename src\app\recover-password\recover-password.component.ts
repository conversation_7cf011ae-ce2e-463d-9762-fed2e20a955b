import {AfterViewInit, Component, OnD<PERSON>roy, OnInit} from '@angular/core';
import {NotificationsService} from '../pages/shared/services/notifications.service';
import {FormBuilder, FormGroup, Validators} from '@angular/forms';
import {Router} from '@angular/router';
import {IPage} from '../pages/shared/types/i-page.interface';
import {TranslateService} from '../pages/shared/services/translate.service';
import {HttpService} from '../services/http.service';
import {RecoverPasswordService} from './recover-password.service';
import {FormValidationService} from '../services/form-validation.service';


@Component({
    selector: 'npa-recover-password',
    templateUrl: './recover-password.component.html',
    styleUrls: ['../login/login.component.styl']
})
export class RecoverPasswordComponent implements OnInit, IPage, AfterViewInit, OnDestroy {

    form: FormGroup;
    isLoading = false;
    lang: string;

    constructor(private _formBuilder: FormBuilder,
                public formValidationService: FormValidationService,
                public translate: TranslateService,
                private _recoverPasswordService: RecoverPasswordService,
                private _http: HttpService,
                public notificationsService: NotificationsService,
                private _router: Router) {
    }

    ngOnInit() {
        this.lang = localStorage.getItem('lang');
        this.createForm();
    }

    changeLocalStorageLang(param) {
        localStorage.setItem('lang', param);
    }

    createForm() {
        this.form = this._formBuilder.group({
            email: ['', [Validators.required, Validators.email, this.formValidationService.emailPattern.validator]],
        });
    }

    ngOnDestroy(): void {
        this.notificationsService.startLoading();
    }


    ngAfterViewInit(): void {
        this.notificationsService.dismissLoading();
    }

    submit(email: string) {
        this.notificationsService.startLoading();
        this._recoverPasswordService.sendLink(email).subscribe((res) => {
            this.notificationsService.success(this.translate.translateKey('RECOVER_PASSWORD_LINK_SENDING_MESSAGE'));
            setTimeout(() => {
                this._router.navigate([this.translate.lang + '/login']);
            }, 3000);
        });
    }
}
