import {BaseEntity, Column, Entity, Index, JoinColumn, ManyToOne, PrimaryGeneratedColumn} from 'typeorm';
import {System} from './system';

@Entity('dashboards')
@Index('unique_slug', ['slug'], { unique: true })

export class Dashboard extends BaseEntity implements ISystem {

    @PrimaryGeneratedColumn()
    id: number;

    @Column({unique: true})
    slug: string;

    @Column()
    description: string;

    @Column()
    system_id: string;

    @ManyToOne(type => System, system => system.id)
    @JoinColumn({name: 'system_id'})
    system: System;

}

export interface ISystem {
    id?: number;
    slug: string;
    description: string;
    system_id: string;
    system: System;
}



