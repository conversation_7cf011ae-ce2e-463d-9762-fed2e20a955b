<div mat-dialog-title fxLayout="row wrap" fxFlexAlign="start">
    <div fxFlex="95">ثبت ملاحظه</div>
    <div fxFlex="5">
        <button mat-dialog-close mat-icon-button matTooltip="بستن">
            <mat-icon>close</mat-icon>
        </button>
    </div>
</div>
<mat-dialog-content>
    <form  fxLayout="row wrap" fxLayoutAlign="start" [formGroup]="form">
        <div fxFlex="33">
            <npa-view-element>
                <div class="npa-label">نمبر تشخیصیه</div>
                <div class="npa-value" lang="en">AFG-APPMS-223</div>
            </npa-view-element>
        </div>
        <div fxFlex="33">
            <npa-view-element>
                <div class="npa-label">کاربر</div>
                <div class="npa-value">مدیر پلان تدارکات</div>
            </npa-view-element>
        </div>
        <mat-form-field fxFlex="33">
            <mat-select placeholder="نوعیت" [compareWith]="formService.selectCompare">
                <mat-option *ngFor="let alert_subcategory of alert_subcategories" [value]="alert_subcategory">
                    {{alert_subcategory.name_da}}
                </mat-option>
            </mat-select>
        </mat-form-field>
        <mat-form-field fxFlex="100">
            <input matInput placeholder="عنوان" formControlName="title">
        </mat-form-field>
        <mat-form-field fxFlex="100" formGroupName="message">
            <textarea matInput placeholder="پیام"></textarea>
        </mat-form-field>

        <mat-card fxFlex="100" class="margin-bottom-20px">
            <mat-card-header>دریافت کننده ها</mat-card-header>
            <mat-card-content fxLayout="row wrap" fxLayoutAlign="start">
                <div fxFlex="100">لطفا نوعیت ملاحظه را انتخاب نمایید.</div>
            </mat-card-content>
        </mat-card>

        <mat-card fxFlex="100" class="margin-bottom-20px">
            <mat-card-header>اضافه نمودن دریافت کننده</mat-card-header>
            <mat-card-content fxLayout="row wrap" fxLayoutAlign="start">
                <div fxFlex="100">لطفا نوعیت ملاحظه را انتخاب نمایید.</div>
            </mat-card-content>
        </mat-card>
        <mat-dialog-actions>
            <button mat-raised-button color="primary"
                    type="submit">{{'SAVE'|translate}}
            </button>
            <button mat-button type="button" [mat-dialog-close]="false">{{'CANCEL'|translate}}</button>
        </mat-dialog-actions>
    </form>
</mat-dialog-content>
