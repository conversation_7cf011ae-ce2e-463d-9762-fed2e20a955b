import {<PERSON><PERSON>out<PERSON>} from '../app.routes';
import {StatusController} from './status.controller';
import {Authentication} from '../middlewares/authentication';

export const STATUS_ROUTES: IRoute[] = [
    {
        url: '',
        httpMethod: 'get',
        controllerMethod: StatusController.getInstance().index,
        middleware: [Authentication.validateToken]
    },
    {
        url: '',
        httpMethod: 'post',
        controllerMethod: StatusController.getInstance().store,
        middleware: [Authentication.validateToken]
    },
    {
        url: '/:id',
        httpMethod: 'get',
        controllerMethod: StatusController.getInstance().show,
        middleware: [Authentication.validateToken]
    },
    {
        url: '/:id',
        httpMethod: 'put',
        controllerMethod: StatusController.getInstance().update,
        middleware: [Authentication.validateToken]
    },
    {
        url: '/:id',
        httpMethod: 'delete',
        controllerMethod: StatusController.getInstance().destroy,
        middleware: [Authentication.validateToken]
    },

];
