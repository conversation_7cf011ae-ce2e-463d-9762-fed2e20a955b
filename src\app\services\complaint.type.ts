export interface IOneToOneFormsScanData {
    is_cancel_request_approved: boolean;
    is_transferred_request_approved: boolean;
    projectPlanning: {
        procurementPlanGeneralDetails: boolean,
        cpv: boolean;
        location: {
            domestic: boolean;
            foreign: boolean;
        }
        procurementProcessProgressTimelines: boolean
        risk: boolean;
        document: boolean
        analysis: boolean,
        challenges: boolean;
    };
    projectImplementation: {
        procurementPlanGeneralDetails: boolean,
        cpv: boolean;
        location: {
            domestic: boolean;
            foreign: boolean;
        }
        procurementProcessProgressTimelines: boolean
        risk: boolean;
        document: boolean
        analysis: boolean,
        challenges: boolean;
    };
}
