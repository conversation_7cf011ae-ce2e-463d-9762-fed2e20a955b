import {Observable, Subject, throwError as observableThrowError} from 'rxjs';
import {Injectable} from '@angular/core';
import {HttpErrorResponse, HttpHandler, HttpHeaderResponse, HttpInterceptor, HttpProgressEvent, HttpRequest, HttpResponse, HttpSentEvent, HttpUserEvent} from '@angular/common/http';

import {HelperService} from './helper.service';
import {catchError, tap} from 'rxjs/operators';

@Injectable()

export class InterceptorService implements HttpInterceptor {
    public isUserAuthenticated: Subject<boolean> = new Subject<boolean>();

    intercept(req: HttpRequest<any>, next: HttpHandler): Observable<HttpSentEvent | HttpHeaderResponse | HttpProgressEvent | HttpResponse<any> | HttpUserEvent<any>> {
        const session = localStorage.getItem('session');
        req = req.clone(session ? {
            setHeaders: {
                Accept: 'application/json',
                'Content-Type': 'application/json',
                Authorization: localStorage.getItem('session'),
            }
        } : {
            setHeaders: {
                Accept: 'application/json',
                'Content-Type': 'application/json',
            }
        });
        return next.handle(req)
            .pipe(tap((event: any) => {
                if (event instanceof HttpResponse) {
                    const token = event.headers.get('authorization');
                    if (token) {
                        const newSession = JSON.parse(session);
                        newSession['token'] = token;
                        localStorage.setItem('session', JSON.stringify(newSession));
                    }

                }
            }), catchError(error => {
                this.isUserAuthenticated.next(false);
                switch ((<HttpErrorResponse>error).status) {
                    case 400:
                        break;
                    case 401:
                        HelperService.navigatedIfUnauthorized();
                        break;
                }
                return observableThrowError(error);
            }));

    }

}

