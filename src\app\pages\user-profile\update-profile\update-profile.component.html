<div class="container" fxLayout="row wrap" fxFlexAlign="start">
    <div fxFlex="90">{{'UPDATE_PROFILE'|translate}}</div>
    <button mat-icon-button matTooltip="{{'CLOSE'|translate}}" [mat-dialog-close]="false" class="dialog-close-button">
        <mat-icon>close</mat-icon>
    </button>
    <form *ngIf="form" [formGroup]="form" (ngSubmit)="updateProfile()" fxLayout="row wrap"
          fxFlexAlign="start">
        <mat-form-field fxFlex="50">
            <input type="text" formControlName="name" matInput placeholder="{{'FIRST_NAME'|translate}}">
            <mat-error *ngIf="form?.get('name')?.errors?.required">
                {{'REQUIRED_FIELD_MESSAGE'|translate}}
            </mat-error>
        </mat-form-field>
        <mat-form-field fxFlex="50">
            <input type="text" formControlName="last_name" matInput placeholder="{{'LAST_NAME'|translate}}">
            <mat-error *ngIf="form?.get('last_name')?.errors?.required">
                {{'REQUIRED_FIELD_MESSAGE'|translate}}
            </mat-error>
        </mat-form-field>
        <mat-form-field fxFlex="50">
            <input type="text" formControlName="username" matInput placeholder="{{'USERNAME'|translate}}">
            <mat-error *ngIf="form?.get('username')?.errors?.required">
                {{'REQUIRED_FIELD_MESSAGE'|translate}}
            </mat-error>
            <mat-error
                    *ngIf="!form?.get('username')?.valid && !form?.get('username')?.errors?.required">
                {{'USERNAME_PATTERN_INVALID_MESSAGE'|translate}}
            </mat-error>
        </mat-form-field>
        <mat-form-field fxFlex="50">
            <input type="text" formControlName="phone_number" matInput
                   placeholder="{{'PHONE_NUMBER'|translate}}">
            <mat-error *ngIf="form?.get('phone_number')?.errors?.required">
                {{'REQUIRED_FIELD_MESSAGE'|translate}}
            </mat-error>
            <mat-error
                    *ngIf="!form?.get('phone_number')?.valid && !form?.get('phone_number')?.errors?.required">
                {{'PHONE_NUMBER_PATTERN_MESSAGE'|translate}}
            </mat-error>
        </mat-form-field>
        <mat-form-field fxFlex="50">
            <input type="text" formControlName="email" matInput placeholder="{{'EMAIL_ADDRESS'|translate}}">
            <mat-error *ngIf="form?.get('email')?.errors?.required">
                {{'REQUIRED_FIELD_MESSAGE'|translate}}
            </mat-error>
            <mat-error
                    *ngIf="!form?.get('email')?.valid && !form?.get('email')?.errors?.required">
                {{'EMAIL_PATTERN_MESSAGE'|translate}}
            </mat-error>
        </mat-form-field>
        <div fxFlex="50"></div>
        <div fxFlex="100">
            <button class="submit" mat-button type="submit"
                    [disabled]="form.invalid || notificationsService.isLoading">{{'SAVING'|translate}}</button>
        </div>
    </form>
</div>
