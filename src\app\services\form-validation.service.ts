import {Injectable} from '@angular/core';
import {Validators, ValidatorFn} from '@angular/forms';
import {MAP} from '../../environments/environment.const';

export interface IValidation {
    validator: ValidatorFn;
    message: (fieldName?: string) => string;
}

@Injectable()

export class FormValidationService {

    private _lang;


    constructor() {
        if (localStorage.getItem('lang')) {
            this._lang = localStorage.getItem('lang');

        } else {
            this._lang = 'prs';
        }
    }

    setLang(lang: string) {
        if (MAP[lang] === undefined) {
            throw new Error('Translation map for this language (' + lang + ') does not exist.');
        }
        this._lang = lang;

    }

    get required(): IValidation {
        return {
            validator: Validators.required,
            message: (fieldName?: string) => {
                return fieldName ?
                    MAP[this._lang]['required'].replace(':name', fieldName) :
                    this._lang === 'en' ?
                        MAP[this._lang]['required'].replace(':name', 'This information') :
                        this._lang === 'ps' ?
                            MAP[this._lang]['required'].replace(':name', 'دغه معلومات') :
                            MAP[this._lang]['required'].replace(':name', 'این معلومات');
            }
        };
    }

    get minLength5(): IValidation {
        return {
            validator: Validators.minLength(5),
            message: () => {
                return MAP[this._lang]['minLength5'];
            }
        };
    }

    get emailPattern(): IValidation {
        return {
            validator: Validators
                .pattern(/^(([a-z0-9](\.|-|_){0,1})*)[a-z0-9]+@(([a-z0-9](\.|-|_){0,1})*)[a-z0-9]\.([a-z0-9](\.|-|_){0,1})+$/),
            message: () => {
                return MAP[this._lang]['emailPattern'];
            }
        };
    }

    get onlyText(): IValidation {
        return {
            validator: Validators.pattern(/^(\u0626|\u0621|\u0622|[\u0627-\u06cc]|[a-zA-Z]|\s)*$/),
            message: () => {
                return MAP[this._lang]['onlyText'];
            }
        };
    }

    get phoneNumberAfg(): IValidation {
        return {
            validator: Validators.pattern(/^93[0-9]{9}$/),
            message: () => {
                return MAP[this._lang]['phoneNumberAfg'];
            }
        };
    }

    get phoneNumberInternational(): IValidation {
        return {
            validator: Validators.pattern(/^[0-9]{1,3}[0-9]{4,14}(?:x.+)?$/),
            message: () => {
                return MAP[this._lang]['phoneNumberInternational'];
            }
        };
    }

    get generalPhoneNumber(): IValidation {
        return {
            validator: Validators.pattern(/^((93(7|2)[0-9]{8})|(((9[^3])|[1-8])[0-9]{2,13}))$/),
            message: () => {
                return MAP[this._lang]['generalPhoneNumber'];
            }
        };
    }

    get integer(): IValidation {
        return {
            validator: Validators.pattern(/^[0-9]+$/),
            message: () => {
                return MAP[this._lang]['integer'];
            }
        };
    }

    get username(): IValidation {
        return {
            validator: Validators.pattern(/^[^0-9A-Z_][a-z0-9]+$/),
            message: () => {
                return MAP[this._lang]['username'];
            }
        };
    }

}




