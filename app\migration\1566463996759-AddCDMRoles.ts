import {MigrationInterface, QueryRunner} from 'typeorm';

export class AddCDMRoles1566463996759 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<any> {
        try {
            await queryRunner.query(`
                INSERT INTO
                roles (name, slug, party, can_be_acted, can_act, is_pe_based, is_sector_based, is_vendor_based, is_record_based, system_id)
                VALUES
                    ('common-data-manager','common-data-manager', 'npa', true, false, false, false, false, false, 5)
                    `);

        } catch (e) {
            throw e;
        }
    }

    public async down(queryRunner: QueryRunner): Promise<any> {
        await queryRunner.query(`Delete from roles where slug = 'common-data-manager' `);
    }

}
