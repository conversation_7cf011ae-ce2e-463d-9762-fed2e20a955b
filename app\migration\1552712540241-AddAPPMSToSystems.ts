import {MigrationInterface, QueryRunner} from 'typeorm';

export class AddAPPMSToSystems1552712540241 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<any> {
        await queryRunner.query(`
            INSERT INTO
              systems (id, slug, url, description, icon_name)
            VALUES
              (4, 'appms', '${process.env.CUSTOM_APPMS_BASE_URL}', 'Afghanistan Procurement Planning Management System', 'assignment')`);
    }

    public async down(queryRunner: QueryRunner): Promise<any> {
    }

}
