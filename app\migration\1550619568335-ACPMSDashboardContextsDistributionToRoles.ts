import {MigrationInterface, QueryRunner} from 'typeorm';

export class ACPMSDashboardContextsDistributionToRoles1550619568335 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<any> {
        await queryRunner.query(`
            INSERT INTO context_accesses (operation_id, is_enabled,role_id, context_id)
            select 1, 1, 1, id
            from contexts where slug in (
            'above-threshold-contracts-count-widget',
            'below-threshold-contracts-count-widget',
            'complete-contracts-payments-value-based-on-procurement-types',
            'consultancy-services-planned-and-actual-uploaded-documents-count',
            'contract-count-and-value-based-on-procurement-type',
            'contract-manager-assigned-contracts-count',
            'contract-manager-contracts-amendments-count-by-type',
            'contract-manager-contracts-time-and-cost-amendments-percentage-vs-origin-time-and-cost',
            'contract-managers-assigned-contracts-status-percentage',
            'contract-status-cancelled-count-widget',
            'contract-status-completed-count-widget',
            'contracts-based-on-challenges-count',
            'contracts-based-on-procurement-method-count',
            'contracts-based-on-selection-method-count',
            'contracts-based-on-status-count',
            'contracts-count-based-on-donor',
            'contracts-implementations-challenges-count',
            'contracts-value-based-on-donor',
            'contracts-value-based-on-status',
            'cpmd-specialists-remarks-count-widget',
            'details-approved-amended-contracts-count-widget',
            'general-information-approved-contracts-count-widget',
            'general-information-not-approved-contracts-count-widget',
            'payments-percentage-in-comparison-with-plan',
            'planned-and-actual-uploaded-documents-count',
            'transferred-contracts-to-contract-manager-status-count',
            'work-in-progress-contracts-payments-value',
            'work-in-progress-contracts-physical-progress-value',
            'provinces-contracts-value-count-and-status'
            )`);
        await queryRunner.query(`
            INSERT INTO context_accesses (operation_id, is_enabled,role_id, context_id)
            select 1, 1, 2, id
            from contexts where slug in (
            'above-threshold-contracts-count-widget',
            'below-threshold-contracts-count-widget',
            'award-authority-contracts-time-and-cost-amendments-percentage-vs-origin-time-and-cost',
            'complete-contracts-payments-value-based-on-procurement-types',
            'contract-count-and-value-based-on-procurement-type',
            'consultancy-services-planned-and-actual-uploaded-documents-count',
            'contracts-amendments-types-count-based-on-procurement-type',
            'contracts-based-on-challenges-count',
            'contracts-based-on-procurement-method-count',
            'contracts-based-on-selection-method-count',
            'contracts-based-on-status-count',
            'contracts-based-on-threshold-and-procurement-type-count',
            'contracts-count-based-on-donor',
            'contracts-count-widget',
            'contracts-implementations-challenges-count',
            'contracts-related-to-contract-manager-count',
            'contracts-total-payments-widget',
            'contracts-total-physical-progress-value-widget',
            'contracts-value-based-on-donor',
            'contracts-value-based-on-status',
            'details-approved-contracts-total-value-widget',
            'general-information-not-approved-contracts-count-widget',
            'payments-percentage-in-comparison-with-plan',
            'planned-and-actual-uploaded-documents-count',
            'provinces-contracts-value-count-and-status',
            'published-and-signed-contracts-count',
            'published-and-unpublished-contracts-count',
            'published-contracts-based-on-threshold-count',
            'published-contracts-count-widget',
            'work-in-progress-contracts-payments-value',
            'work-in-progress-contracts-physical-progress-value'
            )`);
        await queryRunner.query(`
            INSERT INTO context_accesses (operation_id, is_enabled,role_id, context_id)
            select 1, 1, 3, id
            from contexts where slug in (
            'analyzed-contracts-count-widget',
            'change-request-contracts-count-widget',
            'consultancy-services-planned-and-actual-uploaded-documents-count',
            'contract-analysis-prepare-to-publish-count-widget',
            'contract-analysis-published-by-cpmd-managers-count-widget',
            'contracts-amendments-count-by-type',
            'contracts-based-on-challenges-count',
            'contracts-based-on-procurement-method-count',
            'contracts-based-on-status-count',
            'contracts-count-based-on-donor',
            'contracts-count-widget',
            'contracts-implementations-challenges-count',
            'contracts-value-based-on-donor',
            'contracts-value-based-on-status',
            'not-analyzed-contracts-count-widget',
            'payments-percentage-in-comparison-with-plan',
            'pes-contracts-value-percentage-count',
            'pes-new-contracts-vs-transferred-contracts-count',
            'planned-and-actual-uploaded-documents-count',
            'provinces-contracts-value-count-and-status',
            'published-contracts-count-widget',
            'published-documents-percentage-widget',
            'specialist-analysed-contracts-count',
            'specialist-contracts-analysis-status-count',
            'specialist-contracts-analysis-status-count-in-pe',
            'vendors-signed-contracts-value-and-number-count',
            'work-in-progress-contracts-payments-value',
            'work-in-progress-contracts-physical-progress-value'
            )`);
        await queryRunner.query(`
            INSERT INTO context_accesses (operation_id, is_enabled,role_id, context_id)
            select 1, 1, 4, id
            from contexts where slug in (
            'analyzed-contracts-count-widget',
            'change-request-contracts-count-widget',
            'consultancy-services-planned-and-actual-uploaded-documents-count',
            'contract-analysis-prepare-to-publish-count-widget',
            'contract-analysis-published-by-cpmd-managers-count-widget',
            'contracts-amendments-count-by-type',
            'contracts-based-on-challenges-count',
            'contracts-based-on-status-count',
            'contracts-count-based-on-donor',
            'contracts-count-widget',
            'contracts-implementations-challenges-count',
            'contracts-value-based-on-donor',
            'contracts-value-based-on-status',
            'cpmd-manager-contracts-based-on-procurement-method-count',
            'department-contracts-status-count',
            'department-specialists-analyzed-contracts-count',
            'not-analyzed-contracts-count-widget',
            'payments-percentage-in-comparison-with-plan',
            'pes-contracts-value-percentage-count',
            'pes-new-contracts-vs-transferred-contracts-count',
            'planned-and-actual-uploaded-documents-count',
            'provinces-contracts-value-count-and-status',
            'published-contracts-count-widget',
            'published-documents-percentage-widget',
            'vendors-signed-contracts-value-and-number-count',
            'work-in-progress-contracts-payments-value',
            'work-in-progress-contracts-physical-progress-value'
            )`);
    }

    public async down(queryRunner: QueryRunner): Promise<any> {
    }

}
