import {BrowserModule} from '@angular/platform-browser';
import {APP_INITIALIZER, NgModule} from '@angular/core';
import {AppComponent} from './app.component';
import {FlexLayoutModule} from '@angular/flex-layout';
import {BrowserAnimationsModule} from '@angular/platform-browser/animations';
import {MaterialModule} from '../material.module';
import {appComponents, appRoutes} from './app.routes';
import {SharedModule} from './pages/shared/shared.module';
import {FormsModule, ReactiveFormsModule} from '@angular/forms';
import {HTTP_INTERCEPTORS, HttpClientModule} from '@angular/common/http';
import {BootService} from './services/boot.service';
import {InterceptorService} from './services/interceptor.service';
import {AuthService} from './services/auth.service';
import {HttpService} from './services/http.service';
import {RouteService} from './services/route.service';
import {RouteGuardsService} from './services/route-guards.service';
import {DropDownsService} from './services/dropDowns.service';
import {CustomValidationService} from './services/custom-validation.service';
import {ComplaintService} from './services/complaint.service';
import {RecoverPasswordService} from './recover-password/recover-password.service';
import {RecoverPasswordFormService} from './recover-password-form/recover-password-form.service';
import {SocketService} from './pages/shared/services/socket.service';
import {ProcurementTypeContextService} from './services/procurement-type-context.service';
import {HelperService} from './services/helper.service';
import {FormValidationService} from './services/form-validation.service';
import {VendorRegistrationFormProvider} from './vendor-registration-form/vendor-registration-form-provider';

export function init(bootService: BootService): Function {
    return () => bootService.load();
}


@NgModule({
    declarations: [
        ...appComponents,
    ],
    imports: [
        FlexLayoutModule,
        BrowserModule,
        BrowserAnimationsModule,
        MaterialModule,
        SharedModule,
        ReactiveFormsModule,
        FormsModule,
        HttpClientModule,
        appRoutes,
    ],
    providers: [
        InterceptorService,
        BootService,
        AuthService,
        HttpService,
        RouteService,
        DropDownsService,
        RouteGuardsService,
        CustomValidationService,
        SocketService,
        HelperService,
        FormValidationService,
        {
            provide: APP_INITIALIZER,
            useFactory: init,
            deps: [BootService],
            multi: true
        },
        {
            provide: HTTP_INTERCEPTORS,
            useClass: InterceptorService,
            deps: [],
            multi: true
        },
        ComplaintService,
        ProcurementTypeContextService,
        RecoverPasswordService,
        RecoverPasswordFormService,
        VendorRegistrationFormProvider
    ],
    bootstrap: [AppComponent],
})
export class AppModule {
    constructor() {

    }
}
