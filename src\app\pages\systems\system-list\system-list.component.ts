import {Component, OnInit, ViewChild} from '@angular/core';
import {ConfirmationAndJustificationService} from '../../shared/services/confirmation-and-justification.service';
import {AddEditDialogComponent} from '../../dashboard/user-list/add-edit-dialog/add-edit-dialog.component';
import {IComplaintList} from '../../dashboard/user-list/user-list.types';
import {ActivatedRoute, Router} from '@angular/router';
import {MatDialog, MatPaginator} from '@angular/material';
import {FormBuilder, FormGroup} from '@angular/forms';
import {NpaDatabase, NpaDataSource} from '../../shared/classes/npa-table';
import {AuthService} from '../../../services/auth.service';
import {FormService} from '../../shared/services/form.service';
import {NotificationsService} from '../../shared/services/notifications.service';
import {TranslateService} from '../../shared/services/translate.service';
import {NPA_COMMON_DIALOG_WIDTH} from '../../shared/consts/sizes';
import {ComplaintService} from '../../../services/complaint.service';
import {SystemListService} from './system-list.service';
import {ViewDialogComponent} from './view-dialog/view-dialog.component';
import {HttpService} from '../../../services/http.service';

@Component({
  selector: 'npa-system-list',
  templateUrl: './system-list.component.html',
  styleUrls: ['./system-list.component.styl']
})
export class SystemListComponent implements OnInit {

  displayedData = [
    {
      id: 1,
      system_icon: 'AA-BB1999-C',
      system_slug: 'کاربر امتحانی 1',
      system_url: 'کاربر امتحانی 1',
    },
    {
      id: 1,
      system_icon: 'AA-BB1999-C',
      system_slug: 'کاربر امتحانی 1',
      system_url: 'کاربر امتحانی 1',
    },
  ];


  dataSource: NpaDataSource;
  database: NpaDatabase;
  form: FormGroup;
  lang: string;
  referenceTitles: string[] = [];
  chosenTitles: string[] = [];
  @ViewChild(MatPaginator) paginator: MatPaginator;
  dir: string;


  constructor(private formBuilder: FormBuilder,
              public authService: AuthService,
              public formService: FormService,
              public systemListService: SystemListService,
              private activatedRoute: ActivatedRoute,
              private router: Router,
              private dialog: MatDialog,
              private complaintService: ComplaintService,
              private notificationsService: NotificationsService,
              private _httpService: HttpService,
              private confirmationAndJustificationService: ConfirmationAndJustificationService,
              private translateService: TranslateService) {
  }

  ngOnInit() {
    console.log('>>>>>>>>');
    this.lang = this.translateService.lang;
    this.dir = this.translateService.dir;
    // this.initForm();
    this.notificationsService.dismissLoading();
    this.referenceTitles = this.systemListService.getDisplayedColumns(
      this.authService.user.value.role.slug,
      this.authService.loggedInUser.role.slug
    );

    this.chosenTitles = this.referenceTitles.slice();
    setTimeout(() => {
      this.paginator.pageSize = 5;
      this.paginator.pageSizeOptions = [5, 10, 50, 100];
      this.paginator.page.subscribe(() => {
      });
    }, 0);

    this.database = new NpaDatabase(this.displayedData);
    this.dataSource = new NpaDataSource(this.database);

  }

  // initForm() {
  //   this.form = this.formBuilder.group({
  //     user_identification_number: undefined,
  //     project_name: undefined,
  //   });
  // }

  // selectComplaint(data: IComplaintList) {
  //   this.complaintService.complaintInformation
  //     .next(
  //       {
  //         id: data.id,
  //         user_identification_number: data.user_identification_number,
  //         isComplaintSelected: true
  //       }
  //     );
  //   this.authService.selectedComplaintUrl = this.authService.defaultUserLoggedInUrl +
  //     `dashboard/user-list/${data.user_identification_number}`;
  //   this.router.navigate(
  //     [`${encodeURIComponent(data.user_identification_number)}/reports/generate-and-download-reports`],
  //     {relativeTo: this.activatedRoute}
  //   );
  // }

  getWidth() {
    let total = 0;
    this.chosenTitles.forEach((current: string) => {
      total += this.systemListService.getWidthMultiplicationFactor(current);
    });
    return (total * 150) + 'px';
  }

  openAddEditDialog(data = {}) {
    const dialogRef = this.dialog.open(AddEditDialogComponent, {
      width: NPA_COMMON_DIALOG_WIDTH,
      data: data,
      disableClose: true,
    });
    dialogRef.afterClosed().subscribe(result => {
      if (Object.keys(result).length === 0) {
        return;
      }
      if (this.database.find(result.id)) {
        this.database.edit(result);
        return;
      }
      this.database.add(result);
    });
  }

  openViewDialog(data = {}) {
    this.dialog.open(ViewDialogComponent, {
      width: NPA_COMMON_DIALOG_WIDTH,
      data: data,
      disableClose: true,
    });
  }

}
