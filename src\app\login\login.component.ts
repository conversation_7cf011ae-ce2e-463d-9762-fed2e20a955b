import {AfterViewInit, Component, OnD<PERSON>roy, OnInit} from '@angular/core';
import {FormBuilder, FormGroup} from '@angular/forms';
import {AuthService} from '../services/auth.service';
import {Router} from '@angular/router';


import {HttpService} from '../services/http.service';
import {IPage} from '../pages/shared/types/i-page.interface';
import {NotificationsService} from '../pages/shared/services/notifications.service';
import {HttpErrorResponse} from '@angular/common/http';
import {MatSnackBar} from '@angular/material';
import {TranslateService} from '../pages/shared/services/translate.service';
import {HelperService} from '../services/helper.service';
import {FormValidationService} from '../services/form-validation.service';
import {environment as localEnvironment} from '../../environments/environment';
import {environment as productionEnvironment} from '../../environments/environment.prod';

interface ICredentials {
    username: string;
    password: string;
}

@Component({
    selector: 'npa-login',
    templateUrl: './login.component.html',
    styleUrls: ['./login.component.styl']
})
export class LoginComponent implements OnInit, IPage, AfterViewInit, OnDestroy {

    form: FormGroup;
    isLoading = false;
    env = localEnvironment || productionEnvironment;
    lang: string;

    constructor(private _formBuilder: FormBuilder,
                public formValidationService: FormValidationService,
                private _auth: AuthService,
                public translate: TranslateService,
                private _http: HttpService,
                private _notificationsService: NotificationsService,
                private _router: Router,
                private _matSnackBar: MatSnackBar) {
    }

    ngOnInit() {
        this.lang = localStorage.getItem('lang');
        if (!this.env.loginThroughDataBase) {
            this._auth.isUserAuthenticated = true;
            localStorage.setItem('session', '12345');
            this._router.navigate(['dev-ops']);
            this._auth.roles = this.env.logInData.roles;
            this._auth.allCredentials = this.env.logInData.user;
            return;
        }
        this.createForm();
    }

    changeLocalStorageLang(param) {
        localStorage.setItem('lang', param);
    }

    createForm() {
        this.form = this._formBuilder.group({
            username: ['', this.formValidationService.required.validator],
            password: ['', this.formValidationService.required.validator],
        });
    }

    ngOnDestroy(): void {
        this._notificationsService.startLoading();
    }

    ngAfterViewInit(): void {
        this._notificationsService.dismissLoading();
    }

    login(formData: ICredentials) {
        this._notificationsService.startLoading();
        this.isLoading = true;
        this._http.postSpecial(HelperService.redirectedSystemBaseUrl ?
            `api/login?redirected_url=${HelperService.redirectedSystemBaseUrl}` :
            `api/login`, formData, {observe: 'response'})
            .toPromise().then(res => {
            this._notificationsService.dismissLoading();
            if (res.status === 200) {
                const lang = localStorage.getItem('lang');
                const session = res.body.session;
                const singleUseToken = session.singleUseToken;
                delete session.singleUseToken;
                delete session.redirectToUrl;
                this._auth.allCredentials = res.body.user;
                this._auth.isUserAuthenticated = true;
                localStorage.setItem('session', JSON.stringify(session));
                if (singleUseToken && HelperService.redirectedUrl) {
                    document.location.href = `${HelperService.redirectedUrl}?singleUseToken=${singleUseToken}`;
                    return;
                } else if (!singleUseToken && HelperService.redirectedUrl) {
                    this._router.navigate([lang, 'unauthorized-access']);
                    return;
                }
                this._router.navigate([lang, res.body.user.role.slug]);
            }
        }, (err: HttpErrorResponse | any) => {
            this._matSnackBar.open(this.translate.translateKey('ERROR_UNSUCCESSFUL_LOGIN_ATTEMPT'), undefined, {
                duration: 5000,
            });
            if (err.error && err.error.password) {
                this._notificationsService.error(this.translate.translateKey(err.error.password.msg));
            } else {
                this._notificationsService.error(null);
            }
            this.isLoading = false;
            console.error(err);
        });
    }

    goToAGEOPS() {
        window.location.href = this.env.baseUrl.ageops.main;
    }
}
