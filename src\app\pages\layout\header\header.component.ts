import {Component, ElementRef, EventEmitter, Input, OnInit, Output} from '@angular/core';
import {ActivatedRoute, Router} from '@angular/router';
import {AuthService} from '../../../services/auth.service';
import {Location} from '@angular/common';

import {AlertService} from '../../alert/alert.service';
import {IAlert, IAppRealTimeSate} from '../../alert/alert.types';
import {NotificationsService} from '../../shared/services/notifications.service';
import {TranslateService} from '../../shared/services/translate.service';
import {ProcurementTypeContextService} from '../../../services/procurement-type-context.service';
import {IRole} from '../../../services/auth.types';
import {NPA_ALERTS} from '../../shared/consts/messages';
import {environment as localEnvironment} from '../../../../environments/environment';
import {environment as productionEnvironment} from '../../../../environments/environment.prod';

@Component({
    selector: 'npa-header',
    templateUrl: './header.component.html',
    styleUrls: ['./header.component.styl']
})
export class HeaderComponent implements OnInit {
    env = localEnvironment || productionEnvironment;
    isSidebarHeaderMainClose = false;
    isSidebarHeaderUtilitiesClose = true;
    @Output() sidebarMainToggling = new EventEmitter<boolean>();
    @Output() sidebarUtilitiesToggling = new EventEmitter<boolean>();
    @Input() lang: string;
    roles;
    hasReceivedNewAlerts = false;

    constructor(private _router: Router,
                public auth: AuthService,
                private _activatedRoute: ActivatedRoute,
                private _location: Location,
                private _alertService: AlertService,
                private _notificationsService: NotificationsService,
                private _procurementTypeContextService: ProcurementTypeContextService,
                private _authService: AuthService,
                public translate: TranslateService,
                private _elementRef: ElementRef) {

    }

    ngOnInit() {
        this.lang = this.translate.lang;
        this.roles = this.auth.allAccessibleRoles;
        this._alertService.alertSubject.subscribe((alert: IAlert) => {
            this.hasReceivedNewAlerts = !!alert;
            if (!alert) {
                return;
            }
            this._notificationsService.infoDesktop(
                'شما هشدار جدید در سیستم APPMS دارید',
                `${this.env.baseUrl.frontEnd.main}${this._authService.loggedInUser.role.slug}/alert/new`
            );
        });

        this._alertService.appInitialRealTimeStates.subscribe((states: IAppRealTimeSate) => {

            this.hasReceivedNewAlerts = !!states.newAlerts;

            if (!this._alertService.isStateTypeNewAlert(states)) {
                return;
            }

            if (this._alertService.shouldConsiderNewAlertForCurrentSession(states)) {
                this._alertService.markTheAlertShownForCurrentSession(states);

                this._alertService.appRealTimeStatesRepeater.next(states);

                this._notificationsService.infoDesktop(
                    'شما هشدار جدید در سیستم APPMS دارید',
                    `${this.env.baseUrl.frontEnd.main}${this._authService.loggedInUser.role.slug}/alert/new`
                );
            }
        });

    }

    navigateTo() {
        this._router.navigate(['user-profile'], {relativeTo: this._activatedRoute});
    }

    alertTo() {
        this._router.navigate(['alert'], {relativeTo: this._activatedRoute});
    }

    toggleSidebarMain() {
        this.isSidebarHeaderMainClose = !this.isSidebarHeaderMainClose;
        this.sidebarMainToggling.emit(this.isSidebarHeaderMainClose);
    }

    toggleSidebarUtilities() {
        this.isSidebarHeaderUtilitiesClose = !this.isSidebarHeaderUtilitiesClose;
        this.sidebarUtilitiesToggling.emit(this.isSidebarHeaderUtilitiesClose);
    }

    logout() {
        this._notificationsService.startLoading();
        this.auth.isUserAuthenticated = false;
        this._authService.logoutAPI().subscribe(
            () => {
                this._notificationsService.dismissLoading();
                this.auth.clearAllCredentials();
                this._router.navigate([localStorage.getItem('lang'), 'login']);
            }, (error) => {
                this._notificationsService.error(NPA_ALERTS.ERROR);
                console.error(error);
            }
        );
    }

    changeLang(val: string) {
        this._elementRef.nativeElement.parentElement.parentElement.setAttribute('lang', val);
        localStorage.setItem('lang', val);
        const path = this._location.prepareExternalUrl(this._location.path());
        const pathArray = path.split('/');
        pathArray[1] = val;
        const newUrl = pathArray.join('/');
        window.location.href = this.env.baseUrl.frontEnd.main + newUrl.substr(1);
    }

    switchRole(role: IRole) {
        const urlString = this._location.prepareExternalUrl(this._location.path());
        const urlArray = urlString.split('/');
        urlArray[1] = role.slug;
        this._authService.actingRole = role;
        this._router.navigateByUrl(urlArray.join('/'));
    }

}

