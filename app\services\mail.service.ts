import * as env from 'dotenv';
import * as path from 'path';
import * as nodeMailer from 'nodemailer';


env.load({ path: path.resolve(__dirname, '../.env') });

interface IMailOptions {
  from: string; // sender address
  to: string; // list of receivers
  subject: string; // Subject line
  text?: string; // plain text body
  html?: string;
  ses?: any;
}

export class MailService {
  public static async sendEmail (to, subject?, text?, html?) {
    const transport = await nodeMailer.createTransport({
      host: process.env.MAIL_HOST,
      port: process.env.MAIL_PORT,
      secure: false, // true for 465, false for other ports
      auth: {
        user: process.env.MAIL_USERNAME, // generated ethereal user
        pass: process.env.MAIL_PASSWORD // generated ethereal password
      },
      tls: {
            rejectUnauthorized: false,
      	},
    });
    const mailOptions: IMailOptions = {
      from: process.env.MAIL_FROM_NAME,
      to: to,
      subject: subject ? subject : process.env.MAIL_SUBJECT,
      text: text ? text : '',
      html: html ? html : ''
    };
    return await transport.sendMail(mailOptions);
    return true;
  }
}
