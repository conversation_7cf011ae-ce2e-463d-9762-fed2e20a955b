import {NgModule} from '@angular/core';
import {CommonModule} from '@angular/common';
import {sessionsComponents, sessionsRoutes} from './sessions.route';
import {
    MatButtonModule,
    MatCardModule,
    MatCheckboxModule,
    MatDatepickerModule,
    MatDialogModule,
    MatExpansionModule,
    MatFormFieldModule,
    MatIconModule,
    MatInputModule,
    MatNativeDateModule,
    MatPaginatorModule,
    MatRadioModule,
    MatSelectModule,
    MatTableModule,
    MatToolbarModule,
    MatTooltipModule
} from '@angular/material';
import {FormsModule, ReactiveFormsModule} from '@angular/forms';
import {SharedModule} from '../shared/shared.module';
import {FlexLayoutModule} from '@angular/flex-layout';
import {NgxEchartsModule} from 'ngx-echarts';

@NgModule({
    imports: [
        CommonModule,
        FormsModule,
        sessionsRoutes,
        MatCardModule,
        MatTableModule,
        MatToolbarModule,
        MatIconModule,
        MatButtonModule,
        MatExpansionModule,
        FlexLayoutModule,
        MatFormFieldModule,
        MatInputModule,
        MatRadioModule,
        MatSelectModule,
        MatButtonModule,
        MatCheckboxModule,
        MatPaginatorModule,
        MatNativeDateModule,
        MatDatepickerModule,
        ReactiveFormsModule,
        MatTooltipModule,
        MatDialogModule,
        NgxEchartsModule,
        SharedModule,
    ],
    declarations: [
        ...sessionsComponents
    ]
})


export class SessionsModule {
}
