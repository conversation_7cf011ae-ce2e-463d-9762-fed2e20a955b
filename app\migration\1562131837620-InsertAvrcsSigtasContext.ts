import {MigrationInterface, QueryRunner} from 'typeorm';

export class InsertAvrcsSigtasContextns1562131837620 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<any> {
        await queryRunner.query(`
            INSERT INTO
              contexts (slug, url, description, system_id, is_dashboard)
            VALUES
            ('tax-clearance-verification','','', 3, false)
            `);
        await queryRunner.query(`
            INSERT INTO
              context_accesses (is_enabled, role_id, context_id, operation_id)
            VALUES
            (true, 11, (select id from contexts where slug = "tax-clearance-verification"), 1)
            `);

    }

    public async down(queryRunner: QueryRunner): Promise<any> {
    }

}
