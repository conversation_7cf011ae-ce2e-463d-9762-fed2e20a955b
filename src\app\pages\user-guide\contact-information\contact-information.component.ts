import {Component, OnInit} from '@angular/core';
import {NotificationsService} from '../../shared/services/notifications.service';
import {TranslateService} from '../../shared/services/translate.service';

@Component({
    selector: 'npa-contact-information',
    templateUrl: './contact-information.component.html',
    styleUrls: ['./contact-information.component.styl']
})
export class ContactInformationComponent implements OnInit {

    constructor(private _notificationsService: NotificationsService,
                public translate: TranslateService) {
    }

    ngOnInit() {
        this._notificationsService.dismissLoading();
    }

}
