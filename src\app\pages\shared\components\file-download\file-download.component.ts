import {Component, ElementRef, Input, OnInit, ViewChild} from '@angular/core';
import {MatDialog} from '@angular/material';
import {FileDownloadDialogComponent} from './file-download-dialog/file-download-dialog.component';
import {HttpService} from '../../../../services/http.service';
import {FileService} from '../../services/file.service';
import {NPA_ALERTS} from '../../consts/messages';
import {HttpErrorResponse} from '@angular/common/http';
import {NotificationsService} from '../../services/notifications.service';
import {environment} from '../../../../../environments/environment';

export interface IFile {
    id: number;
    original_name: string;
    assigned_name: string;
    field_name: string;
}

interface IUpload {
    name: string;
    date: string;
}

@Component({
    selector: 'npa-file-download',
    templateUrl: './file-download.component.html',
    styleUrls: ['./file-download.component.styl']
})
export class FileDownloadComponent implements OnInit {
    @ViewChild('container') nativeContainer: ElementRef;
    @Input() uploadLogs: IUpload[] = [];
    @Input() file: IFile[];
    lastFile: IFile;
    bubbleDetector = false;
    @Input() isLog = false;

    constructor(private _dialog: MatDialog,
                private _http: HttpService,
                private _fileService: FileService,
                public notificationsService: NotificationsService) {
    }

    ngOnInit() {
        this.lastFile = this.file ? this.file[this.file.length - 1] : null;
        setTimeout(() => {
            this.bubbleDetector = true;
        }, 0);
    }

    downloadFile(): void {
        this.notificationsService.info(NPA_ALERTS.FILE_DOWNLOAD);
        if (environment.isAwsS3Used) {
            this._http.get((this.isLog ? 'api/log/attachment/' : 'api/attachment/') + this.lastFile.id).subscribe(res => {
                window.open(res);
                this.notificationsService.dismissLoading();
                return;
            }, (error: HttpErrorResponse) => {
                this.notificationsService.error(NPA_ALERTS.ERROR);
                console.error(error);
            });
        } else {
            this._http.download((this.isLog ? 'api/log/attachment/' : 'api/attachment/') + this.lastFile.id).subscribe(res => {
                this._fileService.download(this.nativeContainer, res, this.lastFile.original_name);
                this.notificationsService.dismissLoading();
            }, (error: HttpErrorResponse) => {
                this.notificationsService.error(NPA_ALERTS.ERROR);
                console.error(error);
            });
        }
    }

    openDialog() {
        if (this.file) {
            this._dialog.open(FileDownloadDialogComponent, {
                width: '600px',
                data: {
                    showDeleteOption: false,
                    files: this.file
                }
            });
        }
    }

    getFileNameWidth(container: HTMLDivElement): string {
        return (container.clientWidth - 89) + 'px';
    }
}
