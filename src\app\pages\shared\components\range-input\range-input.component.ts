import {Component, EventEmitter, Input, OnInit, Output} from '@angular/core';

@Component({
    selector: 'npa-range-input',
    templateUrl: './range-input.component.html',
    styleUrls: ['./range-input.component.styl']
})
export class RangeInputComponent implements OnInit {
    @Input() config;
    @Input() label: string;
    @Input() name;
    @Input() tooltips = [true, true];
    @Input() npaModel;
    @Output() npaModelChange = new EventEmitter();

    constructor() {
    }

    ngOnInit() {
    }

    onChangeInput($event) {
        this.npaModelChange.emit($event);
    }

}
