import {BaseEntity, Column, Entity, <PERSON>in<PERSON><PERSON>umn, ManyToOne, PrimaryGeneratedColumn} from 'typeorm';
import {Role} from './role';
import {Dashboard} from './dashboard';

@Entity('role_dashboards')
export class RoleDashboard extends BaseEntity implements IRoleDashboard {

    @PrimaryGeneratedColumn()
    id: number;

    @Column()
    dashboard_id: number;

    @Column()
    role_id: number;

    @ManyToOne(type => Dashboard, dashboard => dashboard.id)
    @JoinColumn({name: 'dashboard_id'})
    dashboard: Dashboard;

    @ManyToOne(type => Role, role => role.id)
    @JoinColumn({name: 'role_id'})
    role: Role;


}

export interface IRoleDashboard {
    id?: number;
    role_id: number;
    dashboard_id: number;
    dashboard: Dashboard;
    role: Role;
}



