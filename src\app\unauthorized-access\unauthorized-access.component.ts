import {AfterViewInit, Component, OnInit} from '@angular/core';
import {NotificationsService} from '../pages/shared/services/notifications.service';
import {TranslateService} from '../pages/shared/services/translate.service';
import {HelperService} from '../services/helper.service';
import {environment as localEnvironment} from '../../environments/environment';
import {environment as productionEnvironment} from '../../environments/environment.prod';
import {Router} from '@angular/router';
import {AuthService} from '../services/auth.service';

@Component({
    selector: 'npa-unauthorized-access',
    templateUrl: './unauthorized-access.component.html',
    styleUrls: ['./unauthorized-access.component.styl']
})
export class UnauthorizedAccessComponent implements OnInit, AfterViewInit {
    helperService = HelperService;
    env = localEnvironment || productionEnvironment;

    constructor(public translate: TranslateService, private notification: NotificationsService, private router: Router, private auth: AuthService) {

    }

    ngOnInit() {
    }

    navigateToHome() {
        if (this.auth.isUserAuthenticated) {
            this.router.navigateByUrl(`${localStorage.getItem('lang')}/${this.auth.user.value.role.slug}/dashboard`);
        } else {
            this.router.navigateByUrl(`${localStorage.getItem('lang')}/login`);
        }
    }

    ngAfterViewInit() {
        this.notification.dismissLoading();
    }

}
