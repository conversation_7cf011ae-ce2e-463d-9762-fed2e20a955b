import { Component, Inject, OnInit } from "@angular/core";
import { MAT_DIALOG_DATA, MatDialogRef } from "@angular/material";
import { NotificationsService } from "../../../shared/services/notifications.service";
import { NPA_ALERTS } from "../../../shared/consts/messages";
import { UserListService } from "../user-list.service";
import { FormValidationService } from "../../../../services/form-validation.service";
import { HttpErrorResponse, HttpResponse } from "@angular/common/http";

@Component({
  selector: "npa-entity-dialog",
  templateUrl: "./entity-dialog.component.html",
  styleUrls: ["./entity-dialog.component.styl"],
})
export class EntityDialogComponent implements OnInit {
  entities = [];
  selected = [];
  searched = [];

  constructor(
    private dialogRef: MatDialogRef<EntityDialogComponent>,
    public formValidationService: FormValidationService,
    @Inject(MAT_DIALOG_DATA) public data,
    public notificationsService: NotificationsService,
    private userListService: UserListService
  ) {}

  ngOnInit() {
    if (this.data.length > 0) {
      this.data.forEach((e) => this.select(true, e));
    }
    this._load();
  }

  private _load() {
    this.notificationsService.startLoading();
    this.userListService.entities().subscribe(
      (data: HttpResponse<any>) => {
        this.entities = data.body;
        this.searched = data.body;
        this.notificationsService.dismissLoading();
      },
      (error: HttpErrorResponse) => {
        this.notificationsService.error(NPA_ALERTS.ERROR);
        console.error(error);
      }
    );
  }

  isSelected(entity): boolean {
    return this.selected.some((e) => e.id === entity.id);
  }

  search(text) {
    if (text == "") this.searched = this.entities;
    this.searched = this.entities.filter((e) => e.name_da.includes(text));
  }

  add() {
    this.dialogRef.close(this.selected);
  }

  select(checked, entity) {
    if (checked) {
      const exists = this.selected.find((e) => e.id == entity.id);
      if (exists == undefined) {
        this.selected.push(entity);
      }
    } else {
      this.selected = this.selected.filter((e) => e.id !== entity.id);
    }
  }
}
