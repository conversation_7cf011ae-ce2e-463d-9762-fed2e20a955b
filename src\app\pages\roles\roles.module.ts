import {NgModule} from '@angular/core';
import {CommonModule} from '@angular/common';
import {
    MatButtonModule,
    MatCardModule,
    MatCheckboxModule,
    MatDatepickerModule,
    MatDialogModule,
    MatExpansionModule,
    MatFormFieldModule,
    MatIconModule,
    MatInputModule,
    MatNativeDateModule,
    MatPaginatorModule,
    MatRadioModule,
    MatSelectModule,
    MatTableModule,
    MatToolbarModule,
    MatTooltipModule
} from '@angular/material';
import {FormsModule, ReactiveFormsModule} from '@angular/forms';
import {SharedModule} from '../shared/shared.module';
import {FlexLayoutModule} from '@angular/flex-layout';
import {NgxEchartsModule} from 'ngx-echarts';
import {rolesComponents, RolesRoutes} from './roles.routes';

@NgModule({
    imports: [
        CommonModule,
        FormsModule,
        RolesRoutes,
        MatCardModule,
        MatTableModule,
        MatToolbarModule,
        MatIconModule,
        MatButtonModule,
        MatExpansionModule,
        FlexLayoutModule,
        MatFormFieldModule,
        MatInputModule,
        MatRadioModule,
        MatSelectModule,
        MatButtonModule,
        MatCheckboxModule,
        MatPaginatorModule,
        MatNativeDateModule,
        MatDatepickerModule,
        ReactiveFormsModule,
        MatTooltipModule,
        MatDialogModule,
        NgxEchartsModule,
        SharedModule,
    ],
    declarations: [
        ...rolesComponents
    ]
})
export class RolesModule {
}
