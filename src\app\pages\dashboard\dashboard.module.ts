import {CommonModule} from '@angular/common';
import {FormsModule, ReactiveFormsModule} from '@angular/forms';
import {NgModule} from '@angular/core';
import {dashboardComponents, DashboardRoutes} from './dashboard.routes';
import {
    MatButtonModule,
    MatCardModule,
    MatCheckboxModule,
    MatChipsModule,
    MatDatepickerModule,
    MatDialogModule,
    MatExpansionModule,
    MatFormFieldModule,
    MatIconModule,
    MatInputModule,
    MatNativeDateModule,
    MatPaginatorModule,
    MatRadioModule,
    MatSelectModule,
    MatTableModule,
    MatToolbarModule,
    MatTooltipModule,
} from '@angular/material';
import {FlexLayoutModule} from '@angular/flex-layout';
import {SharedModule} from '../shared/shared.module';
import {NgxEchartsModule} from 'ngx-echarts';
import {UserListService} from './user-list/user-list.service';
import {AddEditDialogComponent} from './user-list/add-edit-dialog/add-edit-dialog.component';
import {DashboardSystemListComponent} from './dashboard-system-list/dashboard-system-list.component';
import {DashboardSystemListService} from './dashboard-system-list/dashboard-system-list.service';
import {ViewDialogComponent} from './dashboard-system-list/view-dialog/view-dialog.component';
import { EntityDialogComponent } from './user-list/entity-dialog/entity-dialog.component';


@NgModule({
    imports: [
        CommonModule,
        FormsModule,
        DashboardRoutes,
        MatCardModule,
        MatTableModule,
        MatToolbarModule,
        MatIconModule,
        MatButtonModule,
        MatExpansionModule,
        FlexLayoutModule,
        MatFormFieldModule,
        MatInputModule,
        MatRadioModule,
        MatSelectModule,
        MatButtonModule,
        MatCheckboxModule,
        MatPaginatorModule,
        MatNativeDateModule,
        MatDatepickerModule,
        ReactiveFormsModule,
        MatTooltipModule,
        MatDialogModule,
        NgxEchartsModule,
        MatChipsModule,
        SharedModule
    ],
    declarations: [
        ...dashboardComponents,
        AddEditDialogComponent,
        DashboardSystemListComponent,
        ViewDialogComponent,
        EntityDialogComponent
    ],
    providers: [
        UserListService,
        DashboardSystemListService,
    ],
    entryComponents: [
        AddEditDialogComponent,
        ViewDialogComponent,
        EntityDialogComponent
    ],
})

export class DashboardModule {
    constructor() {
    }
}
