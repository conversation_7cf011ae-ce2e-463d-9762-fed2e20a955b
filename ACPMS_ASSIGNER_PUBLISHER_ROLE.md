# ACPMS Assigner Publisher Role Implementation

## Overview

This document describes the implementation of the new composite role `acpms-assigner-publisher` that inherits permissions from both `cpms-award-authority` (ID: 2) and `cpms-contract-director` (ID: 24) roles.

## Role Details

### Basic Information
- **Name**: assigner-publisher
- **Slug**: acpms-assigner-publisher
- **Party**: pe (Procurement Entity)
- **System ID**: 2 (ACPMS - Afghanistan Contract Progress and Monitoring System)
- **Can be acted**: true
- **Can act**: false
- **PE Based**: true
- **Sector Based**: false
- **Vendor Based**: false
- **Record Based**: false

### Source Roles
1. **cpms-award-authority** (ID: 2)
   - Existing role in the system
   - PE-based role for procurement entities
   
2. **cpms-contract-director** (ID: 24)
   - Will be created if it doesn't exist
   - PE-based role for contract management

## Implementation Files

### 1. Migration File
**File**: `app/migration/1700000000000-AddAcpmsAssignerPublisherRole.ts`

**Features**:
- Creates the `cpms-contract-director` role if it doesn't exist
- Creates the new composite role `acpms-assigner-publisher`
- Aggregates permissions from both source roles
- Handles permission deduplication
- Includes rollback functionality

### 2. Database Configuration
**File**: `app/config/database.ts`

**Changes**:
- Added import for the new migration
- Added migration to the Migrations array

### 3. User Controller Updates
**File**: `app/user/user.controller.ts`

**Changes**:
- Added `acpms-assigner-publisher` to allowed role slugs in `createACPMSUser`
- Added role handling in user creation switch statements
- Added role handling in `updateUserRole` method
- Supports procurement entity assignment for the new role

## Permission Aggregation

The composite role inherits ALL permissions from both source roles:

1. **From cpms-award-authority (ID: 2)**:
   - All context accesses with operation permissions
   - Dashboard and page access rights
   - Award authority specific permissions

2. **From cpms-contract-director (ID: 24)**:
   - All context accesses with operation permissions
   - Contract director specific permissions
   - Management and oversight capabilities

3. **Deduplication**:
   - The migration ensures no duplicate permissions
   - Uses `NOT EXISTS` clause to prevent conflicts

## USM Integration

### User Creation
The role is fully integrated with the USM (User and Session Manager) module:

1. **API Endpoint**: Uses existing `createACPMSUser` endpoint
2. **Automatic USM Role**: Users get the default "user" role (ID: 9) automatically
3. **Procurement Entity Support**: Supports `role_data.procurement_entities` assignment

### Example API Call
```json
{
  "api_password": "AABBCC!!@@##112233",
  "role_slug": "acpms-assigner-publisher",
  "status_slug": "active",
  "name": "John",
  "last_name": "Doe",
  "username": "johndoe",
  "email": "<EMAIL>",
  "phone_number": "93701234567",
  "role_data": {
    "procurement_entities": [
      {"procurement_entity_id": 1},
      {"procurement_entity_id": 2}
    ]
  }
}
```

## Migration Execution

### Running the Migration
```bash
# Using npm
npm run migration:run

# Using yarn
yarn migration:run

# Direct TypeORM command
npx typeorm migration:run
```

### Rollback (if needed)
```bash
# Using TypeORM
npx typeorm migration:revert
```

## Verification Steps

After running the migration, verify:

1. **Role Creation**:
   ```sql
   SELECT * FROM roles WHERE slug = 'acpms-assigner-publisher';
   ```

2. **Permission Count**:
   ```sql
   SELECT COUNT(*) FROM context_accesses 
   WHERE role_id = (SELECT id FROM roles WHERE slug = 'acpms-assigner-publisher');
   ```

3. **Source Role Permissions**:
   ```sql
   -- Award Authority permissions
   SELECT COUNT(*) FROM context_accesses WHERE role_id = 2;
   
   -- Contract Director permissions (if exists)
   SELECT COUNT(*) FROM context_accesses 
   WHERE role_id = (SELECT id FROM roles WHERE slug = 'cpms-contract-director');
   ```

## Security Considerations

1. **Permission Inheritance**: The role inherits ALL permissions from both source roles
2. **No Privilege Escalation**: Follows existing role patterns and constraints
3. **Audit Trail**: Migration includes logging for tracking changes
4. **Rollback Safety**: Complete rollback functionality implemented

## Monitoring and Maintenance

### Monitoring
- Monitor role usage through user_roles table
- Track permission usage through context_accesses
- Log user creation and role assignments

### Maintenance
- Regular permission audits
- Update role permissions when source roles change
- Monitor for any permission conflicts

## Troubleshooting

### Common Issues
1. **Migration Fails**: Check if source roles exist
2. **Permission Conflicts**: Verify deduplication logic
3. **User Creation Fails**: Ensure role_data format is correct

### Debug Queries
```sql
-- Check role exists
SELECT * FROM roles WHERE slug IN ('cpms-award-authority', 'cpms-contract-director', 'acpms-assigner-publisher');

-- Check permissions
SELECT r.slug, COUNT(ca.id) as permission_count 
FROM roles r 
LEFT JOIN context_accesses ca ON r.id = ca.role_id 
WHERE r.slug IN ('cpms-award-authority', 'cpms-contract-director', 'acpms-assigner-publisher')
GROUP BY r.slug;

-- Check users with the role
SELECT u.username, u.email, r.slug 
FROM users u 
JOIN user_roles ur ON u.id = ur.user_id 
JOIN roles r ON ur.role_id = r.id 
WHERE r.slug = 'acpms-assigner-publisher';
```

## Contact

For questions or issues related to this implementation, please contact the development team or refer to the project documentation.
