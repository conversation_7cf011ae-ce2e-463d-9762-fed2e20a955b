import {MigrationInterface, QueryRunner} from 'typeorm';
import {HashService} from '../services/hash.service';
import * as env from 'dotenv';
import * as path from 'path';

env.load({path: path.resolve(__dirname, '../.env')});

export class InitialSeed1546235144497 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<any> {
        await queryRunner.query(`
            INSERT INTO
              statuses (id, slug, description)
            VALUES
              (1 ,'active', 'means user is active'),
              (2 ,'in-active', 'means user is in-active'),
              (3 ,'suspended', 'means user is suspended'),
              (4 ,'disable', 'means user is disable')`);

        await queryRunner.query(`
            INSERT INTO
              users (id, name, last_name, username, password, email, phone_number, status_id)
            VALUES
              (1, 'admin', 'admin', 'admin', '${await HashService.generate('123456789')}', '<EMAIL>', '93755555555', 1)`);
        await queryRunner.query(`
            INSERT INTO
              systems (id, slug, url, description, icon_name)
            VALUES
              (1, 'usm', '${process.env.CUSTOM_BACK_END_BASE_URL}', 'User and Session Manager', 'code'),
              (2, 'acpms', '${process.env.CUSTOM_ACPMS_BASE_URL}', 'Afghanistan Contract Progress and Monitoring System', 'assignment'),
              (3, 'avrcs', '${process.env.CUSTOM_AVRCS_BASE_URL}', 'Afghanistan Vendor Registration and Classification System', 'domain')
              `);

        await queryRunner.query(`
            INSERT INTO
              roles (id,name, slug, party, can_be_acted, can_act, is_pe_based, is_sector_based, is_vendor_based, is_record_based, system_id)
            VALUES
              (1, 'contract-manager', 'cpms-contract-manager', 'pe', true, false, false, false, false, true, 2),
              (2, 'award-authority','cpms-award-authority', 'pe', true, false, true, false, false, false, 2),
              (3, 'specialist','cpms-specialist', 'npa', true, false, false, false, false, true, 2),
              (4, 'cpmd-manager','cpms-cpm-manager', 'npa', true, false, false, true, false, false, 2),
              (5, 'company','cpms-company', 'vendor', true, false, false, false, true, false, 2),
              (7, 'cpmd-system-development','cpms-cpm-system-development', 'npa', false, true, false, true, false, false, 2),
              (8, 'dev-ops','dev-ops', 'npa', false, true, false, false, false, false, 1),
              (9, 'user','user', 'other', true, false, false, false, false, false, 1),
              (10, 'vendor-registrar','avrcs-vendor-registrar', 'vendor', true, false, false, false, true, false, 3),
              (11, 'vendor-information-approval','avrcs-vendor-information-approval', 'npa', true, false, false, false, false, true, 3)
              `);
              // (6, 'cpmd-director','cpms-cpm-director', 'npa', false, true, false, true, false, false, 2),
        await queryRunner.query(`
            INSERT INTO
              user_roles (id, user_id, role_id)
            VALUES
              (120000, 1, 1),
              (120001, 1, 8)`);
    }

    public async down(queryRunner: QueryRunner): Promise<any> {
        await queryRunner.query(`Delete from contexts`);
        await queryRunner.query(`Delete from user_roles`);
        await queryRunner.query(`Delete from roles`);
        await queryRunner.query(`Delete from systems`);
        await queryRunner.query(`Delete from users`);
        await queryRunner.query(`Delete from statuses`);
    }

}
