import {Injectable} from '@angular/core';
import {TranslateService as translateService} from '@ngx-translate/core';
import {Observable} from 'rxjs';

@Injectable()
export class TranslateService {
  private _lang;
  private _dir;
  translated;

  constructor(private translate: translateService) {
    if (localStorage.getItem('lang')) {
      this.lang = localStorage.getItem('lang');
    } else {
      this.lang = 'prs';
      localStorage.setItem('lang', 'prs');
    }
    translate.setDefaultLang('dari');
    if (localStorage.getItem('lang') === 'en') {
      this.translate.use('english');
    } else if (localStorage.getItem('lang') === 'prs') {
      this.translate.use('dari');
    } else if (localStorage.getItem('lang') === 'ps') {
      this.translate.use('pashto');
    }
    document.querySelector('html').setAttribute('lang', this.lang);
    this.dir = this.lang === 'en' ? 'ltr' : 'rtl';
  }

  public get lang() {
    return this._lang;
  }

  public set lang(val: string) {
    this._lang = val;
    localStorage.setItem('lang', val);
  }

  public get dir() {
    return this._dir;
  }

  public set dir(value: string) {
    this._dir = value;
  }

  normalize(key: string) {
    const keys = key.split('-');
    const value = [];
    for (let i = 0; i < keys.length; i++) {
      value[i] = keys[i].toUpperCase();
    }
    return value.join('_');
  }

  translateKey(value: any) {
    return this.translate.instant(value);
  }

  setDefaultLang(lang) {
    this.translate.setDefaultLang(lang);
  }

  use(lang): Observable<any> {
    return this.translate.use(lang);
  }

  get(value): Observable<any> {
    return this.translate.get(value);
  }

  onLangChange(): Observable<any> {
    return this.translate.onLangChange;
  }

}
