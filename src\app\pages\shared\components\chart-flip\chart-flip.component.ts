import {Component, ElementRef, EventEmitter, Input, OnInit, Output} from '@angular/core';

@Component({
    selector: 'npa-chart-flip',
    templateUrl: './chart-flip.component.html',
    styleUrls: ['./chart-flip.component.styl']
})
export class ChartFlipComponent implements OnInit {
    @Output() flipped = new EventEmitter();
    @Input() title;
    isFlipped;

    constructor(private _nativeContainer: ElementRef) {
    }

    ngOnInit() {
    }

    flip() {
        this.isFlipped = !this.isFlipped;
        this.flipped.emit(true);
    }

    downloadPng() {
        const canvasElement = this._nativeContainer.nativeElement.getElementsByTagName('canvas')[0];

        const context = canvasElement.getContext('2d');
        const originalImageData = context.getImageData(0, 0, canvasElement.width, canvasElement.height);
        const data = originalImageData.data;
        for (let i = 0; i < data.length; i += 4) {
            if (data[i + 3] < 80) {
                data[i] = 255 - data[i];
                data[i + 1] = 255 - data[i + 1];
                data[i + 2] = 255 - data[i + 2];
                data[i + 3] = 255;
            }
        }
        context.putImageData(originalImageData, 0, 0);
    }

    downloadCsv() {
    }

}
