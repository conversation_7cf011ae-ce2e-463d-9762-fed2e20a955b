import {Component, OnInit} from '@angular/core';
import {<PERSON>bstractControl, FormBuilder, FormGroup, ValidatorFn, Validators} from '@angular/forms';
import {ActivatedRoute, Params, Router} from '@angular/router';
import {NotificationsService} from '../pages/shared/services/notifications.service';
import {RecoverPasswordFormService} from './recover-password-form.service';
import {NPA_ALERTS} from '../pages/shared/consts/messages';
import {AuthService} from '../services/auth.service';
import {TranslateService} from '../pages/shared/services/translate.service';
import {FormValidationService} from '../services/form-validation.service';

@Component({
    selector: 'npa-recover-password-form',
    templateUrl: './recover-password-form.component.html',
    styleUrls: ['../login/login.component.styl']
})
export class RecoverPasswordFormComponent implements OnInit {

    form: FormGroup;
    token: Params;
    isLinkValid: boolean;
    lang: string;

    constructor(private _formBuilder: FormBuilder,
                public formValidationService: FormValidationService,
                private _activatedRoute: ActivatedRoute,
                public translate: TranslateService,
                private _authService: AuthService,
                private _recoverPasswordFormService: RecoverPasswordFormService,
                private _router: Router,
                public notificationsService: NotificationsService) {
    }

    ngOnInit() {
        this.lang = localStorage.getItem('lang');
        this.notificationsService.startLoading();
        this.getToken();
        this._recoverPasswordFormService.isLinkValid(this.token)
            .subscribe((res) => {
                this.notificationsService.dismissLoading();
                this.isLinkValid = res.isTokenValidated;
                this.initForm();
            }, (error) => {
                this.notificationsService.dismissLoading();
                this.isLinkValid = false;
                this.initForm();
                console.error(error);
            });
    }

    initForm() {
        this.form = this._formBuilder.group({
            new_password: [
                null, [
                    this.formValidationService.required.validator,
                    Validators.pattern(this._authService.passwordPattern)
                ]
            ],
            new_password_confirmation: [
                null, [
                    this.formValidationService.required.validator,
                    Validators.pattern(this._authService.passwordPattern),
                    this.RepeatPassword('new_password')
                ],
            ]
        });
    }

    changeLocalStorageLang(param) {
        localStorage.setItem('lang', param);
    }

    submit(data: any) {
        data['token'] = this.token;
        this.notificationsService.startLoading();
        this._recoverPasswordFormService.update(data).subscribe((res) => {
            this.notificationsService.success(this.translate.translateKey('PASSWORD_SUCCESSFULLY_CREATED'));
            setTimeout(() => {
                this._router.navigate([this.translate.lang + '/login']);
            }, 2000);
        }, (error) => {
            console.log(error.error);
            console.log(this.translate.translateKey('RECOVER_PASSWORD_EXPIRED_LINK_MESSAGE'));
            if (error.error.name === 'TokenExpiredError') {
                this.notificationsService.error(this.translate.translateKey('RECOVER_PASSWORD_EXPIRED_LINK_MESSAGE'));
                this.notificationsService.dismissLoading();
                setTimeout(() => {
                    this._router.navigate([this.translate.lang + '/login']);
                }, 2000);
            } else {
                this.notificationsService.error(NPA_ALERTS.ERROR);
            }
            console.error(error);
        });
    }

    getToken() {
        this._activatedRoute.queryParamMap.subscribe((params: Params) => {
            this.token = params['params']['t'];
        });
    }

    RepeatPassword(fieldName: string): ValidatorFn {
        return (control: AbstractControl): { [key: string]: any } | null => {
            const otherControl: AbstractControl = control.root.get(fieldName);
            return (otherControl && control.value !== otherControl.value) ? {match: true} : null;
        };
    }
}
