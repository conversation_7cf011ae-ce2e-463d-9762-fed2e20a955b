import {Component, ElementRef, OnInit, ViewChild} from '@angular/core';
import {AlertService} from './alert/alert.service';
import {NotificationsService} from './shared/services/notifications.service';
import {SocketService} from './shared/services/socket.service';
import {TranslateService} from './shared/services/translate.service';

@Component({
    selector: 'npa-pages',
    templateUrl: './pages.component.html',
    styleUrls: ['./pages.component.styl']
})
export class PagesComponent implements OnInit {

    @ViewChild('contents') el: ElementRef;
    isSidebarMainClose = false;
    isSidebarUtilitiesClose = true;
    scrollTop = 0;
    lang: string;

    constructor(private _socketService: SocketService,
                private _alertService: AlertService,
                private _notificationsService: NotificationsService,
                private translate: TranslateService) {
    }

    ngOnInit(): void {
        this._socketService.connect();
        this._alertService.registerAppRealTimeStates();
        this._notificationsService.registerDesktopNotification();
        this._recordApplicationLoadTime();
        this.lang = this.translate.lang;
    }

    private _recordApplicationLoadTime(): void {
        localStorage.setItem('states.lastLoadedOn', (new Date()).getTime() + '');
    }

    toggleMainSidebar(value: boolean) {
        this.isSidebarMainClose = value;
    }

    toggleModulesSidebar(values: boolean) {
        this.isSidebarUtilitiesClose = values;
    }

    calculateScrollTop() {
        this.scrollTop = this.el.nativeElement.scrollTop;
        if (this.scrollTop < 140) {
            this.el.nativeElement.style.padding_top = 140 - this.scrollTop + 'px';
        }
    }

}
