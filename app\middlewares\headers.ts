export class HeadersMiddleware {
    public static accessControl(req, res, next) {
        res.header('Access-Control-Allow-Methods', 'GET, PUT, POST, DELETE, OPTIONS');
        res.header('Access-Control-Allow-Headers', 'Content-Type, x-xsrf-token, Authorization, Accept, X-Requested-With, Application');
        res.header('Access-Control-Expose-Headers', 'Authorization, location, x-pagination-size');
        next();
    }
}
