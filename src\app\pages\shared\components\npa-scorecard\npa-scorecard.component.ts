import {Component, Input, OnChanges, OnInit} from '@angular/core';

@Component({
    selector: 'npa-scorecard',
    templateUrl: './npa-scorecard.component.html',
    styleUrls: ['./npa-scorecard.component.styl']
})
export class NpaScorecardComponent implements OnInit, OnChanges {
    isFlipped = false;
    smallSize = false;
    @Input() iconName;
    @Input() bottomSpace;
    @Input() text;
    @Input() subText;
    size;

    constructor() {
    }

    ngOnInit() {
    }

    ngOnChanges() {
        this.setFontSize();
    }

    setFontSize() {
        if (this.text) {
            length = this.text.toString().length;
            switch (true) {
                case (length <= 5):
                    this.size = 60 + 'px';
                    break;
                case (length <= 7):
                    this.size = 50 + 'px';
                    break;
                case (length <= 10):
                    this.size = 35 + 'px';
                    break;
                case (length <= 15):
                    this.size = 30 + 'px';
                    break;
                case (length <= 20):
                    this.size = 25 + 'px';
                    break;
                case (length <= 30):
                    this.size = 22 + 'px';
                    break;
                case (length <= 45):
                    this.size = 20 + 'px';
                    break;
            }
        }
    }

    getFontSize(container: HTMLDivElement): string {
        const length = container.children[0]['innerText'].length;
        const innerText = +container.children[0]['innerText'];
        if (isNaN(innerText)) {
            switch (true) {
                case (length <= 5):
                    return 60 + 'px';
                case (length <= 7):
                    return 50 + 'px';
                case (length <= 10):
                    return 45 + 'px';
                case (length <= 15):
                    return 40 + 'px';
                case (length <= 30):
                    return 30 + 'px';
                case (length <= 45):
                    return 20 + 'px';

            }
        } else {
            switch (true) {
                case (length <= 5):
                    return 60 + 'px';
                case (length <= 8):
                    setTimeout(function () {
                        this.smallSize = true;
                    }, 0);
                    return 50 + 'px';
                case (length <= 10):
                    setTimeout(function () {
                        this.smallSize = true;
                    }, 0);
                    return 30 + 'px';
                case (length <= 30):
                    setTimeout(function () {
                        this.smallSize = true;
                    }, 0);
                    return 20 + 'px';
            }

        }

    }

    getInfoWidth(container: HTMLDivElement): string {
        const length = container.children[0]['innerText'].length;
        if (length === 0 && !this.bottomSpace) {
            return -15 + 'px';
        }
        return 0 + 'px';

    }

}
