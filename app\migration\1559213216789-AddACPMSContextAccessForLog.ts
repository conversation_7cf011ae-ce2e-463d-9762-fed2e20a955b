import {MigrationInterface, QueryRunner} from "typeorm";

export class AddACPMSContextAccessForLog1559213216789 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<any> {
        await queryRunner.query(`
            insert into
            context_accesses (operation_id, is_enabled, role_id, context_id)
            values (1, 1, 1, (select id from contexts where slug = "system-management")),
            (1, 1, 2, (select id from contexts where slug = "system-management")),
            (1, 1, 3, (select id from contexts where slug = "system-management")),
            (1, 1, 5, (select id from contexts where slug = "system-management")),
            (1, 1, 6, (select id from contexts where slug = "system-management")),
            (1, 1, 7, (select id from contexts where slug = "system-management")),
            (1, 1, 19, (select id from contexts where slug = "system-management"))
            `);
    }

    public async down(queryRunner: QueryRunner): Promise<any> {
    }

}
