import {Component, EventEmitter, Input, OnInit, Output} from '@angular/core';
import {FormControl, FormGroup, Validators} from '@angular/forms';
import {MatDialog} from '@angular/material';
import {FileDownloadDialogComponent} from '../file-download/file-download-dialog/file-download-dialog.component';
import {DropDownsService} from '../../../../services/dropDowns.service';
import {TranslateService} from '../../services/translate.service';
import {FormValidationService} from '../../../../services/form-validation.service';

interface IFile {
    name: string;
    size: number;
    type: string;
    value: string;
}

interface IUpload {
    name: string;
    date: string;
}

@Component({
    selector: 'npa-file-uploader',
    templateUrl: './file-uploader.component.html',
    styleUrls: ['./file-uploader.component.styl']
})
export class FileUploaderComponent implements OnInit {
    nativeInputFile;
    @Input() isMultiple = false;
    @Input() uploadLogs: IUpload[] = [];
    @Input() npaModel: FormControl;
    @Input() uploadNumber: boolean;
    @Input() npaForm: FormGroup;
    @Output() npaFormChange = new EventEmitter<FormGroup>();
    @Output() npaModelChange = new EventEmitter<FormControl>();
    isSelected = false;
    language = [];
    languages = [];
    otherFiles = [];
    showAddButton = 1;
    isControlSet = false;

    constructor(private _dialog: MatDialog,
                private dropDownService: DropDownsService,
                private translate: TranslateService,
                public formValidationService: FormValidationService) {
    }

    ngOnInit() {

        // console.log(this.uploadNumber);
        // console.log(this.npaForm.value);
        this.languages = LANGUAGE;
        let count = 0;
        for (const formValueKey in this.npaForm.value) {
            if (formValueKey.includes('other_')) {
                const index = formValueKey.split('_')[1];
                this.addNewFileUpload(index);
                count++;
            }
        }
        if (count === 0) {
            this.addNewFileUpload(1);
        }
    }


    selectFile(index, isUpload?): void {
        // this.nativeInputFile.click();
        console.log(isUpload);
        console.log('select File:', index);
        document.getElementById('inputFile_' + index).click();
        console.log( document.getElementById('inputFile_' + index));

    }

    onFileChange(event, index) {
        const control = 'other_' + index;
        const reader = new FileReader();
        if (event.target.files && event.target.files.length > 0) {
            const file: File = <File>event.target.files[0];
            this.isSelected = true;
            const selectedFile = {};
            selectedFile['language_id'] = this.language[index].value;
            selectedFile['name'] = file.name;
            selectedFile['size'] = file.size;
            selectedFile['type'] = file.type;
            selectedFile['file'] = file;
            this.npaForm.get(control).setValue(selectedFile);
            this.npaFormChange.emit(this.npaForm);
            // this.npaModel.setValue(selectedFile);
            // this.npaModelChange.emit(this.npaModel);
        }
    }

    deleteUploadedFile(index) {
        console.log('deleteUplaodFile:', index);
        this.isSelected = false;
        this.nativeInputFile.nativeElement.value = '';
        this.npaForm.get('other_' + index).setValue('');
        this.npaFormChange.emit(this.npaForm);

    }

    openDialog() {
        if (this.uploadLogs) {
            const dialogRef = this._dialog.open(FileDownloadDialogComponent, {
                width: '600px',
                data: {
                    showDeleteOption: true,
                    files: this.uploadLogs
                }
            });
            dialogRef.afterClosed().subscribe(result => {
                const finalData = [];
                this.uploadLogs.forEach((row: any) => {
                    if (result !== row.id) {
                        finalData.push(row);
                    }
                });
                this.uploadLogs = finalData;
                if (this.uploadLogs.length === 0) {
                    this.uploadLogs = null;
                }
            });
        }
    }

    getFileNameWidth(container: HTMLDivElement): string {
        return (container.clientWidth - 89) + 'px';
    }


    addNewFileUpload(index) {
        if (index <= 1000) {
            this.otherFiles.push(index);
            this.showAddButton = index;
            this.language[index] = new FormControl('', [Validators.required]);
            this.npaForm.addControl('other_' + index, new FormControl(''));
            this.isControlSet = true;

        }
    }

    cancelFileUpload(index) {
        if (index <= 1000) {
            this.otherFiles.pop();
            this.showAddButton = index;
            this.npaForm.removeControl('other_' + index);
            this.showAddButton = index - 2;
        }
    }


}

const LANGUAGE = [
    {id: 1, name_da: 'دری', name_pa: 'دری', name_en: 'Dari', slug: 'prs', order: null, created_at: null},
    {id: 2, name_da: 'پشتو', name_pa: 'پښتو', name_en: 'Pashto', slug: 'ps', order: null, created_at: null},
    {id: 3, name_da: 'انگلیسی', name_pa: 'انګلیسی', name_en: 'English', slug: 'en', order: null},
];

