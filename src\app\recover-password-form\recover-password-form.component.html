<div class="H2SoFe LZgQXe TFhTPc">
    <div class="RAYh1e LZgQXe" id="initialView">
        <div class="xkfVF" role="presentation" tabindex="null">
            <div class="fctIrd"
                 data-can-enhance="true" data-oauth-third-party-logo-url="" aria-hidden="true">
                <div id="logo" class="SSBsw" title="AGEOPS">
                    <div class="qZp31e">
                        <a [routerLink]="['/', lang, 'login']"> <img width="94" height="57" src="assets/images/ageopsLogoG.svg"></a>
                    </div>
                </div>
            </div>
            <div id="view_container" class="JhUD8d SQNfcc vLGJgb">
                <div class="DRS7Fe bxPAYd k6Zj8d" role="presentation">
                    <div class="jXeDnc">
                        <h1 data-a11y-title-piece="" id="headingText">
                            <content>{{ "WELCOME" | translate }}</content>
                        </h1>
                        <div class="Y4dIwd" id="headingSubtext">
                            <content>{{ "TO_AGEOPS" | translate }}</content>
                        </div>
                        <form *ngIf="form" [formGroup]="form" novalidate (ngSubmit)="submit(form.value)"
                              fxLayout="column"
                              fxLayoutAlign="center">
                            <span class="header">{{'RECOVER_PASSWORD'|translate}}</span>
                            <div fxFlex="100" fxLayout="column">
                                <mat-form-field class="inputs" fxFlex="100">
                                    <input type="password" matInput placeholder="{{'NEW_PASSWORD'|translate}}"
                                           formControlName="new_password"
                                           lang="en">
                                    <mat-error *ngIf="form.get('new_password')?.errors?.required">
                                        {{'REQUIRED_FIELD_MESSAGE'|translate}}
                                    </mat-error>
                                    <mat-error
                                            *ngIf="!form.get('new_password')?.valid && !form.get('new_password')?.errors?.required">
                                        {{'PASSWORD_PATTERN_INVALID_MESSAGE'|translate}}
                                    </mat-error>
                                </mat-form-field>
                                <mat-form-field class="inputs" fxFlex="100">
                                    <input type="password" matInput placeholder="{{'CONFIRM_NEW_PASSWORD'|translate}}"
                                           formControlName="new_password_confirmation"
                                           lang="en">
                                    <mat-error *ngIf="form.get('new_password_confirmation')?.errors?.required">
                                        {{'REQUIRED_FIELD_MESSAGE'|translate}}
                                    </mat-error>
                                    <mat-error *ngIf="form?.get('new_password_confirmation')?.errors?.match">
                                        {{'PASSWORD_NOT_MATCH_MESSAGE'|translate}}
                                    </mat-error>
                                </mat-form-field>
                            </div>
                            <br>
                            <div class="action" fxFlex="100" fxLayout="column" fxLayoutAlign="center">
                                <button fxFlex="100" class="submit" mat-raised-button type="submit"
                                        [disabled]="!form.valid || notificationsService.isLoading"
                                        color="primary">{{'CONFIRM'|translate}}
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        <footer class="RwBngc">
            <ul class="Bgzgmd">
                <li><a (click)="changeLocalStorageLang('prs')" href="prs/auth/recover-password-form">دری</a></li>
                <li><a (click)="changeLocalStorageLang('ps')" href="ps/auth/recover-password-form">پښتو</a></li>
                <li><a (click)="changeLocalStorageLang('en')" href="en/auth/recover-password-form">English</a></li>
            </ul>
        </footer>
    </div>
</div>


