<div fxLayout="row wrap" fxFlexAlign="start">
    <div fxFlex="95">مشاهده معلومات عمومی</div>
    <div fxFlex="5">
        <button mat-dialog-close mat-icon-button matTooltip="بستن">
            <mat-icon>close</mat-icon>
        </button>
    </div>
</div>
<mat-dialog-content class="dialog" fxLayout="row wrap" fxLayoutAlign="start">
        <div fxLayout="row wrap" fxLayoutAlign="start" fxFlex="100">
            <ng-container>
                <npa-view-element fxFlex="33">
                    <div class="npa-label">{{"USER_IDENTIFICATION_NUMBER" |translate}}</div>
                    <div class="npa-value">{{data?.id}}</div>
                </npa-view-element>
                <npa-view-element fxFlex="33">
                    <div class="npa-label">{{"COMPLAINANT"|translate}}</div>
                    <div class="npa-value">{{data?.name}}</div>
                </npa-view-element>
                <npa-view-element fxFlex="33">
                    <div class="npa-label">{{"COMPLAINANT_PHONE_NUMBER"|translate}}</div>
                    <div class="npa-value">{{data?.phone}}</div>
                </npa-view-element>
                <npa-view-element fxFlex="33">
                    <div class="npa-label">{{"COMPLAINANT_EMAIL_ADDRESS"|translate}}</div>
                    <div class="npa-value">{{data?.email}}</div>
                </npa-view-element>
                <npa-view-element fxFlex="33">
                    <div class="npa-label">{{"RELATED_PROJECT" |translate}}</div>
                    <div class="npa-value">{{data?.related_project}}</div>
                </npa-view-element>
                <npa-view-element fxFlex="33">
                    <div class="npa-label">{{"RELATED_COMPANY"|translate}}</div>
                    <div class="npa-value">{{data?.company}}</div>
                </npa-view-element>
                <npa-view-element fxFlex="33">
                    <div class="npa-label">{{"PROCUREMENT_TYPE"|translate}}</div>
                    <div class="npa-value">{{data?.procurement_type}}</div>
                </npa-view-element>
                <npa-view-element fxFlex="33">
                    <div class="npa-label">{{"COMPLAINT_TYPE"|translate}}</div>
                    <div class="npa-value">{{data?.type}}</div>
                </npa-view-element>
                <npa-view-element fxFlex="33">
                    <div class="npa-label">{{"BIDDING_DESCRIPTION"|translate}}</div>
                    <div class="npa-value">{{data?.bidding_description}}</div>
                </npa-view-element>
                <npa-view-element fxFlex="33">
                    <div class="npa-label">{{"PROCUREMENT_ENTITY"|translate}}</div>
                    <div class="npa-value">{{data?.procurement_entity}}</div>
                </npa-view-element>
                <npa-view-element fxFlex="90">
                    <div class="npa-label">{{"COMPLAINT_DESCRIPTION"|translate}}</div>
                    <div class="npa-value">{{data?.description}}</div>
                </npa-view-element>
                <npa-view-element fxFlex="90">
                    <div class="npa-label">{{"UPLOADED_DOCUMENTS"|translate}}</div>
                    <div class="npa-value">{{data?.description}}</div>
                </npa-view-element>
            </ng-container>
        </div>
</mat-dialog-content>
<mat-dialog-actions>
    <button mat-raised-button [mat-dialog-close]="true">بستن</button>
</mat-dialog-actions>

