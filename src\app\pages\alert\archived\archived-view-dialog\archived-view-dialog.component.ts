import {Component, Inject, OnInit} from '@angular/core';
import {FormBuilder, FormGroup} from '@angular/forms';
import {MAT_DIALOG_DATA, MatDialogRef} from '@angular/material';
import {FormValidationService} from '../../../../services/form-validation.service';

@Component({
    selector: 'npa-archived-view-dialog',
    templateUrl: './archived-view-dialog.component.html',
    styleUrls: ['./archived-view-dialog.component.styl']
})
export class ArchivedViewDialogComponent implements OnInit {

    archivedViewDialog: FormGroup;

    constructor(public dialogRef: MatDialogRef<ArchivedViewDialogComponent>,
                @Inject(MAT_DIALOG_DATA) public data: any,
                private formBuilder: FormBuilder,
                public formValidationService: FormValidationService) {
        this.createForm();
    }

    createForm() {
        this.archivedViewDialog = this.formBuilder.group({
            title: ['', this.formValidationService.required.validator],
            message: ['', this.formValidationService.required.validator],
        });
    }

    save() {
        this.dialogRef.close(this.data);
    }

    ngOnInit() {
    }

}
