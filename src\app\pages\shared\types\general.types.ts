export interface IDropdown {
    id?: number;
    name_da?: string;
    name_pa?: string;
    name_en?: string;
    slug?: string;
}

export interface ICheckbox {
    id?: number;
    name_da?: string;
    name_pa?: string;
    name_en?: string;
    slug?: string;
}

export interface IZone {
    id: number;
    name_da: string;
    name_pa: string;
    name_en: string;
    slug: string;
}

export interface IProvince {
    id: number;
    name_da: string;
    name_pa: string;
    name_en: string;
    slug: string;
    zone_id?: number;
    zone?: IZone;
}

export interface IDistrict {
    id: number;
    name_da: string;
    name_pa: string;
    name_en: string;
    slug: string;
    province_id?: number;
    province?: IProvince;
}

export interface IScheduleListItem {
    year: number;
    month?: IDropdown;
    month_id?: number;
}

export interface IForm {
    isValid: () => boolean;
    submit: (data?: any) => void;
    initForm: (data?: any) => void;
}
