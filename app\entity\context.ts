import {BaseEntity, <PERSON>umn, <PERSON>tity, Index, <PERSON>in<PERSON><PERSON>umn, ManyToOne, OneToMany, PrimaryGeneratedColumn} from 'typeorm';
import {System} from './system';
import {ContextAccess} from './context-access';

@Entity('contexts')
@Index('unique_slug', ['slug'], {unique: true})

export class Context extends BaseEntity implements IContext {

    @PrimaryGeneratedColumn()
    id: number;

    @Column({unique: true})
    slug: string;

    @Column()
    description: string;

    @Column()
    url: string;

    @Column({name: 'is_dashboard', default: false})
    is_dashboard: boolean;

    @Column({name: 'system_id'})
    system_id: number;

    @ManyToOne(type => System, system => system.id)
    @JoinColumn({name: 'system_id'})
    system: System;

    @OneToMany(type => ContextAccess, contextAccess => contextAccess.context)
    contextAccesses: ContextAccess[];
}

export interface IContext {
    id: number;
    slug: string;
    description: string;
    url: string;
    system_id: number;
    is_dashboard: boolean;
    system: any | System;
}



