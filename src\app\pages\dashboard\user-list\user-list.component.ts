import {Component, OnInit, ViewChild} from '@angular/core';
import {<PERSON><PERSON>uilder, FormGroup} from '@angular/forms';
import {AuthService} from '../../../services/auth.service';
import {NpaDatabase, NpaDataSource} from '../../shared/classes/npa-table';
import {MatDialog, MatPaginator} from '@angular/material';
import {ComplaintService} from '../../../services/complaint.service';
import {ActivatedRoute, Router} from '@angular/router';
import {NotificationsService} from '../../shared/services/notifications.service';
import {ConfirmationAndJustificationService} from '../../shared/services/confirmation-and-justification.service';
import {IComplaintList} from './user-list.types';
import {TranslateService} from '../../shared/services/translate.service';
import {FormService} from '../../shared/services/form.service';
import {NPA_COMMON_DIALOG_WIDTH} from '../../shared/consts/sizes';
import {AddEditDialogComponent} from './add-edit-dialog/add-edit-dialog.component';
import {UserListService} from './user-list.service';
import { HttpErrorResponse, HttpResponse } from '@angular/common/http';
import { NPA_ALERTS } from '../../shared/consts/messages';

@Component({
  selector: 'npa-user-list',
  templateUrl: './user-list.component.html',
  styleUrls: ['./user-list.component.styl']
})
export class UserListComponent implements OnInit {

  displayedData = [];
  searched = []
  dataSource: NpaDataSource;
  database: NpaDatabase;
  form: FormGroup;
  lang: string;
  referenceTitles: string[] = [];
  chosenTitles: string[] = [];
  @ViewChild(MatPaginator) paginator: MatPaginator;
  dir: string;


  constructor(private formBuilder: FormBuilder,
              public authService: AuthService,
              public formService: FormService,
              public userListService: UserListService,
              private activatedRoute: ActivatedRoute,
              private router: Router,
              private dialog: MatDialog,
              public notificationsService: NotificationsService,
              private confirmationAndJustificationService: ConfirmationAndJustificationService,
              private translateService: TranslateService) {
  }

  ngOnInit() {
    this.lang = this.translateService.lang;
    this.dir = this.translateService.dir;
    this.initForm();
    this._loadList()
    this.notificationsService.dismissLoading();
    this.referenceTitles = this.userListService.getDisplayedColumns(
      this.authService.user.value.role.slug,
      this.authService.loggedInUser.role.slug
    );

    this.chosenTitles = this.referenceTitles.slice();

    this.database = new NpaDatabase(this.displayedData);
    this.dataSource = new NpaDataSource(this.database);

  }

  search(text){
    if(text == ''){
      this.searched  = this.displayedData
      this.database = new NpaDatabase(this.displayedData);
      this.dataSource = new NpaDataSource(this.database);
    }else{
      this.searched = this.displayedData.filter(d => d.email.includes(text))
      this.database = new NpaDatabase(this.searched);
      this.dataSource = new NpaDataSource(this.database);
    }

  }

  private _loadList(pageIndex = 0, pageSize = 50) {
    this.notificationsService.startLoading();
    this.userListService.list({pageIndex, pageSize}).subscribe(
      (data: HttpResponse<any>) => {

          this.displayedData = data.body[0]
          this.searched = data.body[0]
        // this.paginator.length = +data.headers.get('x-pagination-size');
        this.database = new NpaDatabase(this.displayedData);
        this.dataSource = new NpaDataSource(this.database);
        if (data.body.length === 0) {
          this.notificationsService.info('هیچ کاربری یافت نشد.');
        } else {
            this.paginator.pageSize = pageSize;
            this.paginator.pageSizeOptions = [50, 100, 300, 500];
            this.paginator.length = data.body[1]
            this.paginator.page.subscribe(({pageIndex, pageSize}) => {
              this._loadList(pageIndex, pageSize)
            });
          this.notificationsService.dismissLoading();
        }
      },
      (error: HttpErrorResponse) => {
        this.notificationsService.error(NPA_ALERTS.ERROR);
        console.error(error);
      }
    );
  }

  initForm() {
    this.form = this.formBuilder.group({
      email: undefined,
    });
  }


  getWidth() {
    let total = 0;
    this.chosenTitles.forEach((current: string) => {
      total += this.userListService.getWidthMultiplicationFactor(current);
    });
    return (total * 150) + 'px';
  }

  openAddEditDialog(data = {}) {
    const dialogRef = this.dialog.open(AddEditDialogComponent, {
      width: NPA_COMMON_DIALOG_WIDTH,
      data: data,
      disableClose: true,
    });
    dialogRef.afterClosed().subscribe(result => {
      this._loadList();
      if (Object.keys(result).length === 0) {
        return;
      }
      if (this.database.find(result.id)) {
        this.database.edit(result);
        return;
      }
      this.database.add(result);
    });
  }
}
