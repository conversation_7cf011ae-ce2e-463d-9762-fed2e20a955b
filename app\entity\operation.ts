import {BaseEntity, Column, Entity, Index, PrimaryGeneratedColumn} from 'typeorm';

@Entity('operations')
@Index('unique_slug', ['slug'], {unique: true})

export class Operation extends BaseEntity implements IOperations {

    @PrimaryGeneratedColumn()
    id: number;

    @Column({unique: true})
    slug: string;

    @Column()
    name: string;

    @Column()
    description: string;

}

export interface IOperations {
    id: number;
    slug: string;
    name: string;
    description: string;
}



