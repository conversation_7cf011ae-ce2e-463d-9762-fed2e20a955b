export const SIMPLE_TREEMAP = {
    title: {
        text: '',
        x: 'center',
        textStyle: {
            fontFamily: 'XB Niloofar',
            color: '#333',
            fontWeight: 'normal',
            fontSize: 16,
            lineHeight: 20,
        }
    },
    color: [
        '#eab6ea',
        '#9b8bf9',
        '#6263ff',
        '#c679ff',
        '#d7a593',
        '#d8b36a',
        '#e4c869',
        '#d79c7c',
        '#d8c575',
    ],
    tooltip: {
        trigger: 'item',
        formatter: '{b} <br>{a} : {c}'
    },
    toolbox: {
        show: true,
        feature: {
            mark: {
                show: true,
                title: {
                    mark: 'Add Guide',
                    markUndo: 'Remove Guide',
                    markClear: 'Remove all Guide',
                },
            },
            dataView: {
                show: true,
                readOnly: false,
                optionToContent: null,
                contentToOption: null,
                title: 'Data View',
                lang: ['Data View', 'Shutdown', 'Refresh'],
                buttonColor: '#01579B'
            },
            restore: {
                show: true,
                title: 'Reset'
            },
            saveAsImage: {
                show: true,
                title: 'Save As Image'
            }
        }
    },
    calculable: false,
    series: [
        {
            name: '',
            type: 'treemap',
            itemStyle: {
                normal: {
                    label: {
                        show: true,
                        formatter: '{b}',
                        x: 120,
                        y: 20,
                        textStyle: {
                            color: 'white',
                            fontFamily: 'XB Niloofar',
                        }
                    },
                    borderWidth: 5
                },
                emphasis: {
                    label: {
                        show: false
                    }
                }
            },
            data: []
        }
    ]
};
export const DRILLDOWN_TREEMAP = {
    title: {
        text: '',
        x: 'center',
        textStyle: {
            fontFamily: 'XB Niloofar',
            color: '#333',
            fontWeight: 'normal',
            fontSize: 16,
            lineHeight: 20,
        }
    },
    color: [
        '#cccccc',
        '#838281',
        '#595b5d',
        '#ffa12d',
        '#004879',
        '#d8b36a',
        '#34b5d0',
        '#f0b82d',
        '#d8c575',
        '#5a7e92',
        '#e0daba',
        '#0d47a1',
        '#424242',
    ],
    tooltip: {},
    toolbox: {
        show: true,
        feature: {
            mark: {
                show: true,
                title: {
                    mark: 'Add Guide',
                    markUndo: 'Remove Guide',
                    markClear: 'Remove all Guide',
                },
            },
            dataView: {
                show: true,
                readOnly: false,
                optionToContent: null,
                contentToOption: null,
                title: 'Data View',
                lang: ['Data View', 'Shutdown', 'Refresh'],
                buttonColor: '#01579B'
            },
            restore: {
                show: true,
                title: 'Reset'
            },
            saveAsImage: {
                show: true,
                title: 'Save As Image'
            }
        }
    },
    series: [{
        name: 'option',
        type: 'treemap',
        visibleMin: 300,
        data: [],
        leafDepth: 1,
        levels: [
            {
                itemStyle: {
                    normal: {
                        borderColor: '#999',
                        borderWidth: 1,
                        gapWidth: 1
                    }
                }
            },
            {
                colorSaturation: [0.3, 0.6],
                itemStyle: {
                    normal: {
                        borderColorSaturation: 0.7,
                        borderWidth: 1
                    }
                }
            },
            {
                colorSaturation: [0.3, 0.5],
                itemStyle: {
                    normal: {
                        borderColorSaturation: 0.6,
                    }
                }
            },
            {
                colorSaturation: [0.3, 0.5]
            }
        ],
        itemStyle: {
            normal: {
                label: {
                    show: true,
                    formatter: '{b}',
                    x: 120,
                    y: 20,
                    textStyle: {
                        color: 'white',
                        fontFamily: 'XB Niloofar',
                    }
                },
            },
            emphasis: {
                label: {
                    show: false
                }
            }
        },
    }]
};

export const TREE_MAP_DATA = [
    {
        children: [
            {
                name: 'اجناس',
                value: 200
            },
            {
                name: 'ساختمان',
                value: 100
            },
            {
                name: 'خدمات مشورتی',
                value: 300
            },
            {
                name: 'خدمات غیر مشورتی',
                value: 50
            }
        ],
        name: 'وزارت فواید عامه'
    },
    {
        children: [
            {
                name: 'اجناس',
                value: 200
            },
            {
                name: 'ساختمان',
                value: 100
            },
            {
                name: 'خدمات مشورتی',
                value: 300
            },
            {
                name: 'خدمات غیر مشورتی',
                value: 50
            }
        ],
        name: 'وزارت امور خارجه'
    },
    {
        children: [
            {
                name: 'اجناس',
                value: 200
            },
            {
                name: 'ساختمان',
                value: 100
            },
            {
                name: 'خدمات مشورتی',
                value: 300
            },
            {
                name: 'خدمات غیر مشورتی',
                value: 50
            }
        ],
        name: 'وزارت امور داخله'
    },
    {
        children: [
            {
                name: 'اجناس',
                value: 200
            },
            {
                name: 'ساختمان',
                value: 100
            },
            {
                name: 'خدمات مشورتی',
                value: 300
            },
            {
                name: 'خدمات غیر مشورتی',
                value: 50
            }
        ],
        name: 'وزارت امور داخله'
    },
    {
        children: [
            {
                name: 'اجناس',
                value: 200
            },
            {
                name: 'ساختمان',
                value: 100
            },
            {
                name: 'خدمات مشورتی',
                value: 300
            },
            {
                name: 'خدمات غیر مشورتی',
                value: 50
            }
        ],
        name: 'وزارت امور داخله'
    },
    {
        children: [
            {
                name: 'اجناس',
                value: 200
            },
            {
                name: 'ساختمان',
                value: 100
            },
            {
                name: 'خدمات مشورتی',
                value: 300
            },
            {
                name: 'خدمات غیر مشورتی',
                value: 50
            }
        ],
        name: 'وزارت امور داخله'
    },
    {
        children: [
            {
                name: 'اجناس',
                value: 200
            },
            {
                name: 'ساختمان',
                value: 100
            },
            {
                name: 'خدمات مشورتی',
                value: 300
            },
            {
                name: 'خدمات غیر مشورتی',
                value: 50
            }
        ],
        name: 'وزارت امور داخله'
    },
    {
        children: [
            {
                name: 'اجناس',
                value: 200
            },
            {
                name: 'ساختمان',
                value: 100
            },
            {
                name: 'خدمات مشورتی',
                value: 300
            },
            {
                name: 'خدمات غیر مشورتی',
                value: 50
            }
        ],
        name: 'وزارت امور داخله'
    },
    {
        children: [
            {
                name: 'اجناس',
                value: 200
            },
            {
                name: 'ساختمان',
                value: 100
            },
            {
                name: 'خدمات مشورتی',
                value: 300
            },
            {
                name: 'خدمات غیر مشورتی',
                value: 50
            }
        ],
        name: 'وزارت امور داخله'
    },
    {
        children: [
            {
                name: 'اجناس',
                value: 200
            },
            {
                name: 'ساختمان',
                value: 100
            },
            {
                name: 'خدمات مشورتی',
                value: 300
            },
            {
                name: 'خدمات غیر مشورتی',
                value: 50
            }
        ],
        name: 'وزارت امور داخله'
    },
    {
        children: [
            {
                name: 'اجناس',
                value: 200
            },
            {
                name: 'ساختمان',
                value: 100
            },
            {
                name: 'خدمات مشورتی',
                value: 300
            },
            {
                name: 'خدمات غیر مشورتی',
                value: 50
            }
        ],
        name: 'وزارت امور داخله'
    },
    {
        children: [
            {
                name: 'اجناس',
                value: 200
            },
            {
                name: 'ساختمان',
                value: 100
            },
            {
                name: 'خدمات مشورتی',
                value: 300
            },
            {
                name: 'خدمات غیر مشورتی',
                value: 50
            }
        ],
        name: 'وزارت امور داخله'
    },
    {
        children: [
            {
                name: 'اجناس',
                value: 200
            },
            {
                name: 'ساختمان',
                value: 100
            },
            {
                name: 'خدمات مشورتی',
                value: 300
            },
            {
                name: 'خدمات غیر مشورتی',
                value: 50
            }
        ],
        name: 'وزارت امور داخله'
    }
];
export const SIMPLE_TREE_MAP_DATA = [
    {
        value: 100,
        name: 'وزارت فواید عامه'
    },
    {
        value: 200,
        name: 'وزارت امور خارجه'
    },
    {
        value: 300,
        name: 'وزارت امور داخله'
    },
    {
        value: 400,
        name: 'وزارت امور داخله'
    },
    {
        value: 500,
        name: 'وزارت امور داخله'
    },
    {
        value: 600,
        name: 'وزارت امور داخله'
    },
    {
        value: 400,
        name: 'وزارت امور داخله'
    },
    {
        value: 250,
        name: 'وزارت امور داخله'
    },
    {
        value: 280,
        name: 'وزارت امور داخله'
    },
    {
        value: 350,
        name: 'وزارت امور داخله'
    },
    {
        value: 550,
        name: 'وزارت امور داخله'
    },
    {
        value: 470,
        name: 'وزارت امور داخله'
    },
    {
        value: 200,
        name: 'وزارت امور داخله'
    }
];
