import {Injectable} from '@angular/core';

import * as io from 'socket.io-client';
import {environment} from '../../../../environments/environment';
import {AuthService} from '../../../services/auth.service';

export interface INotification {
    type: string;
    payload: any;
}

@Injectable()
export class SocketService {
    public socket: any;

    constructor(private _authService: AuthService) {
    }

    connect() {
        if (!environment.isWebSocketEnabled) {
            return;
        }
        this.socket = io.connect(environment.baseUrl.notificationManager.host + ':' + environment.baseUrl.notificationManager.port);
        this._authenticate();
    }

    public get connection(): Promise<any> {
        if (!environment.isWebSocketEnabled) {
            return Promise.resolve(this._getSocketStub());
        }
        return new Promise((resolve) => {
            if (this.socket) {
                return resolve(this.socket);
            }
            this.socket = io.connect(environment.baseUrl.notificationManager.host + ':' + environment.baseUrl.notificationManager.port);
            this._authenticate();
            resolve(this.socket);
        });
    }

    private _authenticate() {
        this.socket.emit('auth', this._authService.token);
    }

    private _getSocketStub() {
        return {
            on: function (event, cb) {
            }
        };
    }
}
