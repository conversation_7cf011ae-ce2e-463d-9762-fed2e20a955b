<button mat-icon-button [mat-dialog-close]="true" matTooltip="بستن" class="dialog-close-button">
    <mat-icon>close</mat-icon>
</button>
<form [formGroup]="reminderDialog">
    <h1 mat-dialog-title>{{'REMINDER'|translate}}</h1>
    <mat-dialog-content class="dialog" fxLayout="row wrap" fxLayoutAlign="start">
        <npa-view-element fxFlex="25">
            <div class="npa-label">{{'ID'|translate}}:</div>
            <div class="npa-value">xxx</div>
        </npa-view-element>

        <div class="npa-form-field" fxFlex="25">
            <npa-date-time-picker placeholder="{{'REMINDER_SHAMSI_DATE'|translate}}"
                                  [(npaDateModel)]="reminderDialog.controls['reminder_date']"
                                  [valueChange]="reminderDialog.controls['reminder_date'].value"></npa-date-time-picker>
            <mat-error *ngIf="reminderDialog?.get('reminder_date')?.errors?.required ">
                {{formValidationService.required.message()}}
            </mat-error>
        </div>

        <mat-form-field fxFlex="25">
            <input matInput [matDatepicker]="reminder_date"
                   placeholder="{{'REMINDER_GREGORIAN_DATE'|translate}}"
                   [matDatepicker]="reminder_date"
                   formControlName="reminder_date">
            <mat-datepicker-toggle matSuffix [for]="reminder_date"></mat-datepicker-toggle>
            <mat-datepicker #reminder_date></mat-datepicker>
            <mat-error *ngIf="reminderDialog?.get('reminder_date')?.errors?.required">
                {{formValidationService.required.message()}}
            </mat-error>
        </mat-form-field>
        <npa-view-element fxFlex="100">
            <div class="npa-label">{{'TITLE'|translate}}:</div>
            <div class="npa-value">xxx</div>
        </npa-view-element>
    </mat-dialog-content>
    <mat-dialog-actions>
        <button mat-raised-button color="primary" type="submit">{{'SAVE'|translate}}</button>
        <button mat-button [mat-dialog-close]="true">{{'CANCEL'|translate}}</button>
    </mat-dialog-actions>
</form>

