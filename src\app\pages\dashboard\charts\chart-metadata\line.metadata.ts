export const SIMPLE_LINE = {
    title: {
        text: '',
        x: 'center',
        textStyle: {
            fontFamily: 'XB Niloofar',
            color: '#333',
            fontWeight: 'normal',
            fontSize: 16,
            lineHeight: 20,
        }
    },
    toolbox: {
        show: true,
        feature: {
            mark: {
                show: true,
                title: {
                    mark: 'Add Guide',
                    markUndo: 'Remove Guide',
                    markClear: 'Remove all Guide',
                },
            },
            dataView: {
                show: true,
                readOnly: false,
                optionToContent: null,
                contentToOption: null,
                title: 'Data View',
                lang: [],
                buttonColor: '#01579B'
            },
            magicType: {
                show: true,
                type: ['line', 'bar'],
                title: {line: 'line', bar: 'bar'}
            },
            restore: {
                show: true,
                title: 'Reset'
            },
            saveAsImage: {
                show: true,
                title: 'Save As Image'
            }
        }
    },
    tooltip: {
        trigger: 'axis',
        axisPointer: {
            type: 'cross',
            label: {
                backgroundColor: '#6a7985'
            }
        },
        formatter: '{b} <br>{a} : {c}'
    },
    xAxis: {
        type: 'category',
        data: []
    },
    yAxis: {
        type: 'value'
    },
    series: [{
        name: '',
        data: [],
        color: '#595b5d',
        type: 'line'
    }]
};
