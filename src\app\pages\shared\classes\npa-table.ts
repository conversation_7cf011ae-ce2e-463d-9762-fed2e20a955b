import {BehaviorSubject, Observable} from 'rxjs';
import {DataSource} from '@angular/cdk/collections';
import {MatPaginatorIntl} from '@angular/material';

export class NpaDatabase {
    dataChange: BehaviorSubject<any[]> = new BehaviorSubject<any[]>([]);

    constructor(rows: any | any[]) {
        rows = rows || [];
        rows.forEach((row: any) => {
            this.add(row);
        });
    }

    add(row: any) {
        const copiedData = this.data.slice();
        copiedData.push(row);
        this.dataChange.next(copiedData);
    }


    delete(id: number, conditions?: string[]) {
        const data = this.data;
        const finalData = [];

        data.forEach((row: any) => {
            if (conditions) {
                let isTargetRow = true;
                conditions.forEach(current => {
                    isTargetRow = isTargetRow &&
                        row[current] && id === row['id'];
                });
                if (!isTargetRow) {
                    finalData.push(row);
                }
            } else {
                if (id !== row.id) {
                    finalData.push(row);
                }
            }
        });
        this.dataChange.next(finalData);
    }

    find(id: number): boolean {
        const data = this.data;
        for (let i = 0; i < data.length; i++) {
            if (data[i].id === id) {
                return true;
            }
        }
    }

    edit(editedRow: any, conditions?: string[]) {
        const data = this.data;
        const finalData = [];
        data.forEach((row: any) => {

            if (conditions) {
                let isTargetRow = true;
                conditions.forEach((current: string) => {
                    isTargetRow = isTargetRow &&
                        editedRow[current] &&
                        row[current] &&
                        editedRow[current] === row[current];
                });
                finalData.push(isTargetRow ? editedRow : row);
            } else {
                finalData.push(editedRow.id === row.id ? editedRow : row);
            }

        });
        this.dataChange.next(finalData);
    }

    get data(): any[] {
        return this.dataChange.value;
    }

}

export class NpaDataSource extends DataSource<any> {
    constructor(private _domesticDatabase: NpaDatabase) {
        super();
    }

    connect(): Observable<any[]> {
        return this._domesticDatabase.dataChange;
    }

    disconnect() {
    }
}

export function formatPaginator(_matPaginatorIntl: MatPaginatorIntl) {
    if (localStorage.getItem('lang') !== 'en') {
        if (localStorage.getItem('lang') === 'prs') {
            _matPaginatorIntl.itemsPerPageLabel = 'تعداد سطر در صفحه:';
            _matPaginatorIntl.nextPageLabel = 'صفحه بعدی';
            _matPaginatorIntl.previousPageLabel = 'صفحه قبلی';
        } else {
            _matPaginatorIntl.itemsPerPageLabel = 'په پاڼه کې د قطارونو شمیر:';
            _matPaginatorIntl.nextPageLabel = 'بله پاڼه';
            _matPaginatorIntl.previousPageLabel = 'مخکینی پاڼه';
        }
        _matPaginatorIntl.getRangeLabel =
            (page: number, pageSize: number, length: number) => {
                if (length === 0 || pageSize === 0) {
                    return `0 از ${length}`;
                }
                length = Math.max(length, 0);
                const startIndex = page * pageSize;
                const endIndex = startIndex < length ? Math.min(startIndex + pageSize, length) : startIndex + pageSize;
                return `${startIndex + 1} - ${endIndex} از ${length}`;
            };
    }
}
