@import '../../../../../styles/definitions.styl'

ul.main
  padding 0
  margin 0
  li
    list-style-type none
    font-size 14px
    position relative
    overflow hidden
    a, a:visited, a:hover, a:focus, a:active
      height 48px
      position relative
      display: flex;
      align-items: center;
      justify-content space-between
      text-decoration none
      color colorSidebarText
      padding-left 23px
      padding-right 23px
      mat-icon
        height 16px
        width 16px
        font-size 16px
    ul
      max-height 0
      transition max-height 0.5s ease-in
      padding 0
      margin 0
      width 100%
      a
        padding-right 40px
        span
          right 72px
  .expand
    background #595b5d
    transition max-height 0.5s ease-in
    ul
      display: block
      max-height 1000px
      transition max-height 0.5s ease-in
      li
        background #595b5d

    mat-icon.menu-arrow
      -ms-transform rotate(-90deg)
      -webkit-transform rotate(-90deg)
      transform rotate(-90deg)

    .selected
      color colorBanner !important

ul.main[lang=prs], ul.main[lang=ps]
  li
    a
      span
        position absolute
        right 55px

ul.main[lang=en]
  li
    a
      span
        position absolute
        left 55px

ul.main[lang=en]
  li
    ul
      direction ltr
      li
        text-align left
  mat-icon.menu-arrow
    -ms-transform rotate(-180deg)
    -webkit-transform rotate(-180deg)
    transform rotate(-180deg)

ul.main[lang=prs], ul.main[lang=ps]
  li
    ul
      direction rtl
      li
        text-align right

ul.main-sidebar-closed
  li
    ul
      a
        padding-right 23px !important
