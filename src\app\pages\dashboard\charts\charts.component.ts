import {Component, OnInit} from '@angular/core';
import {AuthService} from '../../../services/auth.service';
import {FormBuilder, FormGroup} from '@angular/forms';
import {NotificationsService} from '../../shared/services/notifications.service';


@Component({
    selector: 'npa-charts',
    templateUrl: './charts.component.html',
    styleUrls: ['./charts.component.styl']
})
export class ChartsComponent implements OnInit {
    form: FormGroup;
    expanded;
    procurement_types = [
        {
            name_da: 'ساختمان',
            id: 1
        },
        {
            name_da: 'اجناس',
            id: 2
        },
        {
            name_da: 'خدمات مشورتی',
            id: 2
        },
        {
            name_da: 'خدمات غیر مشورتی',
            id: 2
        },
    ];

    constructor(public authService: AuthService,
                private _formBuilder: FormBuilder,
                public notificationsService: NotificationsService) {
    }

    ngOnInit() {
        this.initForm();
    }

    initForm() {
        this.form = this._formBuilder.group({
            procurement_type: undefined,
            complaint_type: undefined,
            complainant_company: undefined,
            date_of_saved_complaint: undefined,
            project_name: undefined,
            complaint_status: undefined,
            complaint_id: undefined,
            year: undefined,
            related_procurement_entity: undefined,
            specified_members: undefined
        });
    }

    isValid(): boolean {
        return this.form && this.form.valid;
    }
}
