import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { ConfirmationAndJustificationDialogComponent } from './confirmation-and-justification-dialog.component';

describe('ConfirmationAndJustificationDialogComponent', () => {
  let component: ConfirmationAndJustificationDialogComponent;
  let fixture: ComponentFixture<ConfirmationAndJustificationDialogComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ ConfirmationAndJustificationDialogComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(ConfirmationAndJustificationDialogComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
