import {RouterModule, Routes} from '@angular/router';
import {RolesComponent} from './roles.component';
import {RoleListComponent} from './role-list/role-list.component';

export const rolesComponents = [
  RoleListComponent,
  RolesComponent
];
const routes: Routes = [
  {
    path: '',
    component: RolesComponent
  },
  {
    path: 'roles-list',
    component: RoleListComponent
  }
];

export const RolesRoutes = RouterModule.forChild(routes);
