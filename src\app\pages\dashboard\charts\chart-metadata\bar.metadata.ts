export const SIMPLE_VERTICAL_BAR = {
    title: {
        text: '',
        x: 'center',
        textStyle: {
            fontFamily: 'XB Niloofar',
            color: '#333',
            fontWeight: 'normal',
            fontSize: 16,
            lineHeight: 20,
        }
    },
    color: ['#595b5d', '#e2d0ad', '#5a7e92'],
    tooltip: {
        trigger: 'axis',
        axisPointer: {
            type: 'shadow'
        },
        textStyle: {
            fontFamily: 'XB Niloofar',
        },
    },
    toolbox: {
        show: true,
        feature: {
            mark: {
                show: true,
                title: {
                    mark: 'Add Guide',
                    markUndo: 'Remove Guide',
                    markClear: 'Remove all Guide',
                },
            },
            dataView: {
                show: true,
                readOnly: false,
                optionToContent: null,
                contentToOption: null,
                title: 'Data View',
                lang: ['Data View', 'Shutdown', 'Refresh'],
                buttonColor: '#01579B'
            },
            magicType: {
                show: true,
                type: ['line', 'bar'],
                title: {line: 'Line', bar: 'Bar'}
            },
            restore: {
                show: true,
                title: 'Reset'
            },
            saveAsImage: {
                show: true,
                title: 'Save As Image'
            }
        }
    },
    legend: {
        x: 'center',
        y: 'bottom',
        orient: 'horizontal',
        itemGap: 40,
        textStyle: {
            fontFamily: 'XB Niloofar',
        },
        data: []
    },
    calculable: true,
    yAxis: [
        {
            type: 'value',
            name: '',
            nameRotate: 90,
            nameLocation: 'center',
            nameGap: 40,
            nameTextStyle: {
                fontSize: 14,
                fontFamily: 'XB Niloofar',
            }
        }
    ],
    xAxis: {
        type: 'category',
        axisTick: {show: false},

        data: [],
        axisLabel: {
            rich: {
                per: {
                    fontSize: 12,
                    fontFamily: 'XB Niloofar',
                }
            }
        }


    },
    series: [],

};
export const SIMPLE_HORIZONTAL_BAR = {
    title: {
        text: '',
        x: 'center',
        textStyle: {
            fontFamily: 'XB Niloofar',
            color: '#333',
            fontWeight: 'normal',
            fontSize: 16,
            lineHeight: 20,
        }
    },
    color: ['#595b5d', '#e2d0ad'],
    tooltip: {
        trigger: 'axis',
        axisPointer: {
            type: 'shadow'
        },
        textStyle: {
            fontFamily: 'XB Niloofar',
        },
    },
    toolbox: {
        show: true,
        feature: {
            mark: {
                show: true,
                title: {
                    mark: 'Add Guide',
                    markUndo: 'Remove Guide',
                    markClear: 'Remove all Guide',
                },
            },
            dataView: {
                show: true,
                readOnly: false,
                optionToContent: null,
                contentToOption: null,
                title: 'Data View',
                lang: ['Data View', 'Shutdown', 'Refresh'],
                buttonColor: '#01579B'
            },
            magicType: {
                show: true,
                type: ['line', 'bar'],
                title: {line: 'Line', bar: 'Bar'}
            },
            restore: {
                show: true,
                title: 'Reset'
            },
            saveAsImage: {
                show: true,
                title: 'Save As Image'
            }
        }
    },
    calculable: true,
    legend: {
        left: 'center',
        align: 'right',
        bottom: 0,
        orient: 'horizontal',
        textStyle: {
            fontFamily: 'XB Niloofar',
        },
        data: []
    },
    xAxis: [
        {
            type: 'value',
            name: '',
            nameRotate: 0,
            nameLocation: 'center',
            nameGap: 40,
            nameTextStyle: {
                fontSize: 14,
                fontFamily: 'XB Niloofar',
            }
        }
    ],
    yAxis: {
        type: 'category',
        axisTick: {show: false},
        data: [],

        axisLabel: {
            rich: {
                per: {
                    fontSize: 12,
                    fontFamily: 'XB Niloofar',
                }
            }
        }

    },
    grid: {
        left: '15%',
    },
    series: [],

};
export const Y_CATEGORY_STACK_BAR = {
    title: {
        text: '',
        x: 'center',
        textStyle: {
            fontFamily: 'XB Niloofar',
            color: '#333',
            fontWeight: 'normal',
            fontSize: 16,
            lineHeight: 20,
        }
    },
    color: ['#595b5d', '#e2d0ad'],
    tooltip: {
        trigger: 'axis',
        axisPointer: {
            type: 'shadow'
        }
    },
    toolbox: {
        show: true,
        feature: {
            mark: {
                show: true,
                title: {
                    mark: 'Add Guide',
                    markUndo: 'Remove Guide',
                    markClear: 'Remove all Guide',
                },
            },
            dataView: {
                show: true,
                readOnly: false,
                optionToContent: null,
                contentToOption: null,
                title: 'Data View',
                lang: ['Data View', 'Shutdown', 'Refresh'],
                buttonColor: '#01579B'
            },
            magicType: {
                show: true,
                type: ['line', 'bar'],
                title: {line: 'Line', bar: 'Bar'}
            },
            restore: {
                show: true,
                title: 'Reset'
            },
            saveAsImage: {
                show: true,
                title: 'Save As Image'
            }
        }
    },
    legend: {
        left: 'center',
        align: 'right',
        bottom: -7,
        orient: 'horizontal',
        textStyle: {
            fontFamily: 'XB Niloofar',
        },
        data: []
    },
    grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
    },
    xAxis: {
        type: 'value'
    },
    yAxis: {
        type: 'category',
        data: []
    },
    series: []

};
