import {Injectable} from '@angular/core';
import {ActivatedRouteSnapshot, CanActivate, Router, RouterStateSnapshot} from '@angular/router';
import {AuthService} from './auth.service';
import {Observable} from 'rxjs';
import {RouteService} from './route.service';

@Injectable()
export class RouteGuardsService implements CanActivate {

    constructor(public _auth: AuthService,
                private _router: Router,
                private _route: RouteService) {
    }

    canActivate(route: ActivatedRouteSnapshot,
                state: RouterStateSnapshot): Observable<boolean> | Promise<boolean> | boolean {
        if (!this._auth.isUserAuthenticated) {
            this._router.navigateByUrl('login');
            return false;
        }

        if (route.params['role'] && route.params['role'] !== this._auth.user.value.role.slug) {
            return this._route.navigateBasedManualRoleEntry(route.params['role']);
            // this.router.navigate(['unauthorized-access']);
            // return false;
        }
        return true;

    }
}
