import {Injectable} from '@angular/core';
import {HttpService} from './http.service';
import {AuthService} from './auth.service';


import {environment as localEnvironment} from '../../environments/environment';
import {environment as productionEnvironment} from '../../environments/environment.prod';
import {DropDownsService} from './dropDowns.service';
import {Location} from '@angular/common';
import {HelperService} from './helper.service';


@Injectable()
export class BootService {
    env = localEnvironment || productionEnvironment;
    user_identification_number;

    constructor(private _http: HttpService,
                private _auth: AuthService,
                private _location: Location,
                private helperService: HelperService,
                private _dropDownsService: DropDownsService) {
    }

    public load(): Promise<any> {
        if (!localStorage.getItem('lang')) {
            localStorage.setItem('lang', 'prs');
        }
        return new Promise((resolve, reject) => {
            this.checkBrowserCompatibility(reject);
            const path = this._location.prepareExternalUrl(this._location.path());
            this.setTheLangFromUrlIfExists(path);
            HelperService.redirectedUrl = this.helperService.getUrlParameterByName('redirected_url', path);
            if (HelperService.redirectedUrl) {
                let hasToAuth = false;
                if (HelperService.redirectedUrl.includes('toAuth=true')) {
                    hasToAuth = true;
                }
                const urlArray = HelperService.redirectedUrl.split('/');
                HelperService.redirectedSystemBaseUrl = urlArray.slice(0, 3).join('/') + '/';
                if (hasToAuth) {
                    HelperService.redirectedSystemBaseUrl += '?toAuth=true';
                }
                if (!urlArray.slice(3).join('/')) {
                    HelperService.redirectedUrl += 'authentication';
                }
            }
            if (!this.env.production) {
                if (!this.env.loginThroughDataBase) {
                    this._auth.defaultUserLoggedInUrl = `/${localStorage.getItem('lang')}/${this.env.logInData.user.role.slug}/`;

                    this._auth.isUserAuthenticated = true;
                    this.getDropDowns(resolve, reject);

                } else {
                    this.checkSessionAndVerify(resolve, reject);
                }
            } else {
                this.checkSessionAndVerify(resolve, reject);
            }
        });

    }

    checkSessionAndVerify(resolve, reject) {
        if (localStorage.getItem('session')) {
            if (HelperService.redirectedUrl) {
                this._http.get(`api/single-use-token?redirected_url=${HelperService.redirectedSystemBaseUrl}`).subscribe(data => {
                    document.location.href = HelperService.redirectedUrl + '?singleUseToken=' + data.singleUseToken;
                }, (error) => {
                    if (error.status === 404) {
                        HelperService.hasRedirectedSystemAccess.next(true);
                    }
                    this._http.get(`api/usm/initiates`).subscribe((res) => {
                        this._auth.allCredentials = res.user;
                        this._auth.roles = res.roles;
                        this._auth.isUserAuthenticated = true;
                        this.getDropDowns(resolve, reject);
                    }, (er) => {
                        this._auth.isUserAuthenticated = false;
                        resolve(true);
                    });
                });
            } else {
                this._http.get(`api/usm/initiates`).subscribe((res) => {
                    HelperService.redirectedUrl = undefined;
                    this._auth.allCredentials = res.user;
                    this._auth.roles = res.roles;
                    this._auth.isUserAuthenticated = true;
                    this.getDropDowns(resolve, reject);
                }, (error) => {
                    this._auth.isUserAuthenticated = false;
                    resolve(true);
                });
            }
        } else {
            this._auth.isUserAuthenticated = false;
            resolve(true);

        }
    }

    getDropDowns(resolve, reject) {
        if (this._dropDownsService.dropDowns.length === 0) {
            return resolve(true);
        }
        this._dropDownsService.setDropDowns().subscribe(() => {
            return resolve(true);
        }, (error: any) => {
            if (error.status !== 401) {
                alert('خطا در عیار سازی سیستم. لطفاً صفحه را بازنشانی مجدد نمایید.');
                console.error(error);
                window.stop();
                return reject(true);
            }
        });

    }

    setTheLangFromUrlIfExists(path: string) {
        const pathArray = path.split('/');
        if (pathArray.length > 1) {
            if (['prs', 'en', 'ps'].includes(pathArray[1])) {
                localStorage.setItem('lang', pathArray[1]);
            }
        }
    }


    checkBrowserCompatibility(reject) {
        const ua = navigator.userAgent.match(/(opera|chrome|safari|firefox|msie)\/?\s*(\.?\d+(\.\d+)*)/i);
        let browser;
        if (navigator.userAgent.match(/Edge/i) || navigator.userAgent.match(/Trident.*rv[ :]*11\./i)) {
            browser = 'msie';
        } else {
            browser = ua[1].toLowerCase();
        }
        if (browser !== 'chrome') {
            const temp = confirm('کاربر گرامی!\n' +
                'برای دسترسی به سیستم لطفاً از مرورگر Google Chrome استفاده نمایید.\n' +
                'برای دانلود مرورګر تایید نمایید.\n' +
                '\n' +
                'از طرف تیم تخنیکی اداره تدارکات ملی.');
            if (temp) {
                reject(true);
                (<any>window.location).href = 'https://www.google.com/chrome/';
            } else {
                reject(true);
                window.stop();
            }
        }
    }

}

