import {Request, Response} from 'express';
import {InstanceService} from '../services/instance.service';
import {Database, Model} from '../config/database';
import {UserRolePe} from '../entity/user-role-pe';
import {UserRoleVendor} from '../entity/user-role-vendor';
import {UserRoleSector} from '../entity/user-role-sector';
import {IUserRole, UserRole} from '../entity/user-role';
import {UserRoleRecord} from '../entity/user-role-record';
import {createQueryBuilder} from 'typeorm';
import {UserRoleThreshold} from '../entity/user-role-threshold';

export class ResourceController {
    public static instance: ResourceController;

    public static getInstance(): ResourceController {
        return InstanceService.getInstance(ResourceController);

    }

    public async resourcesAssigned(req: Request, res: Response) {
        try {
            const userRole = <IUserRole | any>await Model(UserRole).findOne({where: {id: req.query['user_role_id']}, relations: ['role']});
            const response = {};
            if (userRole.role.is_pe_based) {
                const data = <any>await createQueryBuilder('UserRolePe')
                    .select('UserRolePe.procurement_entity_id')
                    .leftJoin('UserRolePe.peResourceNotApplicable', 'peResourceNotApplicable')
                    .where(`peResourceNotApplicable.id is null`)
                    .andWhere(`UserRolePe.user_role_id=:user_role_id`, {user_role_id: userRole.id})
                    .getMany();
                if (data.length !== 0) {
                    response['user_role_pes'] = data;
                }
            }
            if (userRole.role.is_sector_based) {
                const data = <any>await createQueryBuilder('UserRoleSector')
                    .select('UserRoleSector.sector_id')
                    .leftJoin('UserRoleSector.sectorResourceNotApplicable', 'sectorResourceNotApplicable')
                    .where(`sectorResourceNotApplicable.id is null`)
                    .andWhere(`UserRoleSector.user_role_id=:user_role_id`, {user_role_id: userRole.id})
                    .getMany();
                if (data.length !== 0) {
                    response['user_role_sectors'] = data;
                }
            }
            if (userRole.role.is_vendor_based) {

                const vendorUserRole = <any>await createQueryBuilder('UserRole')
                    .select('UserRole.id')
                    .leftJoin('UserRole.role', 'role')
                    .where(`role.slug = "avrcs-vendor-registrar"`)
                    .andWhere(`UserRole.user_id=:user_id`, {user_id: userRole.user_id})
                    .getOne();
                response['user_role_vendor'] = await Model(UserRoleVendor).findOne({select: ['vendor_license_number'], where: {user_role_id: vendorUserRole.id}});
            }
            if (userRole.role.is_record_based) {
                response['user_role_records'] = await Model(UserRoleRecord).find({select: ['record_id'], where: {user_role_id: userRole.id}});
            }
            if (userRole.role.is_threshold_based) {
                response['user_role_threshold'] = await Model(UserRoleThreshold).findOne({select: ['is_above_threshold'], where: {user_role_id: userRole.id}});
            }
            return res.status(200).json(response);
        } catch (e) {
            return res.status(500).json(e);
        }
    }
}
