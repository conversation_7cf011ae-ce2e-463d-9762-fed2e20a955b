import {Component, OnInit} from '@angular/core';
import {NotificationsService} from '../../shared/services/notifications.service';
import {TranslateService} from '../../shared/services/translate.service';

@Component({
    selector: 'npa-system-information',
    templateUrl: './system-information.component.html',
    styleUrls: ['./system-information.component.styl']
})
export class SystemInformationComponent implements OnInit {

    constructor(private _notificationsService: NotificationsService,
                public translate: TranslateService) {
    }

    ngOnInit() {
        this._notificationsService.dismissLoading();
    }

}
