import {BaseEntity, Column, Entity, Index, JoinC<PERSON>umn, ManyToOne, PrimaryGeneratedColumn} from 'typeorm';
import {System} from './system';
import {UserRole} from './user-role';

@Entity('user_role_thresholds')
@Index('unique_user_role_id_key', ['user_role_id'], {unique: true})
export class UserRoleThreshold extends BaseEntity implements IUserRoleThreshold {

    @PrimaryGeneratedColumn()
    id: number;

    @Column({name: 'is_above_threshold'})
    is_above_threshold: boolean;

    @Column({name: 'user_role_id'})
    user_role_id: number;

    @ManyToOne(type => UserRole, userRole => userRole.id, { onDelete: 'CASCADE' })
    @JoinColumn({name: 'user_role_id'})
    userRole: UserRole;

}

export interface IUserRoleThreshold {
    id?: number;
    is_above_threshold: boolean;
    user_role_id: number;
    userRole?: any | UserRole;
}


