import {Component, OnInit} from '@angular/core';
import {FormService} from '../../shared/services/form.service';
import {ProcurementTypeContextService} from '../../../services/procurement-type-context.service';
import {AuthService} from '../../../services/auth.service';
import {FormBuilder, FormGroup} from '@angular/forms';
import {NotificationsService} from '../../shared/services/notifications.service';
import {TranslateService} from '../../shared/services/translate.service';

@Component({
    selector: 'npa-generate-and-download-reports',
    templateUrl: './generate-and-download-reports.component.html',
    styleUrls: ['./generate-and-download-reports.component.styl']
})
export class GenerateAndDownloadReportsComponent implements OnInit {
    form: FormGroup;

    constructor(private notificationsService: NotificationsService,
                private formBuilder: FormBuilder,
                public formService: FormService,
                public authService: AuthService,
                public translate: TranslateService,
                public contextService: ProcurementTypeContextService) {
    }


    ngOnInit() {
        this.initForm();
        this.notificationsService.dismissLoading();
    }

    initForm() {
        this.form = this.formBuilder.group({
            user_identification_number: undefined,
        });
    }


}
