<mat-card id="ng_test_card">
  <mat-card-header>
    <mat-card-title>{{'SYSTEMS_LIST' |translate}}</mat-card-title>
  </mat-card-header>
  <mat-card-content>
    <mat-table #table *ngIf="dataSource" [dataSource]="dataSource">
      <ng-container matColumnDef="system_icon">
        <mat-header-cell *matHeaderCellDef>
          {{'SYSTEM_ICON'| translate}}
        </mat-header-cell>
        <mat-cell *matCellDef="let row">
          <mat-icon>{{row.icon_name}}</mat-icon>
        </mat-cell>
      </ng-container>
      <ng-container matColumnDef="system_slug">
        <mat-header-cell *matHeaderCellDef>
          {{'SYSTEM_SLUG'| translate}}
        </mat-header-cell>
        <mat-cell *matCellDef="let row">
          {{row.slug}}
        </mat-cell>
      </ng-container>
      <ng-container matColumnDef="system_url" >
        <mat-header-cell *matHeaderCellDef>
          {{'SYSTEM_URL'| translate}}
        </mat-header-cell>
        <mat-cell *matCellDef="let row" class="direction-to-ltr">
          <a href="{{row.url}}">{{row.url}}</a>
        </mat-cell>
      </ng-container>
      <ng-container matColumnDef="options">
        <mat-header-cell *matHeaderCellDef [class]="dashboardSystemListService.getClass('options')">
          {{'OPTIONS' | translate}}
        </mat-header-cell>
        <mat-cell *matCellDef="let row" [class]="dashboardSystemListService.getClass('options')">
          <button
            type='button' mat-icon-button
            matTooltip="{{'VIEW_INFORMATION' | translate}}" (click)="openViewDialog(row)">
            <mat-icon>visibility</mat-icon>
          </button>
        </mat-cell>
      </ng-container>
      <mat-header-row *matHeaderRowDef="chosenTitles" [style.width]="getWidth()"></mat-header-row>
      <mat-row [ngClass]="row?.withdrawal_reason ? 'red-background' : ''"
               *matRowDef="let row; columns: chosenTitles" [style.width]="getWidth()"></mat-row>
    </mat-table>
    <!--<div class="pagination-container">-->
      <!--<mat-paginator></mat-paginator>-->
    <!--</div>-->
  </mat-card-content>
</mat-card>
