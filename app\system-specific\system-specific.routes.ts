import {I<PERSON>out<PERSON>} from '../app.routes';
import {AvrcsSystemSpecificController} from './avrcs-system-specific.controller';
import {AcpmsSystemSpecificController} from './acpms-system-specific.controller';
import {AppmsSystemSpecificController} from './appms-system-specific.controller';

export const APPMS_ROUTES: IRoute[] = [
    {
        url: '/appms/assign-project-to-user',
        httpMethod: 'post',
        controllerMethod: AppmsSystemSpecificController.getInstance().assignProjectToUser
    },
    {
        url: '/appms/:user_role_id/get-role',
        httpMethod: 'get',
        controllerMethod: AppmsSystemSpecificController.getInstance().getRole
    },
    {
        url: '/appms/procurement-plan-manager/:user_role_id/get-procurement-entity',
        httpMethod: 'get',
        controllerMethod: AppmsSystemSpecificController.getInstance().getProcurementPlanManagerPEs
    },
    {
        url: '/appms/get-award-authority',
        httpMethod: 'get',
        controllerMethod: AppmsSystemSpecificController.getInstance().getAwardAuthority,
    },
    {
        url: '/appms/get-procurement-plan-manager',
        httpMethod: 'get',
        controllerMethod: AppmsSystemSpecificController.getInstance().getProcurementPlanManager,
    },
    {
        url: '/appms/get-specialist',
        httpMethod: 'get',
        controllerMethod: AppmsSystemSpecificController.getInstance().getSpecialist,
    },
    {
        url: '/appms/get-procurement-policy-manager',
        httpMethod: 'get',
        controllerMethod: AppmsSystemSpecificController.getInstance().getProcurementPolicyManager,
    },
    {
        url: '/appms/get-procurement-policy-director',
        httpMethod: 'get',
        controllerMethod: AppmsSystemSpecificController.getInstance().getProcurementPolicyDirector
    },
    {
        url: '/appms/get-users-by-ids',
        httpMethod: 'get',
        controllerMethod: AppmsSystemSpecificController.getInstance().getUsersByIds
    },
];

export const SYSTEM_SPECIFIC_ROUTES: IRoute[] = [
    {
        url: '/acpms/get-cpm-director',
        httpMethod: 'get',
        controllerMethod: AcpmsSystemSpecificController.getInstance().getCpmDirector,
    },
    {
        url: '/acpms/get-users-by-ids',
        httpMethod: 'post',
        controllerMethod: AcpmsSystemSpecificController.getInstance().getUsersByIds,
    },
    {
        url: '/acpms/get-roles-by-ids',
        httpMethod: 'get',
        controllerMethod: AcpmsSystemSpecificController.getInstance().getRolesByIds,
    },
    {
        url: '/acpms/get-specialists',
        httpMethod: 'get',
        controllerMethod: AcpmsSystemSpecificController.getInstance().getSpecialists,
    },
    {
        url: '/acpms/get-contract-specialist',
        httpMethod: 'get',
        controllerMethod: AcpmsSystemSpecificController.getInstance().getContractSpecialist,
    },
    {
        url: '/acpms/get-award-authority',
        httpMethod: 'get',
        controllerMethod: AcpmsSystemSpecificController.getInstance().getAwardAuthority,
    },
    {
        url: '/acpms/get-cpm-manager',
        httpMethod: 'get',
        controllerMethod: AcpmsSystemSpecificController.getInstance().getCpmManager,
    },
    {
        url: '/acpms/get-contract-managers',
        httpMethod: 'get',
        controllerMethod: AcpmsSystemSpecificController.getInstance().getContractManagers,
    },
    {
        url: '/acpms/get-contract-contract-manager',
        httpMethod: 'get',
        controllerMethod: AcpmsSystemSpecificController.getInstance().getContractContractManager,
    },
    {
        url: '/acpms/get-company-user',
        httpMethod: 'get',
        controllerMethod: AcpmsSystemSpecificController.getInstance().getCompanyUsers,
    },
    {
        url: '/acpms/get-company-user-roles',
        httpMethod: 'get',
        controllerMethod: AcpmsSystemSpecificController.getInstance().getCompanyUserRoles,
    },
    {
        url: '/avrcs/get-users-by-ids',
        httpMethod: 'get',
        controllerMethod: AvrcsSystemSpecificController.getInstance().getUsersByIds,
    },
    {
        url: '/avrcs/get-vendor-information-approval',
        httpMethod: 'get',
        controllerMethod: AvrcsSystemSpecificController.getInstance().getVendorInformationApproval,
    },
    {
        url: '/avrcs/get-user-with-role',
        httpMethod: 'get',
        controllerMethod: AvrcsSystemSpecificController.getInstance().getUserWithRole,
    },
    {
        url: '/avrcs/insert-vendor-license-number',
        httpMethod: 'post',
        controllerMethod: AvrcsSystemSpecificController.getInstance().insertVendorLicenseNumber,
    },
    {
        url: '/avrcs/check-user-has-vendor/:user_role_id',
        httpMethod: 'get',
        controllerMethod: AvrcsSystemSpecificController.getInstance().checkIfUserHasVendor,
    },
    {
        url: '/acpms/assign-contract-to-user',
        httpMethod: 'post',
        controllerMethod: AcpmsSystemSpecificController.getInstance().assignContractToUser,
    },
    {
        url: '/acpms/assign-contract-to-company-user',
        httpMethod: 'post',
        controllerMethod: AcpmsSystemSpecificController.getInstance().assignContractToCompanyUser,
    },
    {
        url: '/acpms/assign-or-remove-contract-access',
        httpMethod: 'post',
        controllerMethod: AcpmsSystemSpecificController.getInstance().assignOrRemoveContractAccess,
    },
    ...APPMS_ROUTES
];
