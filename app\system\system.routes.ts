import {<PERSON><PERSON>out<PERSON>} from '../app.routes';
import {SystemController} from './system.controller';
import {Authentication} from '../middlewares/authentication';

export const SYSTEM_ROUTES: IRoute[] = [
  {
    url: '',
    httpMethod: 'get',
    controllerMethod: SystemController.getInstance().index,
    // middleware: [Authentication.validateToken]
  },
  {
    url: '/list',
    httpMethod: 'get',
    controllerMethod: SystemController.getInstance().userRoleView,
    middleware: [Authentication.validateToken]
  },
  {
    url: '',
    httpMethod: 'post',
    controllerMethod: SystemController.getInstance().store,
    // middleware: [Authentication.validateToken]
  },
  {
    url: '/:id',
    httpMethod: 'get',
    controllerMethod: SystemController.getInstance().show,
    // middleware: [Authentication.validateToken]
  },
  {
    url: '/:id',
    httpMethod: 'put',
    controllerMethod: SystemController.getInstance().update,
    // middleware: [Authentication.validateToken]
  },
  {
    url: '/:id',
    httpMethod: 'delete',
    controllerMethod: SystemController.getInstance().destroy,
    // middleware: [Authentication.validateToken]
  },

];
