import {BaseEntity, Column, Entity, Index, PrimaryGeneratedColumn} from 'typeorm';

@Entity('systems')
@Index('unique_slug', ['slug'], { unique: true })

export class System extends BaseEntity implements ISystem {

    @PrimaryGeneratedColumn()
    id: number;

    @Column({unique: true})
    slug: string;

    @Column()
    url: string;

    @Column()
    icon_name: string;

    @Column()
    description: string;

}

export interface ISystem {
    id?: number;
    slug: string;
    url: string;
    icon_name: string;
    description: string;
}



