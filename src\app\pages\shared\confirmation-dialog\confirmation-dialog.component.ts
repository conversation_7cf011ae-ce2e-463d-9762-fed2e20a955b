import {Component, Inject, OnInit} from '@angular/core';
import {MAT_DIALOG_DATA, MatDialogRef} from '@angular/material';
import {IConfirmationDialogInput} from '../types/confirmation.type';
import {AuthService} from '../../../services/auth.service';
import {NPA_ALERTS} from '../consts/messages';
import {NotificationsService} from '../services/notifications.service';


@Component({
    selector: 'npa-confirmation-dialog',
    templateUrl: './confirmation-dialog.component.html',
    styleUrls: ['./confirmation-dialog.component.styl']
})
export class ConfirmationDialogComponent implements OnInit {

    constructor(public dialogRef: MatDialogRef<ConfirmationDialogComponent>,
                public authService: AuthService,
                public notificationsService: NotificationsService,
                @Inject(MAT_DIALOG_DATA) public data: IConfirmationDialogInput) {
    }

    ngOnInit() {
    }

    confirm() {
        this.notificationsService.startLoading();
        this.authService.logoutAPI().subscribe(
            () => {
                this.notificationsService.dismissLoading();
                localStorage.clear();
                window.location.reload();
                this.dialogRef.close({
                    status: true,
                });
            }, (error) => {
                this.notificationsService.error(NPA_ALERTS.ERROR);
                console.error(error);
            }
        );

    }

    cancel() {
        this.dialogRef.close({
            status: false,
        });
    }
}
