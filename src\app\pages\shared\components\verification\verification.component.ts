import {Component, EventEmitter, Input, OnInit, Output} from '@angular/core';
import {ConfirmationService} from '../../services/confirmation.service';
import {IConfirmationDialogOutput} from '../../types/confirmation.type';
import {AuthService} from '../../../../services/auth.service';
import {IVerificationDatabase, VERIFICATION_STYLES} from './verification.types';
import {VerificationService} from './verification.service';
import {HttpErrorResponse} from '@angular/common/http';
import {NotificationsService} from '../../services/notifications.service';
import {composeMessage} from '../../consts/errors';
import {TranslateService} from '../../services/translate.service';


@Component({
    selector: 'npa-verification',
    templateUrl: './verification.component.html',
    styleUrls: ['./verification.component.styl']
})
export class VerificationComponent implements OnInit {
    @Output() stateEmitter = new EventEmitter<IVerificationDatabase>();
    @Input() url: string;
    @Input() id: number;
    lang: string;
    @Input() isGeneralConfirmation: false;
    @Input() style: VERIFICATION_STYLES = VERIFICATION_STYLES.SEPARATE_CARD;
    database: IVerificationDatabase;
    verificationStyles = VERIFICATION_STYLES;
    isProcessing = false;
    hasError = true;

    constructor(public confirmationService: ConfirmationService,
                public authService: AuthService,
                private _verificationService: VerificationService,
                public notificationsService: NotificationsService,
                private translate: TranslateService) {
    }

    ngOnInit() {
        this.lang = this.translate.lang;
    }

    showConfirmationDialog() {
        this
            .confirmationService
            .openDialog({
                message: this.translate.translateKey('DO_YOU_WANT_TO_CONFIRM_DATA'),
            });
    }

    showUnconfirmationDialog() {
        this
            .confirmationService
            .openDialog({
                message: this.translate.translateKey('DO_YOU_WANT_TO_UNCONFIRM_DATA'),
            })
            .then((result: IConfirmationDialogOutput) => {
                if (result.status) {
                    this.isProcessing = true;
                    this.notificationsService.startLoading();
                    this._verificationService.unconfirm(this.url, this.id).subscribe(
                        () => {
                            this.database.confirmation_timestamp = undefined;
                            this.stateEmitter.emit(this.database);
                            this.notificationsService.dismissLoading();
                            this.isProcessing = false;
                        }, (error: HttpErrorResponse) => {
                            this.isProcessing = false;
                            this.notificationsService.error(composeMessage(error));
                            console.error(error);
                        }
                    );
                }
            });
    }

    showApprovalDialog() {
        this
            .confirmationService
            .openDialog({
                message: this.translate.translateKey('DO_YOU_WANT_TO_APPROVE_DATA'),
            })
            .then((result: IConfirmationDialogOutput) => {
                if (result.status) {
                    this.isProcessing = true;
                    this.notificationsService.startLoading();
                    this._verificationService.approve(this.url, this.id).subscribe(
                        () => {
                            this.database.approval_timestamp = new Date();
                            this.stateEmitter.emit(this.database);
                            this.notificationsService.dismissLoading();
                            this.isProcessing = false;
                        }, (error: HttpErrorResponse) => {
                            this.isProcessing = false;
                            this.notificationsService.error(composeMessage(error));
                            console.error(error);
                        }
                    );
                }
            });
    }

    showUnApprovalDialog() {
        this
            .confirmationService
            .openDialog({
                message: this.translate.translateKey('DO_YOU_WANT_TO_UNAPROVE_DATA'),
            })
            .then((result: IConfirmationDialogOutput) => {
                if (result.status) {
                    this.isProcessing = true;
                    this.notificationsService.startLoading();
                    this._verificationService.unapprove(this.url, this.id).subscribe(
                        () => {
                            this.database.approval_timestamp = null;
                            this.stateEmitter.emit(this.database);
                            this.notificationsService.dismissLoading();
                            this.isProcessing = false;
                        }, (error: HttpErrorResponse) => {
                            this.isProcessing = false;
                            this.notificationsService.error(composeMessage(error));
                            console.error(error);
                        }
                    );
                }
            });
    }

}
