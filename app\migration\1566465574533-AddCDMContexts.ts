import {MigrationInterface, QueryRunner} from "typeorm";

export class AddCDMContexts1566465574533 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<any> {
        await queryRunner.query(`
            INSERT INTO
              contexts (slug, url, description, system_id, is_dashboard)
            VALUES
            ('common','','', 5, false)
            `);
    }

    public async down(queryRunner: QueryRunner): Promise<any> {
        await queryRunner.query(`Delete from contexts where slug = 'common' `);
    }


}
