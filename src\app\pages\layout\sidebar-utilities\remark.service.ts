import {Injectable} from '@angular/core';
import {Event, NavigationEnd, Router} from '@angular/router';
import {HttpService} from '../../../services/http.service';
import {SocketService} from '../../shared/services/socket.service';
import {Subject} from 'rxjs';

export const NEW_REMARK_EVENT = 'remark';
export const REPLY_REMARK_EVENT = 'remark:reply';

@Injectable()
export class RemarkService {
    isCurrentPageRemarkable = false;
    hasRegisteredEvents = false;
    newRemarkSubject: Subject<any> = new Subject();
    replyRemarkSubject: Subject<any> = new Subject();

    constructor(private _router: Router,
                private _httpService: HttpService,
                private _socketService: SocketService) {}

    registerEvents(): void {
        if (this.hasRegisteredEvents) {
            return;
        }

        this.isCurrentPageRemarkable = this._isCurrentPageRemarkable();

        this._detectRemarkablePages();

        this._socketService.connection.then((socket) => {
            socket.on(NEW_REMARK_EVENT, (notification: any) => {
                this.newRemarkSubject.next(notification);
            });
        });
        this._socketService.connection.then((socket) => {
                socket.on(REPLY_REMARK_EVENT, (notification: any) => {
                    this.replyRemarkSubject.next(notification);
                });
            });
        this.hasRegisteredEvents = true;
    }

    private _isCurrentPageRemarkable(): boolean {
        const l = this.isCurrentPageRemarkable =
            this._router.url.split(
                '/dashboard/'
            ).length >= 2;
        return l;
    }

    private _detectRemarkablePages(): void {
        this._router.events.subscribe((event: Event) => {
                if (event instanceof NavigationEnd) {
                    this.isCurrentPageRemarkable =
                        event.url.split(
                            '/dashboard/'
                        ).length >= 2;
                }
            }
        );
    }
}
