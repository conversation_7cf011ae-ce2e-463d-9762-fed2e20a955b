import {async, ComponentFixture, fakeAsync, TestBed, tick} from '@angular/core/testing';

import {ProjectListComponent} from './user-list.component';
import {
    MatButtonModule,
    MatCardModule,
    MatCheckboxModule,
    MatDatepickerModule,
    MatDialogModule,
    MatExpansionModule,
    MatFormFieldModule,
    MatIconModule,
    MatInputModule,
    MatNativeDateModule,
    MatPaginatorModule,
    MatRadioModule,
    MatSelectModule,
    MatSnackBarModule,
    MatTableModule,
    MatTooltipModule
} from '@angular/material';
import {FlexLayoutModule} from '@angular/flex-layout';
import {FormsModule, ReactiveFormsModule} from '@angular/forms';
import {CommonModule} from '@angular/common';
import {AuthService} from '../../../services/auth.service';
import {HttpService} from '../../../services/http.service';
import {HttpClient} from '@angular/common/http';
import {ActivatedRoute, Router} from '@angular/router';
import {NotificationsService} from '../../shared/services/notifications.service';
import {ConfirmationAndJustificationService} from '../../shared/services/confirmation-and-justification.service';
import {BrowserAnimationsModule} from '@angular/platform-browser/animations';
import {SharedModule} from '../../shared/shared.module';
import {By} from '@angular/platform-browser';
import {Observable} from 'rxjs/Observable';
import {HttpClientTestingModule} from '@angular/common/http/testing';
import 'rxjs/add/observable/from';

fdescribe('ProjectListComponent', () => {
    let component: ProjectListComponent;
    let fixture: ComponentFixture<ProjectListComponent>;
    let projectListServiceMock;

    class ActivatedRouteStub {
        params: Observable<any>;
    }

    class RouterStub {
    }

    beforeEach(async(() => {
        projectListServiceMock = jasmine.createSpyObj('ProjectListService', ['getDisplayedColumns']);
        TestBed.configureTestingModule({
            imports: [
                CommonModule,
                FormsModule,
                MatCardModule,
                MatTableModule,
                MatExpansionModule,
                FlexLayoutModule,
                MatFormFieldModule,
                MatInputModule,
                MatRadioModule,
                MatSelectModule,
                MatButtonModule,
                MatCheckboxModule,
                MatPaginatorModule,
                MatIconModule,
                MatNativeDateModule,
                MatDatepickerModule,
                ReactiveFormsModule,
                MatTooltipModule,
                MatDialogModule,
                MatSnackBarModule,
                BrowserAnimationsModule,
                SharedModule,
                HttpClientTestingModule
            ],
            declarations: [ProjectListComponent],
            providers: [
                AuthService,
                HttpService,
                HttpClient,
                ProjectService,
                NotificationsService,
                ConfirmationAndJustificationService,
                FormValidationService,
                ProjectListService,
                {provide: Router, useClass: RouterStub},
                {provide: ActivatedRoute, useClass: ActivatedRouteStub},
                // {provide: ProjectListService, useValue: projectListServiceMock},

            ]
        })
            .compileComponents().then(() => {
            fixture = TestBed.createComponent(ProjectListComponent);
            component = fixture.componentInstance;
        });
    }));

    it('should create ProjectListComponent', () => {
        expect(component).toBeTruthy();
    });
    it('the dialog should exist', async(() => {
        fixture.detectChanges();
        const button = fixture.debugElement.query(By.css('#ng-validateTokenAndGenerateNew-financial-fund-btn'));
        button.triggerEventHandler('click', null);
        fixture.whenStable().then(() => {
            const el = document.querySelector('npa-confirmation-and-justification-dialog');
            expect(el).toBeTruthy();
        });
    }));
    it('should open the financial funds dialog', async(() => {
        fixture.detectChanges();
        const service = TestBed.get(ConfirmationAndJustificationService);
        const spy = spyOn(service, 'openDialog');
        component.openFinancialFundsDialog();
        expect(spy).toHaveBeenCalled();
    }));
    it('complaint list title should available', async(() => {
        const list = [
            'flags',
            'options',
            'identification_number',
            'project_title',
            'procurement_type',
            'procurement_method',
            'donor',
            'procurement_plan_approval_date',
            'planned_date_of_signing_contract',
            'confirmation_of_procurement_manager',
            'approval_of_procurement_director',
            'bidding_status',
        ];
        // projectListServiceMock.getDisplayedColumns.and.callFake(() => {
        //     return list;
        // });
        //  const serviceDisplayedTitles = component.complaintListService.getDisplayedColumns(null, null);
        fixture.detectChanges();
        const displayedTitles = component.chosenTitles;
        expect(displayedTitles).toEqual(list);
    }));

  
});
