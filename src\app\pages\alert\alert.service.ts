import {Injectable} from '@angular/core';
import {HttpService} from '../../services/http.service';
import {BehaviorSubject, Subject} from 'rxjs';
import {IAlert, IAlertListItem, IAppRealTimeSate} from './alert.types';
import {HttpErrorResponse} from '@angular/common/http';
import {AuthService} from '../../services/auth.service';
import {environment as environmentLocal} from '../../../environments/environment';
import {SocketService} from '../shared/services/socket.service';
import {NpaDatabase} from '../shared/classes/npa-table';

const environment = environmentLocal;

export const ALERT_EVENT = 'alert';

@Injectable()
export class AlertService {
    private _isRegistered = false;

    // The following observable MUST be subscribed ONCE ONLY,
    // for further subscriptions, please use "appRealTimeStatesRepeater"
    appInitialRealTimeStates = new BehaviorSubject(<IAppRealTimeSate>{});
    appRealTimeStatesRepeater = new BehaviorSubject(<IAppRealTimeSate>{});
    alertSubject: Subject<IAlert> = new Subject<IAlert>();

    constructor(private _httpService: HttpService,
                private _authService: AuthService,
                private _socketService: SocketService) {
        this.init();
    }

    init() {
        if (this._isRegistered) {
            return;
        }
        this._socketService.connection.then((socket) => {
            socket.on(ALERT_EVENT, (alert: IAlert) => {
                this.alertSubject.next(alert);
            });
        });
    }

    index(listType: string) {
        return this._httpService.get(`api/alert/instance?list_type=${listType}`);
    }

    updateAppRealTimeStates(states: IAppRealTimeSate) {
        this.appInitialRealTimeStates.next(states);
    }

    getAppRealTimeStates() {
        return this._httpService.get(`api/alert/instance/check?list_type=new_only`)
            .subscribe((states: IAppRealTimeSate) => {
                this.updateAppRealTimeStates(states);
            }, (error: HttpErrorResponse) => {
            });
    }

    registerAppRealTimeStates() {
        if (this._isRegistered) {
            return;
        }

        if (!environment.production && environment.isAlertPollingDisabled) {
            return;
        }


        if (!this._authService.isUserAuthenticated) {
            return;
        }
        // this.getAppRealTimeStates();
        this._isRegistered = true;
    }

    show(alertInstanceId: number) {
        return this._httpService.get(`api/alert/instance/${alertInstanceId}`);
    }

    isStateTypeNewAlert(states: IAppRealTimeSate) {
        if (!states) {
            return false;
        }
        return states.newAlerts !== undefined;
    }

    shouldConsiderNewAlertForCurrentSession(states: IAppRealTimeSate): boolean {
        const keyToCheck = `${states.newAlerts}|${localStorage.getItem('states.lastLoadedOn')}`;
        return keyToCheck !== localStorage.getItem('states.lastShownNewAlertsForThisSession');
    }

    markTheAlertShownForCurrentSession(states: IAppRealTimeSate): void {
        localStorage.setItem('states.lastShownNewAlertsForThisSession',
            `${states.newAlerts}|${localStorage.getItem('states.lastLoadedOn')}`);
    }

    hasDatabaseUnreadAlertItem(database: NpaDatabase) {
        return database.data.filter((item: IAlertListItem) => item.status_id === 1).length > 0;
    }
}
