import {Component, Inject, OnInit} from '@angular/core';
import {MAT_DIALOG_DATA, MatDialogRef} from '@angular/material';
import {FormBuilder, FormGroup} from '@angular/forms';
import {FormValidationService} from '../../../../services/form-validation.service';

@Component({
    selector: 'npa-archived-reminder-dialog',
    templateUrl: './archived-reminder-dialog.component.html',
    styleUrls: ['./archived-reminder-dialog.component.styl']
})
export class ArchivedReminderDialogComponent implements OnInit {
    archivedReminderDialog: FormGroup;
    solarDate = new Date();

    constructor(public dialogRef: MatDialogRef<ArchivedReminderDialogComponent>,
                @Inject(MAT_DIALOG_DATA) public data: any,
                private formBuilder: FormBuilder,
                public formValidationService: FormValidationService) {
        this.createForm();
    }

    createForm() {
        this.archivedReminderDialog = this.formBuilder.group({
            J<PERSON><PERSON>: ['', this.formValidationService.required.validator],
            <PERSON><PERSON>: ['', this.formValidationService.required.validator],
        });
    };

    save() {
        this.dialogRef.close(this.data);
    }

    ngOnInit() {
    }

}
