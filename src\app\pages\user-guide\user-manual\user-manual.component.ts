import {Component, OnInit} from '@angular/core';
import {NotificationsService} from '../../shared/services/notifications.service';
import {TranslateService} from '../../shared/services/translate.service';

@Component({
    selector: 'npa-user-manual',
    templateUrl: './user-manual.component.html',
    styleUrls: ['./user-manual.component.styl']
})
export class UserManualComponent implements OnInit {

    constructor(private _notificationsService: NotificationsService,
                public translate: TranslateService) {
    }

    ngOnInit() {
        this._notificationsService.dismissLoading();
    }

}
