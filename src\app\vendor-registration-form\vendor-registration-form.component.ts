import {AfterViewInit, Component, On<PERSON><PERSON>roy, OnInit} from '@angular/core';
import {FormBuilder, FormGroup} from '@angular/forms';
import {FormValidationService} from '../services/form-validation.service';
import {AuthService} from '../services/auth.service';
import {TranslateService} from '../pages/shared/services/translate.service';
import {HttpService} from '../services/http.service';
import {NotificationsService} from '../pages/shared/services/notifications.service';
import {Router} from '@angular/router';
import {MatSnackBar} from '@angular/material';
import {environment as localEnvironment} from '../../environments/environment';
import {environment as productionEnvironment} from '../../environments/environment.prod';
import {IPage} from '../pages/shared/types/i-page.interface';
import {HttpErrorResponse} from '@angular/common/http';
import {NPA_ALERTS} from '../pages/shared/consts/messages';
import {VendorRegistrationFormProvider} from './vendor-registration-form-provider';
import {composeMessage} from '../pages/shared/consts/errors';

@Component({
    selector: 'npa-register-vendor',
    templateUrl: './vendor-registration-form.component.html',
    styleUrls: ['./vendor-registration-form.component.styl']
})
export class VendorRegistrationFormComponent implements OnInit, IPage, AfterViewInit, OnDestroy {

    form: FormGroup;
    isLoading = false;
    env = localEnvironment || productionEnvironment;
    submitted = false;
    registrationData;
    lang: string;

    constructor(private _formBuilder: FormBuilder,
                public formValidationService: FormValidationService,
                private _auth: AuthService,
                public translate: TranslateService,
                private _http: HttpService,
                public _notificationsService: NotificationsService,
                private _router: Router,
                private _matSnackBar: MatSnackBar,
                private _registerVendorProvider: VendorRegistrationFormProvider,) {
    }

    ngOnInit() {
        this.lang = localStorage.getItem('lang');
        this.initForm(this.registrationData || {});
    }

    submit(data) {
        this._notificationsService.startLoading();
        this.registrationData = data;
        const sendData = Object.assign(this.form.value);
        this._registerVendorProvider.store(sendData).subscribe(() => {
                this._notificationsService.success(this.translate.translateKey('ACCOUNT_CREATED_SET_YOUR_PASSWORD'));
                setTimeout(() => {
                    window.location.href = this.env.baseUrl.frontEnd.main + 'prs' + '/login';
                }, 6000);
                this.submitted = true;
            }, (error: HttpErrorResponse) => {
                if (error && error.error && typeof error.error === 'string') {
                    if (error.error.substring(18, 30) === 'ER_DUP_ENTRY') {
                        this._notificationsService.error(NPA_ALERTS.RECORD_EXIST);
                    }
                    this._notificationsService.error(composeMessage(error));
                } else {
                    this._notificationsService.error(composeMessage(error));
                    console.error(error);
                }
                this.submitted = false;
            }
        );
    }


    changeLocalStorageLang(param) {
        localStorage.setItem('lang', param);
    }

    initForm(data) {
        this.form = this._formBuilder.group({
            name: [
                data.name, [
                    this.formValidationService.required.validator
                ]
            ],
            last_name: [
                data.last_name, [
                    this.formValidationService.required.validator
                ]
            ],
            username: [
                data.username, [
                    this.formValidationService.username.validator,
                    this.formValidationService.required.validator
                ]
            ],
            email: [
                data.email, [
                    this.formValidationService.emailPattern.validator,
                    this.formValidationService.required.validator
                ]
            ],
            phone_number: [
                data.phone_number, [
                    this.formValidationService.phoneNumberInternational.validator,
                    this.formValidationService.required.validator
                ]],
        });
    }

    ngOnDestroy(): void {
        this._notificationsService.startLoading();
    }

    ngAfterViewInit(): void {
        this._notificationsService.dismissLoading();
    }

    isValid(): boolean {
        return this.form.valid;
    }

}
