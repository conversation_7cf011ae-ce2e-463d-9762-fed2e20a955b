import {MigrationInterface, QueryRunner} from 'typeorm';

export class AddCDMUsers1566467449207 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<any> {
        await queryRunner.query(`
            insert into
            users (name, last_name, username, password, is_single_use_password, email, phone_number, status_id)
            values ('common-data-manager', 'common-data-manager', 'common-data-manager', '${process.env.CUSTOM_CDM_USER_PASSWORD}', 0, '${process.env.CUSTOM_CDM_USER_EMAIL}',
            '0791045747', 1)
            `);
    }

    public async down(queryRunner: QueryRunner): Promise<any> {
        await queryRunner.query(`Delete from users where name = 'common-data-manager'`);
    }

}
