<div class="H2SoFe LZgQXe TFhTPc">
    <div class="RAYh1e LZgQXe" id="initialView">
        <div class="xkfVF" role="presentation" tabindex="null">
            <div class="fctIrd"
                 data-can-enhance="true" data-oauth-third-party-logo-url="" aria-hidden="true">
                <div id="logo" class="SSBsw" title="AGEOPS">
                    <div class="qZp31e">
                        <a [routerLink]="['/', lang, 'login']"> <img width="94" height="57"
                                                                     src="assets/images/ageopsLogoG.svg"></a>
                    </div>
                </div>
            </div>
            <div id="view_container" class="JhUD8d SQNfcc vLGJgb">
                <div class="DRS7Fe bxPAYd k6Zj8d" role="presentation">
                    <div class="jXeDnc">
                        <h1 data-a11y-title-piece="" id="headingText">
                            <content>{{ "CREATE_USER_ACCOUNT_FOR_VENDOR" | translate }}</content>
                        </h1>
                        <!--<div class="Y4dIwd" id="headingSubtext">-->
                        <!--<content>{{ "TO_AGEOPS" | translate }}</content>-->
                        <!--</div>-->
                        <form [formGroup]="form" (ngSubmit)="isValid() && submit(form.value)" fxLayout="column"
                              fxLayoutAlign="center">
                            <div fxFlex="100" fxLayout="column">
                                <mat-form-field fxFlex="100">
                                    <input matInput [placeholder]=' "NAME" | translate' formControlName="name">
                                    <mat-error *ngIf="form?.get('name')?.errors?.required">
                                        {{formValidationService.required.message()}}
                                    </mat-error>
                                </mat-form-field>
                                <mat-form-field fxFlex="100">
                                    <input matInput [placeholder]=' "LAST_NAME" | translate'
                                           formControlName="last_name">
                                    <mat-error *ngIf="form?.get('last_name')?.errors?.required">
                                        {{formValidationService.required.message()}}
                                    </mat-error>
                                </mat-form-field>
                                <mat-form-field fxFlex="100">
                                    <input matInput [placeholder]=' "USER_NAME" | translate' formControlName="username">
                                    <mat-error *ngIf="form?.get('username')?.errors?.required">
                                        {{formValidationService.required.message()}}
                                    </mat-error>
                                    <mat-error *ngIf="form.get('username')?.errors?.pattern">
                                        {{formValidationService.username.message()}}
                                    </mat-error>
                                </mat-form-field>
                                <mat-form-field fxFlex="100">
                                    <input matInput [placeholder]=' "COMPANY_OFFICIAL_EMAIL" | translate'
                                           formControlName="email"
                                           type="email"
                                           lang="en">
                                    <mat-error *ngIf="form.get('email')?.errors?.pattern">
                                        {{formValidationService.emailPattern.message()}}
                                    </mat-error>
                                    <mat-error *ngIf="form?.get('email')?.errors?.required">
                                        {{formValidationService.required.message()}}
                                    </mat-error>
                                </mat-form-field>
                                <mat-form-field fxFlex="100">
                                    <input matInput [placeholder]=' "COMPANY_OFFICIAL_PHONE_NUMBER" | translate'
                                           formControlName="phone_number"
                                           lang="en">
                                    <mat-error *ngIf="form.get('phone_number')?.errors?.pattern">
                                        {{formValidationService.phoneNumberInternational.message()}}
                                    </mat-error>
                                    <mat-error *ngIf="form?.get('phone_number')?.errors?.required">
                                        {{formValidationService.required.message()}}
                                    </mat-error>
                                </mat-form-field>
                            </div>
                            <div class="action" fxFlex="100" fxLayout="column" fxLayoutAlign="center">
                                <button fxFlex="100" class="submit" mat-raised-button type="submit"
                                        [disabled]="!isValid() || _notificationsService.isLoading"
                                        color="primary">{{ "SAVE" | translate }}
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        <footer class="RwBngc">
            <ul class="Bgzgmd">
                <li><a (click)="changeLocalStorageLang('prs')" href="prs/login">دری</a></li>
                <li><a (click)="changeLocalStorageLang('ps')" href="ps/login">پښتو</a></li>
                <li><a (click)="changeLocalStorageLang('en')" href="en/login">English</a></li>
            </ul>
        </footer>
    </div>
</div>

