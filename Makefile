include .env

NODE = usm-node
MYSQL = usm-sql
EXEC = docker exec -it


dev:
	docker-compose up --build --remove-orphans

start:
	yarn start --host 0.0.0.0

dev-db-bash:
	$(EXEC) $(MYSQL) /bin/bash

dev-bash:
	$(EXEC) $(NODE) /bin/bash

dev-db-restore:
	$(EXEC) $(MYSQL) /bin/bash -c "mariadb -u $(TYPEORM_USERNAME)  $(TYPEORM_DATABASE) < /backup.sql "

.PHONY: dev-bash \
		dev \
		dev-db-bash \
		dev-db-restore
