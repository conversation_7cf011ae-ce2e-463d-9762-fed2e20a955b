import {CommonModule} from '@angular/common';
import {NgModule} from '@angular/core';
import {FlexLayoutModule} from '@angular/flex-layout';
import {MaterialModule} from '../../material.module';
import {SharedModule} from './shared/shared.module';
import {MatButtonModule, MatDialogModule, MatExpansionModule, MatInputModule, MatTableModule, MatTooltipModule} from '@angular/material';
import {FormsModule, ReactiveFormsModule} from '@angular/forms';
import {HttpClientModule} from '@angular/common/http';
import {pagesComponents, pagesRoutes} from './pages.routes';
import {RemarkService} from './layout/sidebar-utilities/remark.service';
import {AlertService} from './alert/alert.service';
import {ComplaintGeneralInformationDialogComponent} from './layout/top-banner/complaint-general-information-dialog/complaint-general-information-dialog.component';
import {UserProfileService} from './user-profile/user-profile-service';
import {AddRemarkDialogComponent} from './layout/sidebar-utilities/add-remark-dialog/add-remark-dialog.component';
import {ResetPasswordComponent} from './user-profile/reset-password/reset-password.component';
import {UpdateProfileComponent} from './user-profile/update-profile/update-profile.component';


@NgModule({
    declarations: [
        ...pagesComponents,
    ],
    imports: [
        CommonModule,
        FlexLayoutModule,
        MaterialModule,
        SharedModule,
        MatTooltipModule,
        MatTableModule,
        MatDialogModule,
        MatExpansionModule,
        MatInputModule,
        ReactiveFormsModule,
        FormsModule,
        HttpClientModule,
        pagesRoutes,
        MatButtonModule,
    ],
    entryComponents: [
        ComplaintGeneralInformationDialogComponent,
        AddRemarkDialogComponent,
        ResetPasswordComponent,
        UpdateProfileComponent
    ],
    providers: [
        RemarkService,
        UserProfileService,
        AlertService
    ]
})
export class PagesModule {
    constructor() {

    }
}
