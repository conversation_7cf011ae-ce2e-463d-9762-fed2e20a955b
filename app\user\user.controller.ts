import { UserRole } from './../entity/user-role';
import {Request, Response} from 'express';
import {InstanceService} from '../services/instance.service';
import {FIELDS, IUser, User} from '../entity/user';
import {Database, Model, QBuilderGetMany, QBuilderGetOne} from '../config/database';
import {IController} from '../types/controller';
import {generatePassword, HashService} from '../services/hash.service';
import {MailService} from '../services/mail.service';
import {JwtTokenService} from '../services/jwt-token.service';
import {ISession, Session} from '../entity/session';
import {ISystem, System} from '../entity/system';
import {IUserRole, UserRole} from '../entity/user-role';
import {IRole, Role} from '../entity/role';
import {EmailTextService} from '../services/email-texts/email-text.service';
import {IStatus, Status} from '../entity/status';
import {IUserRolePe, UserRolePe} from '../entity/user-role-pe';
import {IUserRoleSector, UserRoleSector} from '../entity/user-role-sector';
import {In, createQueryBuilder, getRepository} from 'typeorm';
import {IUserRoleRecord, UserRoleRecord} from '../entity/user-role-record';
import {IUserRoleThreshold, UserRoleThreshold} from '../entity/user-role-threshold';
import {bool} from 'aws-sdk/clients/signer';

export class UserController implements IController {
    public static instance: UserController;

    public static getInstance(): UserController {
        return InstanceService.getInstance(UserController);

    }

    public async index(req: Request, res: Response) {
        try {
            const userRoleId = req.query['user_role_id'];
            if (userRoleId) {
                const userRole = <IUserRole | any>await Model(UserRole).findOne({id: userRoleId});
                const user = <IUser | any>await Model(User).findOne({select: FIELDS, where: {id: userRole.user_id}});
                if (!user) {
                    return res.status(404).json();
                }
                return res.status(200).json(user);

            }

            const {pageIndex, pageSize, query} = req.query
            console.log(query)
            const offset = +pageIndex == 0 ?  +pageIndex : (+pageIndex * +pageSize)
            const users = <any>await createQueryBuilder('User')
                    .select(FIELDS.map(field => `User.${field}`))
                    .leftJoinAndSelect('User.userRole', 'userRole')
                    .leftJoinAndSelect('userRole.role', 'role')
                    .leftJoinAndSelect('userRole.userRolePe', 'userRolePe')
                    .leftJoinAndSelect('userRole.userRoleSector', 'userRoleSector')
                    // .where(`User.email like :email`, {email: `%${query}%`})
                    .orderBy("User.id", "DESC")
                    .skip(offset)
                    .take(+pageSize)
                    .getManyAndCount();

            if (users.length == 0) {
                return res.status(404).json(users);

            }
            return res.status(200).json(users);
        } catch (e) {
            return res.status(500).json(e);
        }
    }


    public store(req: Request, res: Response) {
        const userModel = Model(User);
        const data: IUser = req.body;
        HashService.generate(data.password)
            .then(hashedPassword => {
                data.password = hashedPassword;
                const user = userModel.create(data);
                userModel.save(user).then(() => {
                    return res.status(201).json([]);
                }).catch(error => {
                        return res.status(500).send(error);
                    }
                );
            })
            .catch((error) => {
                return console.error(error);
            });
    }

    public show(req: Request, res: Response) {
        Model(User).findOne({select: FIELDS, where: {id: req.params.id}}).then(data => {
            data ?
                res.status(200).json(data) :
                res.status(404).json(data);
        }).catch(error => {
            res.status(500).send(error);
        });
    }

    public async update(req: Request, res: Response) {
      let userRole: IUserRole;
      const userRoleId = req.query['user_id'];
      const roleSlugs = req.body['role_slug'];
      const queryRunner = await Database.QueryRunner();

      await queryRunner.instance.startTransaction();

      try {
          const roleExists = await getRepository(Role)
              .createQueryBuilder('role')
              .select('role.slug')
              .where('role.slug IN (:...slugs)', { slugs: roleSlugs })
              .getCount() > 0;

          if (!roleExists) {
              throw new Error('Invalid user role_slug!!!');
          }

          const userTableData = <IUser>await queryRunner.model(User).findOne({ where: { id: userRoleId } });
          const userRoleTableData = await queryRunner.model(UserRole).find({ where: { user_id: userTableData.id } });

          if (!userTableData) throw new Error('User not found');

          const requestBody = req.body;
          const userData = requestBody['user_data'];
          delete userData.role_data;
          delete userData.role_slug;

          if (!/^[^0-9A-Z_][a-z0-9]+$/.test(userData.username)) {
              throw new Error('username_error: username should only contain lowercase English characters and numbers');
          }

          // Remove existing roles except the one with role_id 9
          for (const userRole of userRoleTableData) {
              if (userRole.role_id !== 9) {
                  await queryRunner.model(UserRole).delete({ id: userRole.id });
              }
          }

          const roles = await queryRunner.model(Role).find({
              where: { slug: In(roleSlugs) }
          });

          const result = await Model(User).update({ id: userTableData.id }, userData);
          if (result.raw.affectedRows === 0) {
              return res.status(404).json(result);
          }

          for (const role of roles) {
              if (role.slug === 'appms-implementation-manager') continue;

              const userRoleData = <IUserRole>await queryRunner.model(UserRole).create({
                  user_id: userTableData.id,
                  role_id: role.id
              });
              userRole = await queryRunner.model(UserRole).save(userRoleData);

              const roleData = requestBody['role_data'] || {};

              switch (role.slug) {
                  case 'cpms-observer': {
                      const peData: IUserRolePe[] = roleData['procurement_entities'] || [];
                      if (peData.length > 0) {
                          peData.forEach(pe => pe.user_role_id = userRole.id);
                          await queryRunner.model(UserRolePe).insert(peData);
                      }

                      const sectorData: IUserRoleSector[] = roleData['sectors'] || [];
                      if (sectorData.length > 0) {
                          sectorData.forEach(sec => sec.user_role_id = userRole.id);
                          await queryRunner.model(UserRoleSector).insert(sectorData);
                      }

                      const recordData: IUserRoleRecord[] = roleData['records'] || [];
                      if (recordData.length > 0) {
                          recordData.forEach(rec => rec.user_role_id = userRole.id);
                          await queryRunner.model(UserRoleRecord).insert(recordData);
                      }

                      if ('is_above_threshold' in roleData) {
                          await queryRunner.model(UserRoleThreshold).insert({
                              user_role_id: userRole.id,
                              is_above_threshold: roleData['is_above_threshold']
                          });
                      }
                      break;
                  }
                  case 'cpms-award-authority':
                  case 'cpms-contract-director':
                  case 'cpms-contract-manager': {
                      const userRolePesData: IUserRolePe[] = roleData['procurement_entities'];

                      console.log(`Inserting PE data for role: ${role.slug}`);
                      console.log('Raw PE check this :', userRolePesData);

                      if (Array.isArray(userRolePesData) && userRolePesData.length > 0) {
                          await queryRunner.model(UserRolePe).delete({ user_role_id: userRole.id });

                          for (const pe of userRolePesData) {
                              pe.user_role_id = userRole.id;
                          }

                          console.log('Final PE data to insert:', userRolePesData);

                          await queryRunner.model(UserRolePe).insert(userRolePesData);
                      } else {
                          console.warn(`No procurement_entities provided for role ${role.slug}`);
                      }

                      break;
                  }
                  case 'cpms-cpm-manager':
                  case 'cpms-specialist': {
                      const userRoleSectorsData: IUserRoleSector[] = roleData['sectors'] || [];

                      if (userRoleSectorsData.length > 0) {
                          userRoleSectorsData.forEach(sec => sec.user_role_id = userRole.id);
                          await queryRunner.model(UserRoleSector).insert(userRoleSectorsData);
                      }
                      break;
                  }
              }
          }

          // Ensure default 'user' role exists
          const usmUserRole = await queryRunner.model(Role).findOne({ where: { slug: 'user' } });

          if (usmUserRole) {
              const usmUserRoleDataExist = await queryRunner.model(UserRole).findOne({
                  where: { user_id: userTableData.id, role_id: usmUserRole.id }
              });

              if (!usmUserRoleDataExist) {
                  const usmUserRoleData = queryRunner.model(UserRole).create({
                      user_id: userTableData.id,
                      role_id: usmUserRole.id
                  });
                  await queryRunner.model(UserRole).save(usmUserRoleData);
              }
          }

          await queryRunner.instance.commitTransaction();
          return res.status(204).json([]);
      } catch (e) {
          console.error(e);
          await queryRunner.instance.rollbackTransaction();
          return res.status(400).json({ message: e.message || 'Error updating user' });
      } finally {
          await queryRunner.instance.release();
      }
  }


    public destroy(req: Request, res: Response) {
        Model(User).delete(req.params.id).then(result => {
            result.raw.affectedRows !== 0 ?
                res.status(204).json([]) :
                res.status(404).json(result);
        }).catch(error => {
            res.status(500).send(error);
        });
    }

    public async resetPassword(req: Request, res: Response) {
        try {
            const userRole = <IUserRole | any>await Model(UserRole).findOne({id: req.query['user_role_id']});
            const data = req.body;
            const hashedPassword = await HashService.generate(data.new_password);
            const result = await Model(User).update(userRole.user_id, {password: hashedPassword});
            result.raw.affectedRows !== 0 ?
                res.status(204).json([]) :
                res.status(404).json(result);
        } catch (error) {
            res.status(500).send(error);
        }
    }

    public async recoverPassword(req: Request, res: Response) {
        try {
            const token = JwtTokenService.getToken({email: req.body.email}, 'recoverPassword');
            const lang = req.params.lang;
            const baseURL = process.env.CUSTOM_FRONT_END_BASE_URL;
            const recover_password_link = process.env.RECOVER_PASSWORD_FORM_ROUTE;
            const link = baseURL + lang + '/' + recover_password_link + '?t=' + token;
            const subject = 'AGEOPS-USM: recover password link';
            const user = <IUser>await Model(User).findOne({where: {email: req.body.email}});
            if (!user) {
                return res.status(200).json([]);
            }
            const system = <ISystem>await Model(System).findOne({slug: 'usm'});
            const userRole = <IUserRole>await QBuilderGetOne({
                entity: 'UserRole',
                conditions: {user_id: user.id},
                relation: {
                    name: 'role',
                    conditions: {system_id: system.id},
                }
            });
            if (user && userRole) {
                const authData: ISession = {
                    client_signature: (<Request>req).headers['user-agent'],
                    revoked: false,
                    user_role_id: userRole.id,
                };
                await Model(Session).save(Model(Session).create(authData));
                const send_email = await MailService.sendEmail(req.body.email, subject, null, EmailTextService.getRecoverPasswordEmail(user, link));
                res.status(200).json(send_email);
            } else {
                res.status(404).json([]);
            }
        } catch (error) {
            res.status(500).send(error);
        }
    }

    public async changePassword(req: Request, res: Response) {
        try {
            const token = req.body.token;
            const decodedData = await JwtTokenService.verifyToken(token, 'recoverPassword');
            const password = await HashService.generate(req.body.new_password);
            if (token) {
                const user = <IUser>await Model(User).findOne({select: FIELDS, where: {email: decodedData['email']}});
                const system = <ISystem>await Model(System).findOne({slug: 'usm'});
                const userRole = <IUserRole>await QBuilderGetOne({
                    entity: 'UserRole',
                    conditions: {user_id: user.id},
                    relation: {
                        name: 'role',
                        conditions: {system_id: system.id},
                    }
                });
                const stored_token = await Model(Session).findOne({where: {user_role_id: userRole.id}});
                if (!stored_token) {
                    return res.status(500).json('link expired!');
                }
                const delete_token = await Model(Session).delete({user_role_id: userRole.id});
                const change_password_result = await Model(User).update(user.id, {password: password});
                if (!delete_token || !change_password_result) {
                    return res.status(500).json('Updating password failed!');
                }
                return res.status(204).json([]);
            } else {
                return res.status(400).json('Token does not exist!');
            }
        } catch (e) {
            console.log(e);
            return res.status(500).json(e);
        }
    }

    public async createVendor(req: Request, res: Response) {
        const queryRunner = await Database.QueryRunner();
        await queryRunner.instance.startTransaction();
        let statusCode = 409;
        try {
            const data: IUser = req.body;
            if (!(/^[^0-9A-Z_][a-z0-9]+$/.test(data.username))) {
                throw new Error('username should only contains lowercase English characters and numbers');
            }
            const password = generatePassword(15);
            data.password = await HashService.generate(password);
            data.status_id = 1;
            data.is_single_use_password = true;
            const userObject = <IUser>await queryRunner.model(User).create(<IUser>data);
            const createdUser = <IUser>await queryRunner.model(User).save(userObject);
            const vendorRegistrarRole = <IRole>await queryRunner.model(Role).findOne({where: {slug: 'avrcs-vendor-registrar'}});
            if (!vendorRegistrarRole) {
                statusCode = 404;
                throw new Error('vendor_registrar_role not found!');
            }
            const userRole = <IRole>await queryRunner.model(Role).findOne({where: {slug: 'user'}});
            if (!userRole) {
                statusCode = 404;
                throw new Error('user_role not found!');
            }
            await queryRunner.model(UserRole).insert(<IUserRole[]>[
                {
                    user_id: createdUser.id,
                    role_id: vendorRegistrarRole.id
                },
                {
                    user_id: createdUser.id,
                    role_id: userRole.id
                }
            ]);
            const subject = 'AGEOPS-USM: user creation';
            await MailService.sendEmail(data.email, subject, null, EmailTextService.getVendorUserCredentialEmail(data.username));
            await queryRunner.instance.commitTransaction();
            res.status(201).json(createdUser.id);
        } catch (e) {
            console.log(e);
            const error = e.toString(); // todo use the .toString to change to string
            await queryRunner.instance.rollbackTransaction();
            res.status(statusCode).json(error);
        } finally {
          return await queryRunner.instance.release();
        }
    }

    public async createACPMSUser(req: Request, res: Response) {
        req = req.body;
        if (req['api_password'] !== process.env.CUSTOM_API_PASSWORD) {
            return res.status(403).json([]);
        }
        const queryRunner = await Database.QueryRunner();
        await queryRunner.instance.startTransaction();
        try {
            if (req['role_slug'] !== 'cpms-award-authority' && req['role_slug'] !== 'cpms-contract-manager' &&
                req['role_slug'] !== 'cpms-cpm-manager' && req['role_slug'] !== 'cpms-specialist' && req['role_slug'] !== 'cpms-observer') {
                throw new Error('Invalid user role_slug!!!');
            }
            const userData: IUser = <any>req;
            if (!(/^[^0-9A-Z_][a-z0-9]+$/.test(userData.username))) {
                throw new Error('username should only contains lowercase English characters and numbers');
            }
            const password = generatePassword(15);
            userData['password'] = await HashService.generate(password);
            const role = <IRole>await queryRunner.model(Role).findOne({where: {slug: req['role_slug']}});
            const status = <IStatus>await queryRunner.model(Status).findOne({where: {slug: req['status_slug']}});
            userData['status_id'] = status.id;
            const userObject = <IUser>await queryRunner.model(User).create(<IUser>userData);
            const user = <IUser>await queryRunner.model(User).save(<IUser>userObject);
            const usmUserRole = <IRole>await queryRunner.model(Role).findOne({where: {slug: 'user'}});

            const userRoleData = <IUserRole>await queryRunner.model(UserRole).create(<IUserRole>{
                user_id: user.id,
                role_id: role.id
            });
            const userRole = <IUserRole>await queryRunner.model(UserRole).save(<IUserRole>userRoleData);
            const usmUserRoleData = <IUserRole>await queryRunner.model(UserRole).create(<IUserRole>{
                user_id: user.id,
                role_id: usmUserRole.id
            });
            await queryRunner.model(UserRole).save(<IUserRole>usmUserRoleData);
            switch (req['role_slug']) {
                case 'cpms-observer':
                    const peData: IUserRolePe[] = 'procurement_entities' in req['role_data'] ? req['role_data']['procurement_entities'] : [];
                    if (peData.length > 0) {
                        peData.forEach(element => {
                            element.user_role_id = userRole.id;
                        });
                        await queryRunner.model(UserRolePe).insert(<IUserRolePe[]>peData);
                    }
                    const sectorData: IUserRoleSector[] = 'sectors' in req['role_data'] ? req['role_data']['sectors'] : [];
                    if (sectorData.length > 0) {
                        sectorData.forEach(element => {
                            element['user_role_id'] = userRole.id;
                        });
                        await queryRunner.model(UserRoleSector).insert(<IUserRoleSector[]>sectorData);
                    }
                    const recordData: IUserRoleRecord[] = 'records' in req['role_data'] ? req['role_data']['records'] : [];
                    if (recordData.length > 0) {
                        recordData.forEach(element => {
                            element['user_role_id'] = userRole.id;
                        });
                        await queryRunner.model(UserRoleRecord).insert(<IUserRoleRecord[]>recordData);
                    }
                    const isAboveThreshold: any = 'is_above_threshold' in req['role_data'] ? req['role_data']['is_above_threshold'] : null;
                    if (isAboveThreshold !== null) {
                        await queryRunner.model(UserRoleThreshold).insert(<IUserRoleThreshold>{
                            user_role_id: userRole.id,
                            is_above_threshold: isAboveThreshold
                        });
                    }
                    break;
                case 'cpms-award-authority':
                case 'cpms-contract-manager':
                    const userRolePesData: IUserRolePe[] = req['role_data']['procurement_entities'];
                    userRolePesData.forEach(element => {
                        element.user_role_id = userRole.id;
                    });
                    await queryRunner.model(UserRolePe).insert(<IUserRolePe[]>userRolePesData);
                    break;
                case 'cpms-cpm-manager':
                case 'cpms-specialist':
                    const userRoleSectorsData: IUserRoleSector[] = req['role_data']['sectors'];
                    userRoleSectorsData.forEach(element => {
                        element['user_role_id'] = userRole.id;
                    });
                    await queryRunner.model(UserRoleSector).insert(<IUserRoleSector[]>userRoleSectorsData);
                    break;
                default:
                    throw new Error('Invalid user role_slug!');
            }
            const subject = 'AGEOPS-USM: user creation';
            await MailService.sendEmail(req['email'], subject, null, EmailTextService.getAcpmsUserCredentialEmail(req['username']));
            await queryRunner.instance.commitTransaction();
            res.status(201).json([]);
        } catch (e) {
            console.log(e);
            const error = e.toString(); // todo use the .toString to change to string
            await queryRunner.instance.rollbackTransaction();
            res.status(403).json(error);
        } finally {
          return await queryRunner.instance.release();
        }
    }
    public async createUser(req: Request, res: Response) {
        let newUser: boolean;
        let userRole: IUserRole;
        req = req.body;
        if (req['api_password'] !== process.env.CUSTOM_API_PASSWORD) {
            return res.status(403).json([]);
        }
        const queryRunner = await Database.QueryRunner();
        await queryRunner.instance.startTransaction();
        const roleExists = await getRepository(Role)
            .createQueryBuilder('role')
            .select('role.slug')
            .where('role.slug IN (:...slugs)', { slugs: req['role_slug'] })
            .getCount() > 0;
        try {
            if (!roleExists) {
                throw new Error('Invalid user role_slug!!!');
            }
            const userData: IUser = <any>req;
            if (!(/^[^0-9A-Z_][a-z0-9]+$/.test(userData.username))) {
                throw new Error('username_error: username should only contains lowercase English characters and numbers');
            }
            const password = generatePassword(15);
            userData['password'] = await HashService.generate(password);
            const roles = await queryRunner.model(Role).find({
                where: {
                  slug: In(req['role_slug'])
                }
            });
            const status = <IStatus>await queryRunner.model(Status).findOne({where: {slug: req['status_slug']}});
            userData['status_id'] = status.id;
            const userTableData = <IUser>await queryRunner.model(User).findOne({where: {email: req['email']}});
            if (userTableData) {
                throw new Error('unique_email: user already exist with ' + req['email'] + ' email');
            }
            newUser = true;
            const userObject = <IUser>await queryRunner.model(User).create(<IUser>userData);
            const user = <IUser>await queryRunner.model(User).save(<IUser>userObject);
            const usmUserRole = <IRole>await queryRunner.model(Role).findOne({where: {slug: 'user'}});
            roles.forEach(async (role) => {
                const userRoleData = <IUserRole>await queryRunner.model(UserRole).create(<IUserRole>{
                    user_id: user.id,
                    role_id: role.id
                });
                userRole = <IUserRole>await queryRunner.model(UserRole).save(<IUserRole>userRoleData);
                let userRolePesData: IUserRolePe[];
                let userRoleSectorsData: IUserRoleSector[];
                switch (role.slug) {
                    case 'appms-implementation-manager':
                    case 'appms-mini-plan-acpms-contract-manager':
                    case 'appms-procurement-plan-manager':
                        userRoleSectorsData = req['role_data']['sectors'];
                        userRolePesData = req['role_data']['procurement_entities'];
                        if (userRolePesData && userRolePesData.length) {
                            userRolePesData.forEach((element) => {
                                element.user_role_id = userRole.id;
                            });
                            await queryRunner.model(UserRolePe).insert(<IUserRolePe[]>userRolePesData);
                        }
                        if (userRoleSectorsData && userRoleSectorsData.length) {
                            userRoleSectorsData.forEach(element => {
                                element['user_role_id'] = userRole.id;
                            });
                            await queryRunner.model(UserRoleSector).insert(<IUserRoleSector[]>userRoleSectorsData);
                        }
                        break;
                    case 'appms-implementation-approver':
                    case 'appms-award-authority':
                        userRoleSectorsData = req['role_data']['sectors'];
                        userRolePesData = req['role_data']['procurement_entities'];
                        if (userRolePesData && userRolePesData.length) {
                            userRolePesData.forEach((element) => {
                                element.user_role_id = userRole.id;
                            });
                            await queryRunner.model(UserRolePe).insert(<IUserRolePe[]>userRolePesData);
                        }
                        if (userRoleSectorsData && userRoleSectorsData.length) {
                            userRoleSectorsData.forEach(element => {
                                element['user_role_id'] = userRole.id;
                            });
                            await queryRunner.model(UserRoleSector).insert(<IUserRoleSector[]>userRoleSectorsData);
                        }
                        break;
                    case 'appms-specialist':
                        userRoleSectorsData = req['role_data']['sectors'];
                        userRolePesData = req['role_data']['procurement_entities'];
                        if (userRolePesData && userRolePesData.length) {
                            userRolePesData.forEach((element) => {
                                element.user_role_id = userRole.id;
                            });
                            await queryRunner.model(UserRolePe).insert(<IUserRolePe[]>userRolePesData);
                        }
                        if (userRoleSectorsData && userRoleSectorsData.length) {
                            userRoleSectorsData.forEach(element => {
                                element['user_role_id'] = userRole.id;
                            });
                            await queryRunner.model(UserRoleSector).insert(<IUserRoleSector[]>userRoleSectorsData);
                        }
                        break;
                    case 'appms-procurement-policy-manager':
                        userRoleSectorsData = req['role_data']['sectors'];
                        userRolePesData = req['role_data']['procurement_entities'];
                        if (userRolePesData && userRolePesData.length) {
                            userRolePesData.forEach((element) => {
                                element.user_role_id = userRole.id;
                            });
                            await queryRunner.model(UserRolePe).insert(<IUserRolePe[]>userRolePesData);
                        }
                        if (userRoleSectorsData && userRoleSectorsData.length) {
                            userRoleSectorsData.forEach(element => {
                                element['user_role_id'] = userRole.id;
                            });
                            await queryRunner.model(UserRoleSector).insert(<IUserRoleSector[]>userRoleSectorsData);
                        }
                        break;
                    case 'appms-procurement-policy-director':
                        userRoleSectorsData = req['role_data']['sectors'];
                        userRolePesData = req['role_data']['procurement_entities'];
                        if (userRolePesData && userRolePesData.length) {
                            userRolePesData.forEach((element) => {
                                element.user_role_id = userRole.id;
                            });
                            await queryRunner.model(UserRolePe).insert(<IUserRolePe[]>userRolePesData);
                        }
                        if (userRoleSectorsData && userRoleSectorsData.length) {
                            userRoleSectorsData.forEach(element => {
                                element['user_role_id'] = userRole.id;
                            });
                            await queryRunner.model(UserRoleSector).insert(<IUserRoleSector[]>userRoleSectorsData);
                        }
                        break;
                    case 'cpms-observer':
                        const peData: IUserRolePe[] = 'procurement_entities' in req['role_data'] ? req['role_data']['procurement_entities'] : [];
                        if (peData.length > 0) {
                            peData.forEach(element => {
                                element.user_role_id = userRole.id;
                            });
                            await queryRunner.model(UserRolePe).insert(<IUserRolePe[]>peData);
                        }
                        const sectorData: IUserRoleSector[] = 'sectors' in req['role_data'] ? req['role_data']['sectors'] : [];
                        if (sectorData.length > 0) {
                            sectorData.forEach(element => {
                                element['user_role_id'] = userRole.id;
                            });
                            await queryRunner.model(UserRoleSector).insert(<IUserRoleSector[]>sectorData);
                        }
                        const recordData: IUserRoleRecord[] = 'records' in req['role_data'] ? req['role_data']['records'] : [];
                        if (recordData.length > 0) {
                            recordData.forEach(element => {
                                element['user_role_id'] = userRole.id;
                            });
                            await queryRunner.model(UserRoleRecord).insert(<IUserRoleRecord[]>recordData);
                        }
                        const isAboveThreshold: any = 'is_above_threshold' in req['role_data'] ? req['role_data']['is_above_threshold'] : null;
                        if (isAboveThreshold !== null) {
                            await queryRunner.model(UserRoleThreshold).insert(<IUserRoleThreshold>{
                                user_role_id: userRole.id,
                                is_above_threshold: isAboveThreshold
                            });
                        }
                        break;
                    case 'cpms-award-authority':
                    case 'cpms-contract-director':
                    case 'cpms-contract-manager':
                        userRolePesData = req['role_data']['procurement_entities'];
                        userRolePesData.forEach(element => {
                            element.user_role_id = userRole.id;
                        });
                        await queryRunner.model(UserRolePe).insert(<IUserRolePe[]>userRolePesData);
                        break;
                    case 'cpms-cpm-manager':
                    case 'cpms-specialist':
                        userRoleSectorsData = req['role_data']['sectors'];
                        userRoleSectorsData.forEach(element => {
                            element['user_role_id'] = userRole.id;
                        });
                        await queryRunner.model(UserRoleSector).insert(<IUserRoleSector[]>userRoleSectorsData);
                        break;
                }
            });
            const usmUserRoleData = <IUserRole>await queryRunner.model(UserRole).create(<IUserRole>{
                user_id: user.id,
                role_id: usmUserRole.id
            });
            await queryRunner.model(UserRole).save(<IUserRole>usmUserRoleData);
            if (newUser) {
                const subject = 'AGEOPS-USM: user creation';
                await MailService.sendEmail(req['email'], subject, null, EmailTextService.getAppmsUserCredentialEmail(req['username']));
            }
            await queryRunner.instance.commitTransaction();
            res.status(201).json([]);
        } catch (e) {
            console.log(e);
            const error = e.toString();
            await queryRunner.instance.rollbackTransaction();
            res.status(400).json({message: error});
        } finally {
          return await queryRunner.instance.release();
        }
    }
    public async createAPPMSUser(req: Request, res: Response) {
        let newUser: boolean;
        let userRole: IUserRole;
        req = req.body;
        if (req['api_password'] !== process.env.CUSTOM_API_PASSWORD) {
            return res.status(403).json([]);
        }
        const queryRunner = await Database.QueryRunner();
        await queryRunner.instance.startTransaction();
        const roleExists = await getRepository(Role)
            .createQueryBuilder('role')
            .select('role.slug')
            .where('role.slug = :slug', {slug: req['role_slug']})
            .getCount() > 0;
        try {
            if (!roleExists) {
                throw new Error('Invalid user role_slug!!!');
            }
            const userData: IUser = <any>req;

            if (!(/^[^0-9A-Z_][a-z0-9]+$/.test(userData.username))) {
                throw new Error('username should only contains lowercase English characters and numbers');
            }
            const password = generatePassword(15);
            userData['password'] = await HashService.generate(password);
            const role = <IRole>await queryRunner.model(Role).findOne({where: {slug: req['role_slug']}});
            const status = <IStatus>await queryRunner.model(Status).findOne({where: {slug: req['status_slug']}});
            userData['status_id'] = status.id;
            const userTableData = <IUser>await queryRunner.model(User).findOne({where: {email: req['email']}});
            if (userTableData) {
                const userRoleTableData = <IUserRole>await queryRunner.model(UserRole).findOne({where: {user_id: userTableData.id, role_id: role.id}});
                if (userRoleTableData) {
                    throw new Error('APPMS ' + req['role_slug'] +' role has already assigned to the ' + userTableData.username +' with ' +req['email']+ ' email');
                }
                const userRoleData = <IUserRole>await queryRunner.model(UserRole).create(<IUserRole>{
                    user_id: userTableData.id,
                    role_id: role.id
                });
                userRole = <IUserRole>await queryRunner.model(UserRole).save(<IUserRole>userRoleData);
                const subject = 'AGEOPS-USM: user creation';
                await MailService.sendEmail(req['email'], subject, null, EmailTextService.appmsRoleGrantingCredentialEmail(userTableData.username));
            } else {
                newUser = true;
                const userObject = <IUser>await queryRunner.model(User).create(<IUser>userData);
                const user = <IUser>await queryRunner.model(User).save(<IUser>userObject);
                const usmUserRole = <IRole>await queryRunner.model(Role).findOne({where: {slug: 'user'}});

                const userRoleData = <IUserRole>await queryRunner.model(UserRole).create(<IUserRole>{
                    user_id: user.id,
                    role_id: role.id
                });
                userRole = <IUserRole>await queryRunner.model(UserRole).save(<IUserRole>userRoleData);
                const usmUserRoleData = <IUserRole>await queryRunner.model(UserRole).create(<IUserRole>{
                    user_id: user.id,
                    role_id: usmUserRole.id
                });
                await queryRunner.model(UserRole).save(<IUserRole>usmUserRoleData);
            }

            let userRolePesData: IUserRolePe[];
            let userRoleSectorsData: IUserRoleSector[];
            switch (req['role_slug']) {
                case 'appms-implementation-manager':
                case 'appms-mini-plan-acpms-contract-manager':
                case 'appms-procurement-plan-manager':
                    userRoleSectorsData = req['role_data']['sectors'];
                    userRolePesData = req['role_data']['procurement_entities'];
                    if (userRolePesData && userRolePesData.length) {
                        userRolePesData.forEach((element) => {
                            element.user_role_id = userRole.id;
                        });
                        await queryRunner.model(UserRolePe).insert(<IUserRolePe[]>userRolePesData);
                    }
                    if (userRoleSectorsData && userRoleSectorsData.length) {
                        userRoleSectorsData.forEach(element => {
                            element['user_role_id'] = userRole.id;
                        });
                        await queryRunner.model(UserRoleSector).insert(<IUserRoleSector[]>userRoleSectorsData);
                    }
                    break;
                case 'appms-implementation-approver':
                case 'appms-award-authority':
                    userRoleSectorsData = req['role_data']['sectors'];
                    userRolePesData = req['role_data']['procurement_entities'];
                    if (userRolePesData && userRolePesData.length) {
                        userRolePesData.forEach((element) => {
                            element.user_role_id = userRole.id;
                        });
                        await queryRunner.model(UserRolePe).insert(<IUserRolePe[]>userRolePesData);
                    }
                    if (userRoleSectorsData && userRoleSectorsData.length) {
                        userRoleSectorsData.forEach(element => {
                            element['user_role_id'] = userRole.id;
                        });
                        await queryRunner.model(UserRoleSector).insert(<IUserRoleSector[]>userRoleSectorsData);
                    }
                    break;
                case 'appms-specialist':
                    userRoleSectorsData = req['role_data']['sectors'];
                    userRolePesData = req['role_data']['procurement_entities'];
                    if (userRolePesData && userRolePesData.length) {
                        userRolePesData.forEach((element) => {
                            element.user_role_id = userRole.id;
                        });
                        await queryRunner.model(UserRolePe).insert(<IUserRolePe[]>userRolePesData);
                    }
                    if (userRoleSectorsData && userRoleSectorsData.length) {
                        userRoleSectorsData.forEach(element => {
                            element['user_role_id'] = userRole.id;
                        });
                        await queryRunner.model(UserRoleSector).insert(<IUserRoleSector[]>userRoleSectorsData);
                    }
                    break;
                case 'appms-procurement-policy-manager':
                    userRoleSectorsData = req['role_data']['sectors'];
                    userRolePesData = req['role_data']['procurement_entities'];
                    if (userRolePesData && userRolePesData.length) {
                        userRolePesData.forEach((element) => {
                            element.user_role_id = userRole.id;
                        });
                        await queryRunner.model(UserRolePe).insert(<IUserRolePe[]>userRolePesData);
                    }
                    if (userRoleSectorsData && userRoleSectorsData.length) {
                        userRoleSectorsData.forEach(element => {
                            element['user_role_id'] = userRole.id;
                        });
                        await queryRunner.model(UserRoleSector).insert(<IUserRoleSector[]>userRoleSectorsData);
                    }
                    break;
                case 'appms-procurement-policy-director':
                    userRoleSectorsData = req['role_data']['sectors'];
                    userRolePesData = req['role_data']['procurement_entities'];
                    if (userRolePesData && userRolePesData.length) {
                        userRolePesData.forEach((element) => {
                            element.user_role_id = userRole.id;
                        });
                        await queryRunner.model(UserRolePe).insert(<IUserRolePe[]>userRolePesData);
                    }
                    if (userRoleSectorsData && userRoleSectorsData.length) {
                        userRoleSectorsData.forEach(element => {
                            element['user_role_id'] = userRole.id;
                        });
                        await queryRunner.model(UserRoleSector).insert(<IUserRoleSector[]>userRoleSectorsData);
                    }
                    break;
            }
            if (newUser) {
                const subject = 'AGEOPS-USM: user creation';
                await MailService.sendEmail(req['email'], subject, null, EmailTextService.getAppmsUserCredentialEmail(req['username']));
            }
            await queryRunner.instance.commitTransaction();
            res.status(201).json([]);
        } catch (e) {
            console.log(e);
            const error = e.toString();
            await queryRunner.instance.rollbackTransaction();
            res.status(400).json({message: error});
        } finally {
          return await queryRunner.instance.release();
        }
    }
}
