import {Component, OnInit} from '@angular/core';
import {IUserProfile} from './user-profile-types';
import {AuthService} from '../../services/auth.service';
import {NotificationsService} from '../shared/services/notifications.service';
import {HttpService} from '../../services/http.service';
import {HttpErrorResponse} from '@angular/common/http';
import {NPA_ALERTS} from '../shared/consts/messages';
import {FormBuilder, FormGroup, Validators} from '@angular/forms';
import {MatDialog} from '@angular/material';
import {ResetPasswordComponent} from './reset-password/reset-password.component';
import {UpdateProfileComponent} from './update-profile/update-profile.component';
import {FormValidationService} from '../../services/form-validation.service';

@Component({
    selector: 'npa-user-profile',
    templateUrl: './user-profile.component.html',
    styleUrls: ['./user-profile.component.styl']
})

export class UserProfileComponent implements OnInit {

    data: IUserProfile;

    constructor(public notificationsService: NotificationsService,
                private formBuilder: FormBuilder,
                private dialog: MatDialog,
                private _httpService: HttpService,
                private _authService: AuthService,
                public formValidationService: FormValidationService) {
    }


    ngOnInit() {
        this.notificationsService.startLoading();
        this._httpService.get(`api/users?user_role_id=${this._authService.loggedInUser.id}`)
            .subscribe((response: IUserProfile) => {
                this.notificationsService.dismissLoading();
                this.data = response;
            }, (error: HttpErrorResponse) => {
                this.notificationsService.error(NPA_ALERTS.ERROR);
                console.error(error);
            });
    }


    openPasswordResetDialog() {
        this.dialog.open(ResetPasswordComponent, {
            width: '300px',
            data: {},
            disableClose: true
        });
    }

    openUserProfileDialog() {
        const dialogRef = this.dialog.open(UpdateProfileComponent, {
            width: '500px',
            data: {},
            disableClose: true
        });
        dialogRef.afterClosed().subscribe(result => {
            if (result['status']) {
                this.data = result['data'];

            }
        });
    }
}
