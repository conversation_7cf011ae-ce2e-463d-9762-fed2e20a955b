import {MigrationInterface, QueryRunner} from 'typeorm';

export class AlterAPPMSRolePermissions1562131837619 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<any> {
        await queryRunner.query(`
            update roles
                set system_id = 4, is_pe_based = true, is_sector_based = true, is_record_based = true
                where slug in (
                    'appms-procurement-plan-manager',
                    'appms-award-authority',
                    'appms-specialist',
                    'appms-procurement-policy-manager',
                    'appms-procurement-policy-director',
                    'appms-contract-progress-monitoring-director',
                    'appms-system-development'
                    )
              `);
    }

    public async down(queryRunner: QueryRunner): Promise<any> {
    }

}
