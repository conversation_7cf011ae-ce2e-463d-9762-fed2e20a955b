import * as jwt from 'jsonwebtoken';
import * as env from 'dotenv';
import * as path from 'path';
import * as fs from 'fs';
import * as  keyPair from 'keypair';

env.load({path: path.resolve(__dirname, '../.env')});

interface IRSAKeys {
    accessPrivate: <PERSON>uffer;
    accessPublic: Buffer;
    refreshPrivate: Buffer;
    refreshPublic: Buffer;
    recoverPasswordPublic: Buffer;
    recoverPasswordPrivate: Buffer;
}

export interface IUserSession {
    token: string;
    singleUseToken?: string;
    redirectToUrl?: string;
    user?: any;
    roles?: any;
}

type TokenType = 'access' | 'refresh' | 'singleUse' | 'recoverPassword' | 'systemToken';

export class JwtTokenService {

    private static RSAKeys: IRSAKeys = {
        accessPublic: undefined,
        accessPrivate: undefined,
        refreshPrivate: undefined,
        refreshPublic: undefined,
        recoverPasswordPublic: undefined,
        recoverPasswordPrivate: undefined
    };

    public static async verifyToken(token, type: TokenType, secretKey?) {
        let secret;
        let algorithm;
        switch (type) {
            case 'access':
                secret = this.RSAKeys.accessPublic;
                algorithm = process.env.JWT_ALGORITHM;
                break;
            case 'refresh':
                secret = this.RSAKeys.refreshPublic;
                algorithm = process.env.JWT_ALGORITHM;
                break;
            case 'singleUse':
                secret = process.env.JWT_SINGEL_USE_TOKEN_SECRET;
                algorithm = process.env.JWT_SECRET_ALGORITHM;
                break;
            case 'recoverPassword':
                secret = this.RSAKeys.recoverPasswordPublic;
                algorithm = process.env.JWT_ALGORITHM;
                break;
            case 'systemToken':
                secret = secretKey;
                algorithm = process.env.JWT_SECRET_ALGORITHM;
                break;
            default:
                throw new Error('Invalid token type!');
                break;

        }
        return new Promise((resolve, reject) => {
            jwt.verify(token, secret, algorithm, (err, decodedToken) => {
                if (err || !decodedToken) {
                    return reject(err);
                }
                resolve(decodedToken);
            });
        });
    }

    public static getToken(data, type: TokenType, secretKey?) {
        switch (type) {
            case 'access':
                return jwt.sign(data,
                    this.RSAKeys.accessPrivate, {
                        algorithm: process.env.JWT_ALGORITHM,
                    });
            case 'refresh':
                return jwt.sign(data,
                    this.RSAKeys.refreshPrivate, {
                        expiresIn: process.env.JWT_REFRESH_EXPIRES_IN,
                        algorithm: process.env.JWT_ALGORITHM,

                    });
            case 'singleUse':
                return jwt.sign(data,
                    process.env.JWT_SINGEL_USE_TOKEN_SECRET, {
                        expiresIn: process.env.JWT_SINGEL_USE_TOKEN_EXPIRES_IN,
                        algorithm: process.env.JWT_SECRET_ALGORITHM,
                    });
            case 'recoverPassword':
                return jwt.sign(data,
                    this.RSAKeys.recoverPasswordPrivate, {
                        expiresIn: process.env.JWT_RECOVER_PASSWORD_EXPIRES_IN,
                        algorithm: process.env.JWT_ALGORITHM,

                    });
            case 'systemToken':
                return jwt.sign(data,
                    secretKey, {
                        algorithm: process.env.JWT_SECRET_ALGORITHM,
                    });
            default:
                throw new Error('Invalid token type!');
                break;

        }


    }


    public static generateRSAs() {
        try {

            if (fs.existsSync(path.resolve() + '/keys/access-private.key') &&
                fs.existsSync(path.resolve() + '/keys/refresh-private.key') &&
                fs.existsSync(path.resolve() + '/keys/recover-password-private.key')) {
                this.RSAKeys['accessPrivate'] = fs.readFileSync(path.resolve() + '/keys/access-private.key');
                this.RSAKeys['accessPublic'] = fs.readFileSync(path.resolve() + '/keys/access-public.pem');
                this.RSAKeys['refreshPrivate'] = fs.readFileSync(path.resolve() + '/keys/refresh-private.key');
                this.RSAKeys['refreshPublic'] = fs.readFileSync(path.resolve() + '/keys/refresh-public.pem');
                this.RSAKeys['recoverPasswordPrivate'] = fs.readFileSync(path.resolve() + '/keys/recover-password-private.key');
                this.RSAKeys['recoverPasswordPublic'] = fs.readFileSync(path.resolve() + '/keys/recover-password-public.pem');

                return;
            }
            const accessPair = keyPair({bits: 2048, e: 65537});
            const refreshPair = keyPair({bits: 2048, e: 65537});
            const recoverPasswordPair = keyPair({bits: 2048, e: 65537});
            this.RSAKeys['accessPrivate'] = accessPair.private;
            this.RSAKeys['accessPublic'] = accessPair.public;
            this.RSAKeys['refreshPrivate'] = refreshPair.private;
            this.RSAKeys['refreshPublic'] = refreshPair.public;
            this.RSAKeys['recoverPasswordPrivate'] = recoverPasswordPair.private;
            this.RSAKeys['recoverPasswordPublic'] = recoverPasswordPair.public;

            fs.writeFile(path.resolve() + '/keys/access-private.key', accessPair.private, function (err) {
                if (err) {
                    return console.error(err);
                }
                console.log('RSA access-private key file generated');
            });

            fs.writeFile(path.resolve() + '/keys/access-public.pem', accessPair.public, function (err) {
                if (err) {
                    return console.error(err);
                }
                console.log('RSA access-public key file generated');
            });
            fs.writeFile(path.resolve() + '/keys/refresh-private.key', refreshPair.private, function (err) {
                if (err) {
                    return console.error(err);
                }
                console.log('RSA refresh-private key file generated');
            });

            fs.writeFile(path.resolve() + '/keys/refresh-public.pem', refreshPair.public, function (err) {
                if (err) {
                    return console.error(err);
                }
                console.log('RSA refresh-public key file generated');
            });
            fs.writeFile(path.resolve() + '/keys/recover-password-private.key', recoverPasswordPair.private, function (err) {
                if (err) {
                    return console.error(err);
                }
                console.log('RSA recover-password-private key file generated');
            });

            fs.writeFile(path.resolve() + '/keys/recover-password-public.pem', recoverPasswordPair.public, function (err) {
                if (err) {
                    return console.error(err);
                }
                console.log('RSA recover-password-public key file generated');
            });
        } catch (e) {
            throw e;
        }
    }


}

