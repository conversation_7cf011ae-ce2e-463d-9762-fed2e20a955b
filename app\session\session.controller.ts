import {Request, Response} from 'express';
import {InstanceService} from '../services/instance.service';
import {Model, QBuilderGetOne} from '../config/database';
import {FIELDS, IUser, User} from '../entity/user';
import {IUserSession, JwtTokenService} from '../services/jwt-token.service';
import {ISystem, System} from '../entity/system';
import {ISession, Session} from '../entity/session';
import {IUserRole, UserRole} from '../entity/user-role';
import {Role} from '../entity/role';
import {Context} from '../entity/context';
import {createQueryBuilder, In} from 'typeorm';


export class SessionController {
    public static instance: SessionController;

    public static getInstance(): SessionController {
        return InstanceService.getInstance(SessionController);

    }

    public async login(req: Request, res: Response) {
        try {
            const user: IUser | any = <IUser>await Model(User).findOne({select: FIELDS, where: {username: req.body.username}});
            const selectedSystem = req.query['redirected_url'] ? <ISystem>await Model(System)
                .findOne({url: req.query['redirected_url']}) : null;
            const usm: ISystem = <ISystem>await Model(System).findOne({slug: 'usm'});
            const userRole: IUserRole | any = <IUserRole>await QBuilderGetOne({
                entity: 'UserRole',
                conditions: {user_id: user.id},
                relation: {
                    name: 'role',
                    conditions: {system_id: usm.id},
                }
            });

            const authData: ISession = {
                client_signature: (<Request>req).headers['user-agent'],
                revoked: false,
                user_role_id: userRole.id,
            };
            const session = <ISession>await Model(Session).save(Model(Session).create(authData));
            const token = JwtTokenService.getToken({
                username: user.username,
                email: user.email,
                user_role_id: userRole.id,
                role: userRole.role.slug,
                jti: session.id
            }, 'access');

            const createdUserSession: IUserSession = {
                token: token,
            };
            if (selectedSystem) {
                const selectedSystemUserRole: IUserRole | any = <IUserRole>await QBuilderGetOne({
                    entity: 'UserRole',
                    conditions: {user_id: user.id},
                    relation: {
                        name: 'role',
                        conditions: {system_id: selectedSystem.id},
                    }
                });
                if (selectedSystemUserRole) {
                    createdUserSession.singleUseToken = JwtTokenService
                        .getToken({
                            system: selectedSystem.slug,
                            user_role_id: selectedSystemUserRole.id
                        }, 'singleUse');
                }
            }


            userRole.role.contexts = await Model(Context).find({where: {system_id: usm.id}});
            user.role = userRole.role;
            // sets the userId to userRoleId because current systems uses userId but the actual user is determined by userRoleId
            user.id = userRole.id;
            return res.status(200).json({
                user: user,
                session: createdUserSession
            });
        } catch (error) {
            // todo proper exception handling
            return res.status(401).send(error.message);
        }
    }

    public async singleUseTokenAuthentication(req: Request, res: Response) {
        try {
            const decodedToken = await JwtTokenService.verifyToken(req.query['singleUseToken'], 'singleUse');
            const userRole = <IUserRole | any>await Model(UserRole)
                .findOne({where: {id: decodedToken['user_role_id']}, relations: ['role']});

            const authData: ISession = {
                client_signature: (<Request>req).headers['user-agent'],
                revoked: false,
                user_role_id: userRole.id,
            };
            const session = <ISession>await Model(Session).save(Model(Session).create(authData));
            const user = <any>await Model(User).findOne({select: FIELDS, where: {id: userRole.user_id}});
            const token = JwtTokenService
                .getToken({
                    user_role_id: userRole.id,
                    username: user.username,
                    email: user.email,
                    role: userRole.role.slug,
                    jti: session.id
                }, 'systemToken', req.query['key']);
            const createdUserSession: IUserSession = {
                token: token,
            };
            const roles = await Model(Role).find({where: {system_id: userRole.role.system_id}});
            const contextResult = await createQueryBuilder('Context')
                .leftJoin('Context.contextAccesses', 'contextAccesses')
                .leftJoinAndSelect('contextAccesses.operation', 'operation')
                .addSelect('contextAccesses')
                .where(`contextAccesses.role_id=:role_id`, {role_id: userRole.role.id})
                .getMany();

            userRole.role.contexts = contextResult;
            user.role = userRole.role;
            // sets the userId to userRoleId because current systems uses userId but the actual user is determined by userRoleId
            user.id = userRole.id;
            return res.status(200).json({session: createdUserSession, user: user, roles: roles});
        } catch (error) {
            // todo proper exception handling
            return res.status(401).send(error.message);
        }

    }


    public async systemUserTokenVerification(req: Request, res: Response) {
        return res.status(200).json({
            username: res.locals.username,
            email: res.locals.email,
            role: res.locals.role,
            user_id: res.locals.user_role_id
        });
    }

    public async systemInitiate(req: Request, res: Response) {
        try {
            const userRole = <IUserRole | any>await Model(UserRole)
                .findOne({where: {id: res.locals.user_role_id}, relations: ['role']});
            const user = <IUser | any>await Model(User).findOne({select: FIELDS, where: {id: userRole.user_id}});
            const roles = await Model(Role).find({where: {system_id: userRole.role.system_id}});
            const contextResult = await createQueryBuilder('Context')
                .leftJoin('Context.contextAccesses', 'contextAccesses')
                .leftJoinAndSelect('contextAccesses.operation', 'operation')
                .addSelect('contextAccesses')
                .where(`contextAccesses.role_id=:role_id`, {role_id: userRole.role.id})
                .getMany();

            userRole.role.contexts = contextResult;

            user.role = userRole.role;
            // sets the userId to userRoleId because current systems front ends uses userId but the actual user is determined by userRoleId
            user.id = userRole.id;
            return res.status(200).json({user: user, roles: roles});
        } catch (error) {
            // todo proper exception handling
            return res.status(401).send(error.message);
        }
    }

    public async getSingleUseToken(req: Request, res: Response) {
        try {
            const redirectedUrl: string = req.query['redirected_url'];
            let updatedRedirectedUrl: string = redirectedUrl;

            if (!updatedRedirectedUrl.includes('toAuth=true')) {
                updatedRedirectedUrl += '?toAuth=true';
            }
            let system;
            system = <ISystem>await Model(System).findOne({url: redirectedUrl});

            if (!system) {
                system = <ISystem>await Model(System).findOne({url: updatedRedirectedUrl});
            }

            const usmUserRole = <IUserRole | any>await Model(UserRole).findOne({where: {id: res.locals.user_role_id}, relations: ['role']});
            const userRole = <IUserRole>await QBuilderGetOne({
                entity: 'UserRole',
                conditions: {user_id: usmUserRole.user_id},
                relation: {
                    name: 'role',
                    conditions: {system_id: system.id},
                }
            });
            if (!userRole) {
                return res.status(404).json();
            }
            return res.status(200).json(
                {
                    singleUseToken: JwtTokenService.getToken({system: system.slug, user_role_id: userRole.id}, 'singleUse'),
                });
        } catch (e) {
            return res.status(401).json(e);
        }
    }

    public async revokeSession(req: Request, res: Response) {
        try {
            const userRoleId = res.locals.user_role_id;
            const userRole = <IUserRole>await Model(UserRole).findOne({id: userRoleId});
            const userRoles = <any>await Model(UserRole).find({where: {user_id: userRole.user_id}, select: ['id']});
            const ids = [];
            userRoles.forEach(item => {
                ids.push(item.id);
            });
            const deleted = await Model(Session).delete({user_role_id: In(ids)});
            if (deleted.raw.affectedRows === 0) {
                return res.status(404).json('No session found!');
            }
            return res.status(204).json([]);
        } catch (e) {
            return res.status(500).json(e);
        }
    }

    public async verifyRecoverPasswordToken(req: Request, res: Response) {
        try {
            const token = req.query['token'];
            if (token) {
                const decodedData = await JwtTokenService.verifyToken(token, 'recoverPassword');
                return res.status(200).json({isTokenValidated: true, email: decodedData['email']});
            } else {
                return res.status(400).json('token does not exist!');
            }

        } catch (e) {
            return res.status(401).json(e);
        }
    }
}
