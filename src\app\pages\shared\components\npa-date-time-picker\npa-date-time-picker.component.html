<!--<div class="dateTimePicker" [ngClass]="{'focus': isFocused && !npaDateModel, 'npa-error': npaDateModel?.invalid && npaDateModel?.touched}" (click)="isFocused = true">-->
    <!--<label class="label" [ngClass]="{'float': date}">{{placeholder}}</label>-->
    <!--<dp-date-picker-->
            <!--(click)="markAsTouched()"-->
            <!--[config]="datePickerConfig"-->
            <!--[ngModel]="date"-->
            <!--(ngModelChange)="onChangeInput($event)"-->
            <!--mode="day"-->
            <!--theme="dp-material"-->
            <!--#datePicker>-->
    <!--</dp-date-picker>-->
    <!--<mat-icon>date_range</mat-icon>-->
<!--</div>-->
