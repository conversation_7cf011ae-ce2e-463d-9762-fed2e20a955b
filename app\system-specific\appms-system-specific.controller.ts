import {InstanceService} from '../services/instance.service';
import {Request, Response} from 'express';
import {Database} from '../config/database';
import {IUserRoleRecord, UserRoleRecord} from '../entity/user-role-record';
import {createQueryBuilder} from 'typeorm';
import {HelperService} from '../services/helper.service';

export class AppmsSystemSpecificController {
    public static instance: AppmsSystemSpecificController;

    public static getInstance(): AppmsSystemSpecificController {
        return InstanceService.getInstance(AppmsSystemSpecificController);
    }


    public async assignProjectToUser(req: Request, res: Response) {
        const queryRunner = await Database.QueryRunner();
        try {
            const data: IUserRoleRecord = req.body;
            await queryRunner.instance.startTransaction();
            const userRoleRecordObject = <any>await queryRunner.model(UserRoleRecord).create(<any>{
                record_id: data.record_id,
                user_role_id: data.user_role_id
            });
            const userRoleRecord = <any>await queryRunner.model(UserRoleRecord).save(<any>userRoleRecordObject);
            await queryRunner.instance.commitTransaction();
            res.status(201).json(userRoleRecord.id);
        } catch (e) {
            console.log(e);
            await queryRunner.instance.rollbackTransaction();
            res.status(500).json(e);
        } finally {
          return await queryRunner.instance.release();
        }
    }

    public async getProcurementPlanManagerPEs(req: Request, res: Response) {
        const userRoleId = req.params['user_role_id'];

        try {
            if (userRoleId === null || userRoleId === undefined) {
                throw new Error('`user_role_id` is required.');
            }
            const entities = <any>await createQueryBuilder('UserRolePe')
                .select('UserRolePe.procurement_entity_id')
                .innerJoin('UserRole', 'user_roles')
                .where(`UserRolePe.user_role_id=:userRoleId`, {userRoleId: userRoleId})
                .getMany();

            return res.status(200).json(entities);
        } catch (e) {
            console.log(e);
            return res.status(500).json(e);
        }
    }

    public async getRole(req: Request, res: Response) {
        const userRoleId = req.params['user_role_id'];
        try {
            if (userRoleId === null || userRoleId === undefined) {
                throw new Error('`user_role_id` is required.');
            }

            const entity = <any>await createQueryBuilder('Role')
                .innerJoinAndSelect('UserRole', 'user_role', 'user_role.role_id = Role.id')
                .where(`user_role.id= ${userRoleId}`)
                .getOne();

            return res.status(200).json(entity);
        } catch (e) {
            console.log(e);
            return res.status(500).json(e);
        }
    }

    public async getAwardAuthority(req: Request, res: Response) {
        try {
            const role = <any>await createQueryBuilder('Role')
                .where('Role.slug = :slug', {slug: 'appms-award-authority'})
                .getOne();

            const query = createQueryBuilder('User', 'user')
                .leftJoinAndSelect('user.userRole', 'userRole')
                .leftJoinAndSelect('userRole.role', 'role')
                .where(`role.slug=:slug `, {slug: 'appms-award-authority'});

            if (role.is_pe_based && req.query['procurement_entity_id']) {
                query
                    .leftJoin('userRole.userRolePe', 'userRolePe')
                    .addSelect('user.username')
                    .addSelect('user.last_name')
                    .addSelect('user.email')
                    .andWhere('userRolePe.procurement_entity_id=:pe_id', {pe_id: req.query['procurement_entity_id']});
            } else if (role.is_sector_based && req.query['sector_id']) {
                query
                    .leftJoin('userRole.userRoleSector', 'userRoleSector')
                    .addSelect('user.name')
                    .addSelect('user.last_name')
                    .addSelect('user.email')
                    .andWhere('userRoleSector.sector_id=:sector_id', {sector_id: req.query['sector_id']});
            }

            return res.status(200).json(await query.getMany());
        } catch (e) {
            console.log(e);
            return res.status(500).json(e);
        }
    }

    public async getProcurementPlanManager(req: Request, res: Response) {
        try {
            const role = <any>await createQueryBuilder('Role')
                .where('Role.slug = :slug', {slug: 'appms-procurement-plan-manager'})
                .getOne();

            const query = createQueryBuilder('User', 'user')
                .leftJoinAndSelect('user.userRole', 'userRole')
                .leftJoinAndSelect('userRole.role', 'role')
                .where(`role.slug=:slug `, {slug: 'appms-procurement-plan-manager'});

            if (role.is_record_based && req.query['record_id']) {
                query
                    .leftJoin('userRole.userRoleRecord', 'userRoleRecord')
                    .addSelect('user.name')
                    .addSelect('user.last_name')
                    .addSelect('user.email')
                    .andWhere('userRoleRecord.record_id=:record_id', {record_id: req.query['record_id']});
            } else if (role.is_pe_based && req.query['procurement_entity_id']) {
                query
                    .leftJoin('userRole.userRolePe', 'userRolePe')
                    .addSelect('user.name')
                    .addSelect('user.last_name')
                    .addSelect('user.email')
                    .andWhere('userRolePe.procurement_entity_id=:pe_id', {pe_id: req.query['procurement_entity_id']});
            } else if (role.is_sector_based && req.query['sector_id']) {
                query
                    .leftJoin('userRole.userRoleSector', 'userRoleSector')
                    .addSelect('user.name')
                    .addSelect('user.last_name')
                    .addSelect('user.email')
                    .andWhere('userRoleSector.sector_id=:sector_id', {sector_id: req.query['sector_id']});
            }

            return res.status(200).json(await query.getMany());
        } catch (e) {
            console.log(e);
            return res.status(500).json(e);
        }
    }

    public async getSpecialist(req: Request, res: Response) {
        try {
            const role = <any>await createQueryBuilder('Role')
                .where('Role.slug = :slug', {slug: 'appms-specialist'})
                .getOne();

            const query = createQueryBuilder('User', 'user')
                .leftJoinAndSelect('user.userRole', 'userRole')
                .leftJoinAndSelect('userRole.role', 'role')
                .where(`role.slug=:slug `, {slug: 'appms-specialist'});

            if (role.is_record_based && req.query['record_id']) {
                query
                    .leftJoin('userRole.userRoleRecord', 'userRoleRecord')
                    .addSelect('user.name')
                    .addSelect('user.last_name')
                    .addSelect('user.email')
                    .addSelect('role.id')
                    .andWhere('userRoleRecord.record_id=:record_id', {record_id: req.query['record_id']});
            } else if (role.is_pe_based && req.query['procurement_entity_id']) {
                query
                    .leftJoin('userRole.userRolePe', 'userRolePe')
                    .addSelect('user.name')
                    .addSelect('user.last_name')
                    .addSelect('user.email')
                    .addSelect('role.id')
                    .andWhere('userRolePe.procurement_entity_id=:pe_id', {pe_id: req.query['procurement_entity_id']});
            } else if (role.is_sector_based && req.query['sector_id']) {
                query
                    .leftJoin('userRole.userRoleSector', 'userRoleSector')
                    .addSelect('user.name')
                    .addSelect('user.last_name')
                    .addSelect('user.email')
                    .addSelect('role.id')
                    .andWhere('userRoleSector.sector_id=:sector_id', {sector_id: req.query['sector_id']});
            }

            return res.status(200).json(await query.getMany());
        } catch (e) {
            console.log(e);
            return res.status(500).json(e);
        }
    }

    public async getProcurementPolicyManager(req: Request, res: Response) {
        try {
            const user = <any>await createQueryBuilder('User', 'user')
                .leftJoinAndSelect('user.userRole', 'userRole')
                .leftJoinAndSelect('userRole.role', 'role')
                .where(`role.slug=:slug`, {slug: 'appms-procurement-policy-manager'})
                .getOne();
            return res.status(200).json(user);
        } catch (e) {
            console.log(e);
            return res.status(500).json(e);
        }
    }

    public async getProcurementPolicyDirector(req: Request, res: Response) {
        try {
            const user = <any>await createQueryBuilder('User', 'user')
                .leftJoinAndSelect('user.userRole', 'userRole')
                .leftJoinAndSelect('userRole.role', 'role')
                .where(`role.slug=:slug`, {slug: 'appms-procurement-policy-director'})
                .getOne();
            return res.status(200).json(user);
        } catch (e) {
            console.log(e);
            return res.status(500).json(e);
        }
    }

    public async getUsersByIds(req: Request, res: Response) {
        try {
            const ids = HelperService.normalizeQueryParams(req.query['ids']);
            const query = <any>await createQueryBuilder('UserRole')
                .select('UserRole.id')
                .leftJoin('UserRole.user', 'user')
                .leftJoin('UserRole.role', 'role')
                .addSelect('user.name')
                .addSelect('user.last_name')
                .addSelect('user.email')
                .addSelect('user.username')
                .addSelect('role.slug')
                .where(`UserRole.id in (:...ids)`, {ids: ids})
                .getMany();
            query.forEach((item) => {
                item['full_name'] = (item.user.name === item.user.last_name) ? item.user.name : item.user.name + ' ' + item.user.last_name;
                item['role_slug'] = item.role.slug;
                item['email'] = item.user.email;
                item['username'] = item.user.username;
                delete item.user;
                delete item.role;
            });
            return res.status(200).json(query);
        } catch (e) {
            console.log(e);
            return res.status(500).json(e);
        }
    }
}
