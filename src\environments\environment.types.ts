export interface IEnvironment {
    production?: boolean;
    logInData?: any;
    contextsList: any;
    isAlertPollingDisabled?: boolean;
    alertPollIntervalInMin?: number;
    loginThroughDataBase: boolean;
    isAwsS3Used: boolean;
    isWebSocketEnabled: boolean;
    baseUrl: {
        frontEnd: {
            main: string
        },
        backend: {
            main: string
        },
        notificationManager: {
            host: string
            port: number
        }
        ageops: {
            main: string
        }
    };
    stickyMessage?: {
        shouldShow?: boolean,
        message?: string
    };
}
