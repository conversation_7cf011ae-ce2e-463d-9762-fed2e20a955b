import {COMPONENT_NAME} from './da-component-name.service';

export interface IMenuItem {
    path: string;
    title: string;
    icon: string;
    selected: boolean;
    isContractChild?: boolean;
    children?: IMenuItem[];
    isVisibleParent?: boolean;
}

export const MENU_ITEM: IMenuItem[] = [
    {
        path: 'dashboard',
        title: COMPONENT_NAME['dashboard'],
        icon: 'dashboard',
        selected: false,
        isContractChild: false,
        isVisibleParent: true,
        children: [
            {
                path: 'charts',
                title: COMPONENT_NAME['charts'],
                icon: 'dashboard',
                selected: false,
                isContractChild: false,

            },
            {
                path: 'contract-list',
                title: COMPONENT_NAME['contract-list'],
                icon: 'dashboard',
                selected: false,
                isContractChild: false,
            }
        ]
    },
    {
        path: 'contract-general-info',
        title: COMPONENT_NAME['contract-general-info'],
        icon: 'info',
        selected: false,
        isContractChild: true,
        isVisibleParent: false,
        children: [
            {
                path: 'info-view',
                title: COMPONENT_NAME['info-view'],
                icon: 'info',
                selected: false,
                isContractChild: false,

            },
            {
                path: 'info-add',
                title: COMPONENT_NAME['info-add'],
                icon: 'info',
                selected: false,
                isContractChild: false,
            },
            {
                path: 'exception',
                title: COMPONENT_NAME['exception'],
                icon: 'info',
                selected: false,
                isContractChild: false,
            }
        ]
    },
    {
        path: 'contract-planning',
        title: COMPONENT_NAME['contract-planning'],
        icon: 'content_paste',
        selected: false,
        isContractChild: true,
        isVisibleParent: false,
        children: [
            {
                path: 'contract-approval',
                title: COMPONENT_NAME['contract-approval'],
                icon: 'content_paste',
                selected: false,
                isContractChild: false,

            },
            {
                path: 'procurement-entity-contact-person',
                title: COMPONENT_NAME['procurement-entity-contact-person'],
                icon: 'content_paste',
                selected: false,
                isContractChild: false,

            },
            {
                path: 'contract-execution-location',
                title: COMPONENT_NAME['contract-execution-location'],
                icon: 'content_paste',
                selected: false,
                isContractChild: false,

            },
            {
                path: 'contract-details',
                title: COMPONENT_NAME['contract-details'],
                icon: 'content_paste',
                selected: false,
                isContractChild: false,

            },
            {
                path: 'company',
                title: COMPONENT_NAME['company'],
                icon: 'content_paste',
                selected: false,
                isContractChild: false,
            },
            {
                path: 'subcontract',
                title: COMPONENT_NAME['subcontract'],
                icon: 'content_paste',
                selected: false,
                isContractChild: false,
            },
            {
                path: 'finance-affairs-physical-progress',
                title: COMPONENT_NAME['finance-affairs-physical-progress'],
                icon: 'content_paste',
                selected: false,
                isContractChild: false,
            },
            {
                path: 'liquidated-damages-period',
                title: COMPONENT_NAME['liquidated-damages-period'],
                icon: 'content_paste',
                selected: false,
                isContractChild: false,
            }
        ]
    },
    {
        path: 'contract-progress',
        title: COMPONENT_NAME['contract-progress'],
        icon: 'history',
        selected: false,
        isContractChild: true,
        isVisibleParent: false,
        children: [
            {
                path: 'contract',
                title: COMPONENT_NAME['contract'],
                icon: 'history',
                selected: false,
                isContractChild: false,
            },
            {
                path: 'finance-affairs-physical-progress',
                title: COMPONENT_NAME['finance-affairs-physical-progress'],
                icon: 'history',
                selected: false,
                isContractChild: false,
            },
            // contractService.oneToOneScanData.contractPlanning.isContractDetailsDelayPenaltyApplicable
            {
                path: 'delay-penalty',
                title: COMPONENT_NAME['delay-penalty'],
                icon: 'history',
                selected: false,
                isContractChild: false,
            },
            {
                path: 'defect-liability',
                title: COMPONENT_NAME['defect-liability'],
                icon: 'history',
                selected: false,
                isContractChild: false,
            }
        ]
    },
    {
        path: 'contract-amendments',
        title: COMPONENT_NAME['contract-amendments'],
        icon: 'library_add',
        selected: false,
        isContractChild: true,
        isVisibleParent: false,
        children: [
            {
                path: 'amendments',
                title: COMPONENT_NAME['amendments'],
                icon: 'library_add',
                selected: false,
                isContractChild: false,
            }
        ]
    },
    {
        path: 'challenges-and-remarks',
        title: COMPONENT_NAME['challenges-and-remarks'],
        icon: 'sync_problem',
        selected: false,
        isContractChild: true,
        isVisibleParent: false,
        children: [
            {
                path: 'procurement-entity-actions',
                title: COMPONENT_NAME['procurement-entity-actions'],
                icon: 'sync_problem',
                selected: false,
                isContractChild: false,
            },
            {
                path: 'npa-other-comments',
                title: COMPONENT_NAME['npa-other-comments'],
                icon: 'sync_problem',
                selected: false,
                isContractChild: false,
            }
        ]
    },
    {
        path: 'reports',
        title: COMPONENT_NAME['reports'],
        icon: 'report',
        selected: false,
        isContractChild: false,
        isVisibleParent: true,
        children: [
            {
                path: 'procurement-entity-profile',
                title: COMPONENT_NAME['procurement-entity-profile'],
                icon: 'report',
                selected: false,
                isContractChild: false,
            }
        ]
    },
    {
        path: 'user-guide',
        title: COMPONENT_NAME['user-guide'],
        icon: 'help',
        selected: false,
        isContractChild: false,
        isVisibleParent: true,
        children: [
            {
                path: 'system-information',
                title: COMPONENT_NAME['system-information'],
                icon: 'help',
                selected: false,
                isContractChild: false,
            },
            {
                path: 'change-log',
                title: COMPONENT_NAME['change-log'],
                icon: 'help',
                selected: false,
                isContractChild: false,
            },
            {
                path: 'contact-information',
                title: COMPONENT_NAME['contact-information'],
                icon: 'help',
                selected: false,
                isContractChild: false,
            },
            {
                path: 'user-manual',
                title: COMPONENT_NAME['user-manual'],
                icon: 'help',
                selected: false,
                isContractChild: false,
            },
            {
                path: 'reset-system',
                title: COMPONENT_NAME['reset-system'],
                icon: 'help',
                selected: false,
                isContractChild: false,
            }
        ]
    }

];
