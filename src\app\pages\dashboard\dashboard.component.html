<!--<div fxLayout="column" class="exceptional-mat-button-wrapper">-->
    <!--<mat-card fxFlex="100">-->
        <!--<mat-card-header class="main-page-bottom-border">-->
            <!--{{'DASHBOARD_INCLUDE_FOLLOWING_PART'|translate}}-->
        <!--</mat-card-header>-->
        <!--<mat-card-content fxLayout="column">-->
            <!--<div fxFlex="100" *ngIf="authService.user.value.role.slug !== 'dev-ops'">-->
                <!--<a mat-button-->
                   <!--type="button"-->
                   <!--routerLink="/{{lang}}/{{authService.user.value.role.slug}}/{{contextService.context}}/dashboard/charts">-->
                    <!--{{'CHARTS'|translate}}</a>-->
            <!--</div>-->
        <!--</mat-card-content>-->
    <!--</mat-card>-->

    <!--&lt;!&ndash;<div fxLayout="row wrap" class="scorecards-wrapper">&ndash;&gt;-->
    <!--&lt;!&ndash;<npa-scorecard iconName="person" fxFlex="25">&ndash;&gt;-->
    <!--&lt;!&ndash;<label title>بازدید تمام صفحات</label>&ndash;&gt;-->
    <!--&lt;!&ndash;<label value>52000</label>&ndash;&gt;-->
    <!--&lt;!&ndash;<label text>در کل</label>&ndash;&gt;-->
    <!--&lt;!&ndash;<label footer>در طول سال</label>&ndash;&gt;-->
    <!--&lt;!&ndash;<label back> این بخش دفعات بازدید (یک بار وارد شدن در سیستم و بیشتر از 30 ثانیه ماندن در سیستم)را نشان می دهد.</label>&ndash;&gt;-->
    <!--&lt;!&ndash;</npa-scorecard>&ndash;&gt;-->

    <!--&lt;!&ndash;<npa-scorecard iconName="alarm" fxFlex="25">&ndash;&gt;-->
    <!--&lt;!&ndash;<label title>اوسط معیاد بازدید</label>&ndash;&gt;-->
    <!--&lt;!&ndash;<label value>60</label>&ndash;&gt;-->
    <!--&lt;!&ndash;<label text>ثانیه</label>&ndash;&gt;-->
    <!--&lt;!&ndash;<label footer>در طول سال</label>&ndash;&gt;-->
    <!--&lt;!&ndash;<label back>این بخش مدت زمانی (ثانیه) بازدید از سیستم را نشان می دهد. یعنی یک کاربر در 30 روز بطور اوسط، چند ثانیه خویش را در سیستم سپری نموده است.</label>&ndash;&gt;-->
    <!--&lt;!&ndash;</npa-scorecard>&ndash;&gt;-->

    <!--&lt;!&ndash;<npa-scorecard iconName="home" fxFlex="25">&ndash;&gt;-->
    <!--&lt;!&ndash;<label title>بیشترین بازدید</label>&ndash;&gt;-->
    <!--&lt;!&ndash;<label value>33</label>&ndash;&gt;-->
    <!--&lt;!&ndash;<label text>صفحه دشبوردها</label>&ndash;&gt;-->
    <!--&lt;!&ndash;<label footer>در طول سال</label>&ndash;&gt;-->
    <!--&lt;!&ndash;<label back>بعد از وارد شدن در سیستم، کدام یکی از صفحات سیستم بیشترین باز دید را در 30 روز داشته است.</label>&ndash;&gt;-->
    <!--&lt;!&ndash;</npa-scorecard>&ndash;&gt;-->

    <!--&lt;!&ndash;<npa-scorecard iconName="sync_problem" fxFlex="25">&ndash;&gt;-->
    <!--&lt;!&ndash;<label title>کمترین بازدید</label>&ndash;&gt;-->
    <!--&lt;!&ndash;<label value>80</label>&ndash;&gt;-->
    <!--&lt;!&ndash;<label text>صفحه چالش و ملاحظات</label>&ndash;&gt;-->
    <!--&lt;!&ndash;<label footer>در طول سال</label>&ndash;&gt;-->
    <!--&lt;!&ndash;<label back> بعد از وارد شدن در سیستم، کدام یکی از صفحات سیستم کمترین باز دید را در 30 روز داشته است.</label>&ndash;&gt;-->
    <!--&lt;!&ndash;</npa-scorecard>&ndash;&gt;-->
    <!--&lt;!&ndash;</div>&ndash;&gt;-->
<!--</div>-->

<router-outlet></router-outlet>
