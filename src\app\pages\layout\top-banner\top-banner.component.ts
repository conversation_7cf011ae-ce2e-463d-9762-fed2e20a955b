import {Component, Input, OnInit} from '@angular/core';
import {NavigationEnd, Router} from '@angular/router';
import {MatDialog} from '@angular/material';
import {AuthService} from '../../../services/auth.service';
import {BootService} from '../../../services/boot.service';
import {Location} from '@angular/common';
import {ComplaintService} from '../../../services/complaint.service';
import {ComplaintGeneralInformationDialogComponent} from './complaint-general-information-dialog/complaint-general-information-dialog.component';
import {NPA_COMMON_DIALOG_WIDTH} from '../../shared/consts/sizes';
import {TranslateService} from '../../shared/services/translate.service';
import {ProcurementTypeContextService} from '../../../services/procurement-type-context.service';

interface IRoute {
    key: string;
    url: string;
    name: string;
}

@Component({
    selector: 'npa-banner',
    templateUrl: './top-banner.component.html',
    styleUrls: ['./top-banner.component.styl']
})

export class TopBannerComponent implements OnInit {
    @Input() isSidebarMainClose;
    @Input() isSidebarUtilitiesClose;
    @Input() scrollTop;
    routeUrl: IRoute[];
    complaintIdentificationNumber: string;
    role: string;
    lang: string;
    arrowIcon: string;

    constructor(private _router: Router,
                private _authService: AuthService,
                private _bootService: BootService,
                private _location: Location,
                private _complaintService: ComplaintService,
                private translate: TranslateService,
                public contextService: ProcurementTypeContextService,
                private _dialog: MatDialog) {
        this.role = _authService.user.value.role.slug;
        this.lang = this.translate.lang;
    }


    ngOnInit() {
        this._complaintService.complaintInformation.subscribe(complaint => {
            let complaintIdentificationNumber = complaint.user_identification_number;
            while (this.complaintIdentificationNumber && this.complaintIdentificationNumber.indexOf('%') >= 0) {
                complaintIdentificationNumber = this.decode(complaintIdentificationNumber);
            }
            this.complaintIdentificationNumber = complaintIdentificationNumber;
            this.routeUrl = [];
            this.buildBreadcrumb(this._location.path());
        });
        this._router.events.subscribe((data) => {
            if (!(data instanceof NavigationEnd)) {
                return;
            }
            const urlArray = data.url.split('/');
            const activated_route = urlArray[urlArray.length - 1];
            if (activated_route === 'user-list') {
                this.complaintIdentificationNumber = null;
            }
            this.routeUrl = [];
            const urlString = data['urlAfterRedirects'];
            if (urlString !== undefined) {
                this.buildBreadcrumb(urlString);
            }
        });
        this.arrowIcon = this.lang === 'prs' || 'ps' ? 'keyboard_arrow_left' : 'keyboard_arrow_right';

    }

    private buildBreadcrumb(urlString: string) {
        let urlsArray = [];
        urlsArray = urlString.split('/').slice(3);
        urlsArray.forEach((item: string) => {
            const path = urlsArray.slice(0, urlsArray.indexOf(item) + 1);
            for (let i = 0; i < path.length; i++) {
                path[i] = this.decode(path[i]);
                path[i] = encodeURIComponent(encodeURIComponent(path[i]));
            }
            const stringPath = path.join('/');
            this.routeUrl.push({
                key: isNaN(Number(item)) ? this.translate.normalize(decodeURIComponent(decodeURIComponent(item))) : item,
                url: stringPath,
                name: item
            });

        });
    }

    decode(urlComponent: string) {
        while (urlComponent && urlComponent.indexOf('%') >= 0) {
            urlComponent = decodeURIComponent(urlComponent);
        }
        return urlComponent;
    }

    openProjectGeneralInfoDialog() {
        this._dialog.open(ComplaintGeneralInformationDialogComponent, {
            width: NPA_COMMON_DIALOG_WIDTH,
            data: {},
            disableClose: true
        });
    }
}
