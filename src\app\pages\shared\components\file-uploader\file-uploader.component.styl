@import "../../../../../styles/definitions.styl"
  .npa-label
    display flex
    justify-content space-between
    color rgba(0,0,0,.54)
    margin-top 14px
    margin-bottom 8px
    mat-icon:hover
        cursor pointer
  .other
    position relative
  .add
    position absolute
    transition .5s ease
    right 70%
  .close
  position absolute
      transition .5s ease
      right 10%
 .buttons
    height 50px
  .npa-controls
    [mat-icon-button]
      margin-left 4px
    mat-chip
      position relative
      width calc(100% - 30xp)
      .npa-file-name
        font-family Roboto, Helvetica Neue Light, Helvetica Neue, Helvetica, Arial, Lucida Grande, sans-serif
        font-size 12px
        overflow hidden
        white-space nowrap
        text-overflow ellipsis
      mat-icon
        position absolute
        display flex
        align-items center
        justify-content center
        left 3px
        top -8px
        border-radius 50%
        background colorWarn
        color #fff
        font-size 14px !important
        width 20px
        height 20px
        &:hover
          cursor pointer
  mat-chip
    position relative
    padding-left 25px !important
    display flex
    align-items center
    font-family Roboto, Helvetica Neue Light, Helvetica Neue, Helvetica, Arial, Lucida Grande, sans-serif
    font-size 12px
