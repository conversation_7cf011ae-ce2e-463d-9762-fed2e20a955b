import {Injectable} from '@angular/core';
import {HttpService} from '../../../../services/http.service';
import {Observable} from 'rxjs';
import {IVerificationDatabase} from './verification.types';

@Injectable()
export class VerificationService {

    constructor(private _httpService: HttpService) {
    }

    show(url: string, id: number): Observable<IVerificationDatabase> {
        return this._httpService.get(`${url}/${id}`);
    }

    confirm(url: string, id: number): Observable<any> {
        return this._httpService.post(`${url}/${id}/confirm`, {});
    }

    unconfirm(url: string, id: number): Observable<any> {
        return this._httpService.post(`${url}/${id}/unconfirm`, {});
    }

    approve(url: string, id: number): Observable<any> {
        return this._httpService.post(`${url}/${id}/approve`, {});
    }

    unapprove(url: string, id: number): Observable<any> {
        return this._httpService.post(`${url}/${id}/unapprove`, {});
    }
}
