import {Component, OnInit} from '@angular/core';
import {RouteService} from './services/route.service';
import {TranslateService} from './pages/shared/services/translate.service';

@Component({
    selector: 'npa-root',
    templateUrl: './app.component.html',
    styleUrls: ['./app.component.styl']
})
export class AppComponent implements OnInit {
    constructor(private _routeService: RouteService, public translate: TranslateService) {
        this._routeService.setProperRoute();
    }

    ngOnInit(): void {
    }


}
