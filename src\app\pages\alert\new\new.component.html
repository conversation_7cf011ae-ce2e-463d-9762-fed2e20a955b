<mat-card>
    <mat-table [dataSource]="dataSource">
        <mat-header-row *matHeaderRowDef="displayedColumns" fxLayout="row wrap" fxLayoutAlign="start"></mat-header-row>
        <mat-row *matRowDef="let row; columns: displayedColumns;"></mat-row>

        <ng-container matColumnDef="id">
                <mat-header-cell *matHeaderCellDef fxFlex="5"> {{'PROPRIETARY_ID'|translate}}</mat-header-cell>
            <mat-cell *matCellDef="let row" fxFlex="5"> {{row.instance_id}}</mat-cell>
        </ng-container>

        <ng-container matColumnDef="status">
            <mat-header-cell *matHeaderCellDef fxFlex="15">{{'STATUS'|translate}}</mat-header-cell>
            <mat-cell *matCellDef="let row" fxFlex="15" fxLayout="row" fxLayoutAlign="start start">
                <span class="status status-{{row.status_slug}}">
                    <mat-icon aria-hidden="true">lens</mat-icon>
                </span>
                <span>{{row.status_name_da}}</span>
            </mat-cell>
        </ng-container>

        <ng-container matColumnDef="created_at">
            <mat-header-cell *matHeaderCellDef fxFlex="20">{{'CREATION_TIME'|translate}}</mat-header-cell>
            <mat-cell *matCellDef="let row" fxFlex="20">
                <span class="npa-number">
                    {{row.created_at | jalaliDateTime}}
                </span>
            </mat-cell>
        </ng-container>

        <ng-container matColumnDef="contents">
            <mat-header-cell *matHeaderCellDef fxFlex="45">{{'TEXT'|translate}}</mat-header-cell>
            <mat-cell *matCellDef="let row" fxFlex="45"> {{row.contents}}</mat-cell>
        </ng-container>
        <ng-container matColumnDef="options">
            <mat-header-cell *matHeaderCellDef fxFlex="10">{{'OPERATIONS'|translate}}</mat-header-cell>
            <mat-cell *matCellDef="let row" fxFlex="10">
                <div fxFlex="100">
                    <button type='button'
                            mat-icon-button (click)="openViewDialog(row)"
                            matTooltip="نمایش">
                        <mat-icon>visibility</mat-icon>
                    </button>
                    <!--<button type='button'-->
                    <!--(click)="openArchivedConfirmation('آیا مطمئن هستید هشدار '+row.id+' آرشیف گردد؟')"-->
                    <!--mat-icon-button-->
                    <!--matTooltip="آرشیف">-->
                    <!--<mat-icon>archive</mat-icon>-->
                    <!--</button>-->
                    <!--<button type='button' (click)="openReminderDialog()"-->
                    <!--mat-icon-button matTooltip="یاد آوری">-->
                    <!--<mat-icon>add_alert</mat-icon>-->
                    <!--</button>-->
                </div>
            </mat-cell>
        </ng-container>
    </mat-table>
</mat-card>
