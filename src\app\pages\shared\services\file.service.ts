import {ElementRef, Injectable} from '@angular/core';

@Injectable()
export class FileService {

    constructor() {

    }

    download(container: ElementRef, blob: Blob, fileName: string) {
        const url = window.URL.createObjectURL(blob);
        const a = window.document.createElement('a');
        a.setAttribute('href', url);
        a.setAttribute('download', fileName);
        container.nativeElement.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        a.remove();
    }


    dataURItoBlob(dataURI: string): Blob {
        const byteString = atob(dataURI.split(',')[1]);

        const mimeString = dataURI.split(',')[0].split(':')[1].split(';')[0];

        const ab = new ArrayBuffer(byteString.length);

        const ia = new Uint8Array(ab);

        for (let i = 0; i < byteString.length; i++) {
            ia[i] = byteString.charCodeAt(i);
        }

        return new Blob([ab], {type: mimeString});
    }

}
