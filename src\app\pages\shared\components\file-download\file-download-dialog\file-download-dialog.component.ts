import {Component, ElementRef, Inject, Input, OnInit, ViewChild} from '@angular/core';
import {MAT_DIALOG_DATA, MatDialogRef} from '@angular/material';
import {HttpService} from '../../../../../services/http.service';
import {NpaDatabase, NpaDataSource} from '../../../classes/npa-table';
import {NPA_ALERTS} from '../../../consts/messages';
import {HttpErrorResponse} from '@angular/common/http';
import {NotificationsService} from '../../../services/notifications.service';
import {IConfirmationDialogOutput} from '../../../types/confirmation.type';
import {ConfirmationService} from '../../../services/confirmation.service';
import {FileService} from '../../../services/file.service';
import { environment } from '../../../../../../environments/environment';
@Component({
    templateUrl: './file-download-dialog.component.html',
    styleUrls: ['./file-download-dialog.component.styl']
})
export class FileDownloadDialogComponent implements OnInit {
    @ViewChild('container') nativeContainer: ElementRef;
    dataSource: NpaDataSource;
    database: NpaDatabase;
    @Input() isLog = false;
    columns = ['number', 'original_name', 'created_at', 'option'];

    constructor(public dialogRef: MatDialogRef<FileDownloadDialogComponent>,
                @Inject(MAT_DIALOG_DATA) public data: any, private _http: HttpService,
                public notificationsService: NotificationsService,
                private _fileService: FileService,
                private confirmationService: ConfirmationService) {
    }

    ngOnInit() {
        let index = 1;
        this.data.files.forEach(element => {
            element['number'] = index++;
        });
        this.database = new NpaDatabase(this.data.files);
        this.dataSource = new NpaDataSource(this.database);
    }

    downloadFile(file): void {
        this.notificationsService.info(NPA_ALERTS.FILE_DOWNLOAD);
        if (environment.isAwsS3Used) {
            this._http.get((this.isLog ? 'api/log/attachment/' : 'api/attachment/') + file.id).subscribe(res => {
                window.open(res);
                this.notificationsService.dismissLoading();
                return;
            }, (error: HttpErrorResponse) => {
                this.notificationsService.error(NPA_ALERTS.ERROR);
                console.error(error);
            });
        } else {
            this._http.download((this.isLog ? 'api/log/attachment/' : 'api/attachment/') + file.id).subscribe(res => {
                this._fileService.download(this.nativeContainer, res, file.original_name);
                this.notificationsService.dismissLoading();
            }, (error: HttpErrorResponse) => {
                this.notificationsService.error(NPA_ALERTS.ERROR);
                console.error(error);
            });
        }
    }

    deleteFile(id) {
        this
            .confirmationService
            .openDialog({
                message: NPA_ALERTS.DELETE_CONFIRM,
            })
            .then((result: IConfirmationDialogOutput) => {
                if (result.status) {
                    this.notificationsService.startLoading();
                    this._http.delete('api/attachment/' + id)
                        .subscribe(
                            () => {
                                this.database.delete(id);
                                this.notificationsService.dismissLoading();
                                this.dialogRef.close(id);
                            },
                            (error: HttpErrorResponse) => {
                                this.notificationsService.dismissLoading();
                                this.notificationsService.error(NPA_ALERTS.ERROR);
                                console.error(error);
                            }
                        );
                }
            });
    }

}
