import {Component, OnInit} from '@angular/core';
import {ProcurementTypeContextService} from '../../services/procurement-type-context.service';
import {AuthService} from '../../services/auth.service';
import {TranslateService} from '../shared/services/translate.service';
import {NotificationsService} from '../shared/services/notifications.service';

@Component({
    selector: 'npa-reports',
    templateUrl: './reports.component.html',
    styleUrls: ['./reports.component.styl']
})
export class ReportsComponent implements OnInit {

    lang;

    constructor(private notificationsService: NotificationsService,
                public authService: AuthService,
                public contextService: ProcurementTypeContextService,
                private translate: TranslateService) {
        this.lang = this.translate.lang;
    }


    ngOnInit() {
        this.notificationsService.dismissLoading();
    }


}
