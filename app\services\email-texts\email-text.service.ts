const HEADER = `
<!doctype html>
<html>
<head>
    <meta charset="utf-8">
    <title>NPA | USM</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body>`;
const FOOTER = `
<footer>
  <i style="font-size: 12px">If any assistance is required, please do not hesitate to contact us using the following email address: <EMAIL></i>
</footer>
</body>
</html>`;

export class EmailTextService {

    public static getRecoverPasswordEmail(user, link) {
        return HEADER +
            `<div style="direction: rtl">
                    <b>سیستم تدارکات الکترونیکی و باز افغانستان (AGEOPS)</b>
                    <br><br>
                    <b>نام کاربری :</b> ${user.username}
                    <p>کاربرگرامی! جهت بازیابی رمزعبور حساب کاربری خویش از <a href="${link}"> لینک بازیابی
                    رمز عبور </a> استفاده نمائید و یا هم بالای لینک انتهای ایمیل این کلیک نمائید. </p>
                    <p style="color:#990000">در صورتیکه شما در خواست بازیابی رمز عبور نکرده اید، لطف نموده این ایمیل را نادیده بگیرید.</p>
                </div>
                <hr>
                <div style="direction: rtl">
                    <b>د افغانستان د الکترونیکي او پرانستو تدارکاتو سیستم (AGEOPS)</b>
                    <br><br>
                    <b>د کاربر نوم :</b> ${user.username}
                    <p>د سیستم محترم کارکوونکی! د خپل پټ نوم د بیا لاسته راوړنې په موخه تاسو څخه غوښتنه کیږي چې
                    <a  href="${link}">
                    د پټ نوم د بیاځل لاسته راوړنې لینک څخه </a> ګته واخلی او یا هم دی برښنالیک په
                    لاندیني لینک باندې کلیک وکړی.
                    </p>
                    <p style="color:#990000">که تاسې د پټنوم د بیارغولو غوښتنه نه وي کړې، په درنښت سره دغه بریښنالیک ته پام مه کوی.</p>
                </div>
                <hr>
                <div style="direction: ltr">
                    <b>Afghanistan Government Electronic and Open Procurement System (AGEOPS) </b>
                    <br><br>
                    <b>Username:</b> ${user.username}
                    <p>In order to recover your account password, click <a href="${link}"> here </a> or use the following link:</p>
                    <p style="color:#990000">Note: If you have not requested this password reset, you can ignore this email.</p>
                    <br>
                    <b>Link: </b><span>${link}</span>
                </div>
                <br><br>
        `
            + FOOTER;
    }

    public static getVendorUserCredentialEmail(username) {
        const baseURL = process.env.CUSTOM_FRONT_END_BASE_URL;
        const recoverPasswordPrsLink = baseURL + 'prs/auth/recover-password';
        const recoverPasswordPsLink = baseURL + 'ps/auth/recover-password';
        const recoverPasswordEnLink = baseURL + 'en/auth/recover-password';
        return HEADER +
            `<div style="direction: rtl">
                    <b>سیستم تدارکات الکترونیکی و باز افغانستان (AGEOPS)</b>
                    <br><br>
                    <p>کاربرگرامی!</p>
                    <p>حساب کاربری شما موفقانه ایجاد گردید. </p>
                    <p><b>نام کاربری :</b> ${username}</p>
                     <p> جهت ایجاد رمز عبور خویش از <a href="${recoverPasswordPrsLink}"> لینک بازیابی
                    رمز عبور </a> استفاده نمائید.</p>
                </div>
                <hr>
                <div style="direction: rtl">
                    <b>د افغانستان د الکترونیکي او پرانستو تدارکاتو سیستم (AGEOPS)</b>
                    <br><br>
                    <p>د سیستم محترم کاروونکی!</p>
                    <p>ستاسو حساب په بریالیتوب سره رامینځ ته شوو. </p>
                    <p><b>کارن نوم :</b> ${username}</p>
                    <p> د خپل پټنوم د جوړولو لپاره ، تاسو څخه غوښتنه کیږي چې
					 <a  href="${recoverPasswordPsLink}">
                    د پټ نوم د بیاځل لاسته راوړنې لینک</a> څخه ګته واخلی.
					</p>
                </div>
                <hr>
                <div style="direction: ltr">
                    <b>Afghanistan Government Electronic and Open Procurement System (AGEOPS) </b>
                    <br><br>
                    <p>Dear User!</p>
                    <p>Your account has been successfully created. </p>
                    <p><b>username :</b> ${username}</p>
                    <p>In order to set your password, please click
					<a href="${recoverPasswordEnLink}">here</a>
					</p>
                </div>
        `
            + FOOTER;
    }

    public static getAcpmsUserCredentialEmail(username) {
        const baseURL = process.env.CUSTOM_FRONT_END_BASE_URL;
        const recoverPasswordPrsLink = baseURL + 'prs/auth/recover-password';
        const recoverPasswordPsLink = baseURL + 'ps/auth/recover-password';
        const recoverPasswordEnLink = baseURL + 'en/auth/recover-password';
        return HEADER +
            `<div style="direction: rtl">
                    <b>سیستم تدارکات الکترونیکی و باز افغانستان (AGEOPS)</b>
                    <br><br>
                    <p>کاربرگرامی!</p>
                    <p>حساب کاربری شما موفقانه ایجاد گردید. </p>
                    <p><b>نام کاربری :</b> ${username}</p>
                     <p> جهت ایجاد رمز عبور خویش از <a href="${recoverPasswordPrsLink}"> لینک بازیابی
                    رمز عبور </a> استفاده نمائید.</p>
                </div>
                <hr>
                <div style="direction: rtl">
                    <b>د افغانستان د الکترونیکي او پرانستو تدارکاتو سیستم (AGEOPS)</b>
                    <br><br>
                    <p>د سیستم محترم کاروونکی!</p>
                    <p>ستاسو حساب په بریالیتوب سره رامینځ ته شوو. </p>
                    <p><b>کارن نوم :</b> ${username}</p>
                    <p> د خپل پټنوم د جوړولو لپاره ، تاسو څخه غوښتنه کیږي چې
					 <a  href="${recoverPasswordPsLink}">
                    د پټ نوم د بیاځل لاسته راوړنې لینک</a> څخه ګته واخلی.
					</p>
                </div>
                <hr>
                <div style="direction: ltr">
                    <b>Afghanistan Government Electronic and Open Procurement System (AGEOPS) </b>
                    <br><br>
                    <p>Dear User!</p>
                    <p>Your account has been successfully created. </p>
                    <p><b>username :</b> ${username}</p>
                    <p>In order to set your password, please click
					<a href="${recoverPasswordEnLink}">here</a>
					</p>
                </div>
        `
            + FOOTER;
    }

    public static getAppmsUserCredentialEmail(username) {
        // todo: To be merged with `getAcpmsUserCredentialEmail` method because they both do the same thing.
        const baseURL = process.env.CUSTOM_FRONT_END_BASE_URL;
        const recoverPasswordPrsLink = baseURL + 'prs/auth/recover-password';
        const recoverPasswordPsLink = baseURL + 'ps/auth/recover-password';
        const recoverPasswordEnLink = baseURL + 'en/auth/recover-password';
        return HEADER +
            `<div style="direction: rtl">
                    <b>سیستم تدارکات الکترونیکی و باز افغانستان (AGEOPS)</b>
                    <br><br>
                    <p>کاربرگرامی!</p>
                    <p>حساب کاربری شما موفقانه ایجاد گردید. </p>
                    <p><b>نام کاربری :</b> ${username}</p>
                     <p> جهت ایجاد رمز عبور خویش از <a href="${recoverPasswordPrsLink}"> لینک بازیابی
                    رمز عبور </a> استفاده نمائید.</p>
                </div>
                <hr>
                <div style="direction: rtl">
                    <b>د افغانستان د الکترونیکي او پرانستو تدارکاتو سیستم (AGEOPS)</b>
                    <br><br>
                    <p>د سیستم محترم کاروونکی!</p>
                    <p>ستاسو حساب په بریالیتوب سره رامینځ ته شوو. </p>
                    <p><b>کارن نوم :</b> ${username}</p>
                    <p> د خپل پټنوم د جوړولو لپاره ، تاسو څخه غوښتنه کیږي چې
					 <a  href="${recoverPasswordPsLink}">
                    د پټ نوم د بیاځل لاسته راوړنې لینک</a> څخه ګته واخلی.
					</p>
                </div>
                <hr>
                <div style="direction: ltr">
                    <b>Afghanistan Government Electronic and Open Procurement System (AGEOPS) </b>
                    <br><br>
                    <p>Dear User!</p>
                    <p>Your account has been successfully created. </p>
                    <p><b>username :</b> ${username}</p>
                    <p>In order to set your password, please click
					<a href="${recoverPasswordEnLink}">here</a>
					</p>
                </div>
        `
            + FOOTER;
    }

    public static appmsRoleGrantingCredentialEmail(username) {
        const baseURL = process.env.CUSTOM_FRONT_END_BASE_URL;
        return HEADER +
            `<div style="direction: rtl">
                    <b>سیستم تدارکات الکترونیکی و باز افغانستان (AGEOPS)</b>
                    <br><br>
                    <p>کاربرگرامی!</p>
                    <p>حساب کاربری شما موفقانه ایجاد گردید. </p>
                    <p><b>نام کاربری :</b> ${username}</p>
                     <a href="${baseURL}"></a>
                </div>
                <hr>
                <div style="direction: rtl">
                    <b>د افغانستان د الکترونیکي او پرانستو تدارکاتو سیستم (AGEOPS)</b>
                    <br><br>
                    <p>د سیستم محترم کاروونکی!</p>
                    <p>ستاسو حساب په بریالیتوب سره جوړ شو. </p>
                    <p><b>کارن نوم :</b> ${username}</p>
                     <a href="${baseURL}"></a>
                </div>
                <hr>
                <div style="direction: ltr">
                    <b>Afghanistan Government Electronic and Open Procurement System (AGEOPS) </b>
                    <br><br>
                    <p>Dear User!</p>
                    <p>Your account has been successfully created. </p>
                    <p><b>username :</b> ${username}</p>
                    <a href="${baseURL}"></a>
                </div>
        `
            + FOOTER;
    }


}
