import {IUser, User} from '../entity/user';
import {HashService} from './hash.service';
import {Model} from '../config/database';
import {IUserRole, UserRole} from '../entity/user-role';

const {check} = require('express-validator/check');

export class ValidatorService {


}

export const Validator = {
    user: {
        onRegister: [
            check('name').exists().withMessage(required('NAME')),
            check('last_name').exists().withMessage(required('LAST_NAME')),
            check('username').exists().withMessage(required('USERNAME'))
                .isLength({min: 3}).withMessage(invalid('USERNAME')),
            check('email')
                .exists().withMessage(required('EMAIL'))
                .isEmail().withMessage(invalid('EMAIL'))
            ,
            check('status_id').exists().withMessage(required('STATUS_ID'))
                .isDecimal().withMessage(invalid('STATUS_ID')),
            check('password')
                .exists().withMessage(required('PASSWORD'))
                .isLength({min: 8, max: 20}).withMessage(invalid('PASSWORD')),
        ],
        onSystemUserRegister: [
            check('name').exists().withMessage(required('NAME')),
            check('last_name').exists().withMessage(required('LAST_NAME')),
            check('username').exists().withMessage(required('USERNAME'))
                .isLength({min: 3}).withMessage(invalid('USERNAME')),
            check('email')
                .exists().withMessage(required('EMAIL'))
                .isEmail().withMessage(invalid('EMAIL'))
        ],
        onPasswordReset: [
            check('password')
                .exists().withMessage(required('PASSWORD'))
                .custom(async (password, {req}) => {
                    const userRole = <IUserRole>await Model(UserRole).findOne(req.query.user_role_id);
                    const user = <IUser>await Model(User).findOne(userRole.user_id);
                    return await HashService.verify(password, user.password);
                }).withMessage('Wrong password'),
            check('new_password')
                .isLength({min: 8, max: 20}).withMessage(invalid('NEW_PASSWORD'))
                .exists().withMessage(required('NEW_PASSWORD')),
            check('repeat_password')
                .exists().withMessage(required('REPEAT_PASSWORD'))
                .isLength({min: 8, max: 20}).withMessage(invalid('REPEAT_PASSWORD'))
                .custom((repeat_password, {req}) => {
                    return repeat_password === req.body.new_password;
                }).withMessage('New passwords do not match'),


        ],
        isEmailExist: [
            check('email')
                .exists().withMessage(required('EMAIL'))
                .isEmail().withMessage(invalid('EMAIL'))
                .custom(async (email) => {
                    const user = <IUser>await Model(User).findOne({email: email});
                    return user.email === email;
                }).withMessage('email does not exist'),

        ],
        verifyPassword: [
            check('new_password')
                .exists().withMessage(required('NEW_PASSWORD'))
                .isLength({min: 8}).withMessage(invalid('NEW_PASSWORD')),
            check('new_password_confirmation')
                .exists().withMessage(required('NEW_PASSWORD_CONFIRMATION'))
                .isLength({min: 8}).withMessage(invalid('NEW_PASSWORD_CONFIRMATION'))
        ]
    },
    auth: {
        onLogin: [
            check('username')
                .exists().withMessage(required('username'))
            , check('password')
                .exists().withMessage(required('password'))
                .isLength({min: 8, max: 20}).withMessage(invalid('PASSWORD'))
                .custom((password, {req}) => {
                    return Model(User).findOne({where: {username: req.body.username, status_id: 1}})
                        .then((user: IUser) => HashService.verify(password, user.password));
                }).withMessage(invalid('USERNAME_OR_PASSWORD'))
        ]
    },
};


function required(fieldName) {
    return `${fieldName}_IS_REQUIRED`;
}

function invalid(fieldName) {
    return `INVALID_${fieldName}`;
}
