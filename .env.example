APP_NAME=usm
APP_ENV=local
TZ=Asia/Kabul
PORT=3000
HOST=127.0.0.1

JWT_ACCESS_EXPIRES_IN='1000000'
JWT_REFRESH_EXPIRES_IN='190000000'
JWT_ALGORITHM='RS256'
JWT_SECRET_ALGORITHM='HS256'
JWT_SINGEL_USE_TOKEN_SECRET=')(*&y78FFyyt$%&&&JLKJ342'
JWT_SINGEL_USE_TOKEN_EXPIRES_IN='1200000'
EXPIRED_TOKEN_REFRESH_TIME_LIMIT=60000
JWT_RECOVER_PASSWORD_EXPIRES_IN=200000

B_CRYPT_SALT_ROUNDS=12

TYPEORM_CONNECTION=mysql
TYPEORM_HOST=localhost
TYPEORM_USERNAME=root
TYPEORM_PASSWORD=
TYPEORM_DATABASE=usm
TYPEORM_PORT=3306

TYPEORM_SYNCHRONIZE=true
TYPEORM_MIGRATIONS_RUN=false
TYPEORM_LOGGING=true
TYPEORM_ENTITIES=app/entity/**/*.ts
TYPEORM_MIGRATIONS=app/migration/**/*.ts
TYPEORM_SUBSCRIBERS=app/subscriber/**/*.ts

TYPEORM_ENTITIES_DIR=app/entity
TYPEORM_MIGRATIONS_DIR=app/migration
TYPEORM_SUBSCRIBERS_DIR=app/subscriber

MAIL_DRIVER=smtp
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=mailer.pmis.npa!@#
MAIL_ENCRYPTION=tls
MAIL_FROM_NAME="AGEOPS-USM mailing system"
MAIL_PROVIDER="AWS"

CUSTOM_APPMS_BASE_URL= http://localhost:4500/
CUSTOM_ACPMS_BASE_URL= http://localhost:4300/
CUSTOM_AVRCS_BASE_URL= http://localhost:4400/
CUSTOM_CDM_BASE_URL= http://localhost:4600/
CUSTOM_BACK_END_BASE_URL= http://localhost:4200/
CUSTOM_FRONT_END_BASE_URL= http://127.0.0.1:4200/
RECOVER_PASSWORD_FORM_ROUTE = auth/recover-password-form
CUSTOM_API_PASSWORD=AABBCC!!@@##112233

CUSTOM_CDM_USER_PASSWORD="$2a$10$EVO/rftpS8gr5YRZiv/c7edtz7QWB0q0/rd1pgm1ERc8VZ7hhTOom"
CUSTOM_CDM_USER_EMAIL=""

AWS_KEY=********************
AWS_SECRET=FqwroGBF/trZdEFGeNN22o+9//7K+NBOQSdfeChc
AWS_REGION=ap-northeast-2
