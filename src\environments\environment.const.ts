import {IRole, IUser} from '../app/services/auth.types';

export const MAP = {
    prs: {
        required: ':name الزامی است.',
        minLength5: 'حداقل دارای پنچ حرف باید باشد.',
        emailPattern: 'لطفا ایمل درست وارد نمایید.',
        onlyText: 'لطفا فقط حروف وارد نمایید.',
        phoneNumberAfg: 'لطفا شماره ی تماس را به شکل 93xxxxxxxxx وارد نمایید.',
        generalPhoneNumber: 'لطفا شماره ی تماس داخل افغانستان را به شکل 937xxxxxxxx و یا 932xxxxxxxx وارد نمایید.',
        phoneNumberInternational: 'Please provide the international phone number, e.g. 14155552671 (US phone number).',
        integer: 'لطفا اعداد بدون اعشاریه وارد نمایید.',
        username: 'نام کاربری باید ترکیبی از حروف کوچک انگلیسی و اعداد باشد.'
    },
    ps: {
        required: ':name اړین دی.',
        minLength5: 'لږترلږه باید پنځه توري‎ ولري.',
        emailPattern: 'مهربانۍ وکړی صحیح ایمیل ولیکی.',
        onlyText: 'مهربانۍ وکړی فقد توری وکاروي.',
        phoneNumberAfg: 'مهربانۍ وکړی د اړیکی شمیره په دغه څیر 93xxxxxxxxx وکاروي.',
        generalPhoneNumber: 'مهربانۍ وکړی د افغانستان  اړیکی شمیره په دغه څیر 937xxxxxxxx او یا دغه څیر 932xxxxxxxx  وکاروي.',
        phoneNumberInternational: 'Please provide the international phone number, e.g. 14155552671 (US phone number).',
        integer: 'مهربانۍ وکړی شمېرې پرته له عشاریه ولیکی.',
        username: 'د کارن نوم باید د انګلیسی کوچني حروفو او شمیرو  له ترکیب څخه جوړ شی.'
    },
    en: {
        required: ':name is required.',
        minLength5: 'This field must contain at least 5 characters.',
        emailPattern: 'Please enter a valid email address.',
        onlyText: 'Please enter alphabetical characters only.',
        phoneNumberAfg: 'Please enter a phone number in the 93xxxxxxxxx format.',
        generalPhoneNumber: 'Please enter Afghanistan local phone numbers in the formats 937xxxxxxxx or 932xxxxxxxx.',
        phoneNumberInternational: 'Please provide the international phone number, e.g. 14155552671 (US phone number).',
        integer: 'Please enter a whole number.',
        username: 'Username has to be the combination of lowercase English characters and numbers.',
    }

};

const ROLES: IRole[] = [
    {
        id: 1,
        slug: 'dev-ops',
        name: 'dev-ops',
        can_be_acted: true,
        can_act: false,
        contexts: [],
    },
    {
        id: 2,
        slug: 'user',
        name: 'user',
        can_be_acted: true,
        can_act: false,
        contexts: [],
    }
];

export const CONTEXTS_LIST = [
    {
        roleId: 1,
        slug: 'my-role-slug',
        accessibleContexts: [
            {name: 'dashboard'},
            {name: 'users'},
            {name: 'charts'},
            {name: 'user-list'},
            {name: 'sessions'},
            {name: 'sessions-list'},
            {name: 'systems'},
            {name: 'systems-list'},
            {name: 'contexts'},
            {name: 'contexts-list'},
            {name: 'roles'},
            {name: 'roles-list'},
            {name: 'sample-main-menu'},
            {name: 'sample-sub-menu'},
            {name: 'user-guide'},
            {name: 'system-information'},
            {name: 'change-log'},
            {name: 'contact-information'},
            {name: 'user-manual'},
            {name: 'reset-system'},
            {name: 'reports'},
            {name: 'generate-and-download-reports'}
        ]
    },
    {
        roleId: 2,
        slug: 'my-role-slug',
        accessibleContexts: [
            {name: 'users'},
            {name: 'sessions'},
            {name: 'sessions-list'},
            {name: 'dashboard-systems-list'},
            {name: 'sample-main-menu'},
            {name: 'sample-sub-menu'},
            {name: 'user-guide'},
            {name: 'system-information'},
            {name: 'change-log'},
            {name: 'contact-information'},
            {name: 'user-manual'},
            {name: 'reset-system'},
            {name: 'reports'},
            {name: 'generate-and-download-reports'}
        ]
    }];

const USER: IUser = {
    id: 8,
    full_name: 'کاربر امتحانی',
    username: 'superuser',
    email: '<EMAIL>',
    role: ROLES[0],
};
export const LOG_IN_DATA = {
    user: USER,
    roles: ROLES
};
