import {MigrationInterface, QueryRunner} from 'typeorm';

export class AddCDMUserRoles1566468106589 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<any> {
        await queryRunner.query(`
            insert into
            user_roles (user_id, role_id)
            values (
                (select id from users where username = "common-data-manager"),
                (select id from roles where slug = "common-data-manager")
                ),
                (
                (select id from users where username = "common-data-manager"),
                (select id from roles where slug = "user")
                )
            `);
    }

    public async down(queryRunner: QueryRunner): Promise<any> {
        await queryRunner.query(`Delete from user_roles where role_id = 
(select id from roles where slug = "common-data-manager")`);
    }

}
