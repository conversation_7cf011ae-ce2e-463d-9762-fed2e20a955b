import {MigrationInterface, QueryRunner} from 'typeorm';

export class AddACPMSContextAccesses1562821279347 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<any> {
        await queryRunner.query(`
            insert into
                context_accesses (is_enabled,role_id,context_id,operation_id)
            values
            (1,19,113,1),
            (1,19,114,1)
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<any> {
    }

}
