<div fxLayout="row" fxLayoutAlign="space-between center">
  <h4 fxFlex="50">{{ (isEditing ? 'EDIT_USER' : 'ADD_USER')|translate }}</h4>
  <div fxFlex="100" fxLayoutAlign="end">
      <button mat-dialog-close mat-icon-button matTooltip="{{'CLOSE' | translate}}">
          <mat-icon>close</mat-icon>
      </button>
  </div>
</div>
<form *ngIf="form" [formGroup]="form" (ngSubmit)="isValid() && submit(form.value)">
    <mat-dialog-content class="dialog" fxLayout="row wrap" fxLayoutAlign="start">
        <div fxLayout="row wrap" fxLayoutAlign="start" fxFlex="100">
            <ng-container>
              <mat-form-field fxFlex="33">
                <input matInput placeholder='{{"NAME" | translate}}'
                       formControlName="name">
                <mat-error *ngIf="form?.get('name')?.errors?.required">
                    {{formValidationService.required.message()}}
                </mat-error>
              </mat-form-field>

              <mat-form-field fxFlex="33">
                <input matInput placeholder='{{"LAST_NAME" | translate}}'
                       formControlName="last_name">
                <mat-error *ngIf="form?.get('last_name')?.errors?.required">
                    {{formValidationService.required.message()}}
                </mat-error>
              </mat-form-field>

              <mat-form-field fxFlex="33">
                <input matInput placeholder='{{"USER_NAME" | translate}}'
                        formControlName="username">
                <mat-error *ngIf="form?.get('username')?.errors?.required || form?.get('username')?.errors?.pattern">
                    {{form.get('username')?.errors?.required && formValidationService.required.message()}}
                    {{form.get('username')?.errors?.pattern && formValidationService.username.message()}}
                </mat-error>
              </mat-form-field>
            </ng-container>
        </div>

        <div fxLayout="row wrap" fxLayoutAlign="start" fxFlex="100">
          <ng-container>
            <mat-form-field fxFlex="33">
              <input matInput placeholder='{{"EMAIL_ADDRESS" | translate}}'
                     formControlName="email">
              <mat-error *ngIf="form?.get('email')?.errors?.required || form?.get('email')?.errors?.pattern">
                  {{form?.get('email')?.errors?.required && formValidationService.required.message()}}
                  {{form?.get('email')?.errors?.pattern && formValidationService.emailPattern.message()}}
              </mat-error>
            </mat-form-field>

            <mat-form-field fxFlex="33">
              <input matInput placeholder='{{"PHONE_NUMBER" | translate}}'
                     formControlName="phone_number">
              <mat-error *ngIf="form?.get('phone_number')?.errors?.required || form?.get('phone_number')?.errors?.pattern">
                  {{form?.get('phone_number')?.errors?.required && formValidationService.required.message()}}
                  {{form?.get('phone_number')?.errors?.pattern && formValidationService.phoneNumberAfg.message()}}
              </mat-error>
            </mat-form-field>

            <mat-form-field fxFlex="33">
              <mat-label>{{'CHOOSE_ROLE'|translate}}</mat-label>
              <mat-select formControlName="role_slug" multiple (selectionChange)="roleSelect($event.value)">
                <mat-select-trigger>
                  {{form?.get('role_slug').value ? form?.get('role_slug').value[0] : ''}}
                  <span *ngIf="form?.get('role_slug').value?.length > 1" class="example-additional-selection">
                    (+{{form?.get('role_slug').value.length - 1}} {{form?.get('role_slug').value?.length === 2 ? 'other' : 'others'}})
                  </span>
                </mat-select-trigger>
                <mat-optgroup *ngFor="let group of formattedRoles" [label]="group.system">
                  <mat-option  *ngFor="let role of group.role"  [value]="role.slug" >
                    {{role.name}}
                  </mat-option>
                </mat-optgroup>
              </mat-select>
              <mat-error *ngIf="form?.get('role_slug')?.errors?.required">
                {{formValidationService.required.message()}}
              </mat-error>
            </mat-form-field>
          </ng-container>
        </div>
        <div fxLayout="row wrap" fxLayoutAlign="start" fxFlex="100">
          <ng-container>
            <mat-form-field fxFlex="33" *ngIf="(isCpmdUser || isObserver)">
              <mat-label>{{'CHOOSE_SECTOR'|translate}}</mat-label>
              <mat-select multiple (selectionChange)="sectorSelect($event.value)"  [(value)]="selectedSectors" [compareWith]="sectorsCompareWithFn">
                <mat-select-trigger>
                  {{selectedSectors[0]?.name_da}}
                  <span *ngIf="selectedSectors.length > 1" class="example-additional-selection">
                    (+{{selectedSectors.length - 1}} {{selectedSectors.length === 2 ? 'other' : 'others'}})
                  </span>
                </mat-select-trigger>
                <mat-option *ngFor="let sector of sectors" [value]="sector" aria-checked="">
                  {{sector.name_da}}
                </mat-option>
              </mat-select>
            </mat-form-field>
          </ng-container>
        </div>
        <div fxLayout="row wrap" fxLayoutAlign="start start" fxFlex="100">
          <ng-container>
            <div fxFlex="40" *ngIf="entities.length > 0">
              <span>
                <h5>{{ 'نهاد های تدارکاتی' }}</h5>
                <ul>
                  <li *ngFor="let entity of entities">{{ entity.name_da }}</li>
                </ul>
              </span>
              <span style="background-color: #fdefef; padding: 5px 30px;" *ngIf="entities.length == 0">{{ ' نهاد تدارکاتی  انتخاب نگردیده!' }}</span>
            </div>
          </ng-container>
        </div>
        <!-- <div fxLayout="row wrap" fxLayoutAlign="start" fxFlex="100">
          <ng-container>
            <mat-form-field fxFlex="33" >
              <mat-select [disabled]="isEditing" formControlName="system" multiple (selectionChange)="systemSelect($event.value)" placeholder="{{'CHOOSE_SYSTEM'|translate}}">
                  <mat-option *ngFor="let system of systems" [value]="system">{{system?.slug}}
                  </mat-option>
              </mat-select>
            </mat-form-field>
            <mat-form-field fxFlex="67" >
              <mat-select  formControlName="role_slug" multiple placeholder="{{'CHOOSE_ROLE'|translate}}" >
                  <mat-option *ngFor="let role of systemRoles" [value]="role?.slug" >{{role?.name}}
                  </mat-option>
              </mat-select>
              <mat-error *ngIf="form?.get('role_slug')?.errors?.required">
                {{formValidationService.required.message()}}
              </mat-error>
            </mat-form-field>
          </ng-container>
        </div> -->
    </mat-dialog-content>
    <mat-dialog-actions fxLayoutAlign="start">
        <button mat-raised-button color="primary"
                type="submit" [disabled]="!isValid() || notificationsService.isLoading">{{ (isEditing ? 'UPDATE' : 'SAVE')|translate}}
        </button>
        <button mat-button type="button" [mat-dialog-close]="true">{{'CANCEL'|translate}}</button>
        <button
              [disabled]="isCpmdUser"
              mat-button type="button"
              (click)="openEntityDialog(entities)">{{"SELECT_PROCUREMENT_ENTITY" | translate}}
        </button>
    </mat-dialog-actions>
</form>
