<div class="container" fxLayout="column" #container>
    <div class="npa-label">
        <ng-content select="label"></ng-content>
    </div>

    <div class="npa-controls" fxLayout="row" *ngIf="lastFile && bubbleDetector">
        <button
                type="button"
                [disabled]="!file"
                mat-icon-button
                (click)="openDialog()"
                matTooltip="فایلهای ثبت شده">
            <mat-icon>history</mat-icon>
        </button>

        <mat-chip-list>
            <mat-chip matTooltip="دانلود" (click)="downloadFile()">
                <div class="npa-file-name" [style.width]="getFileNameWidth(container)">
                    <span>{{lastFile?.original_name}}</span>
                </div>
            </mat-chip>
        </mat-chip-list>
    </div>
</div>
