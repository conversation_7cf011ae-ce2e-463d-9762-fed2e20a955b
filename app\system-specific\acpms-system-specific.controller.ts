import {Request, Response} from 'express';
import {InstanceService} from '../services/instance.service';
import {UserRole} from '../entity/user-role';
import {Role} from '../entity/role';
import {IUserRoleRecord, UserRoleRecord} from '../entity/user-role-record';
import {createQueryBuilder} from 'typeorm';
import {Database} from '../config/database';
import {HelperService} from '../services/helper.service';


export class AcpmsSystemSpecificController {
    public static instance: AcpmsSystemSpecificController;

    public static getInstance(): AcpmsSystemSpecificController {
        return InstanceService.getInstance(AcpmsSystemSpecificController);

    }

    public async assignOrRemoveContractAccess(req: Request, res: Response) {
        const queryRunner = await Database.QueryRunner();
        await queryRunner.instance.startTransaction();
        try {
            let removables = JSON.parse(req.body.remove);
            const assignable = JSON.parse(req.body.assign);
            removables = removables.concat(assignable);
            for (let i = 0; i < removables.length; i++) {
                await queryRunner.model(UserRoleRecord).delete({
                    record_id: removables[i].contract_id,
                    user_role_id: removables[i].specialist_id
                });
            }
            for (let i = 0; i < assignable.length; i++) {
                await queryRunner.model(UserRoleRecord).insert(<IUserRoleRecord>{
                    record_id: assignable[i].contract_id,
                    user_role_id: assignable[i].specialist_id
                });
            }
            await queryRunner.instance.commitTransaction();
             res.status(200).json([]);
        } catch (e) {
            await queryRunner.instance.rollbackTransaction();
             res.status(500).json(e);
        } finally {
          return await queryRunner.instance.release();
        }
    }

    public async getContractContractManager(req: Request, res: Response) {
        try {
            const contractId = req.query['contract_id'];
            const queriedData = await createQueryBuilder('UserRoleRecord')
                .leftJoin('UserRoleRecord.userRole', 'userRole')
                .leftJoin('userRole.role', 'role')
                .leftJoin('userRole.user', 'user')
                .where(`UserRoleRecord.record_id=:record_id`, {record_id: contractId})
                .andWhere(`role.slug='cpms-contract-manager'`)
                .addSelect('userRole')
                .addSelect('user.email')
                .addSelect('user.name')
                .addSelect('user.last_name')
                .orderBy('user.name', 'ASC')
                .getOne();
            let user;
            if (queriedData) {
                user = queriedData['userRole']['user'];
            }
            if (user) {
                user['full_name'] = (user.name === user.last_name) ? user.name : (user.name + ' ' + user.last_name);
                user['id'] = queriedData['userRole']['id'];
                delete user.name;
                delete user.last_name;
            }
            if (!user) {
                return res.status(404).json();
            }
            return res.status(200).json(user);
        } catch (e) {
            return res.status(500).json(e);
        }
    }
    public async getCompanyUserRoles(req: Request, res: Response) {
        try {
            const contractId = req.query['contract_id'];
            const queriedData = await createQueryBuilder('UserRoleRecord')
                .leftJoin('UserRoleRecord.userRole', 'userRole')
                .leftJoin('userRole.role', 'role')
                .leftJoin('userRole.user', 'user')
                .where(`UserRoleRecord.record_id=:record_id`, {record_id: contractId})
                .andWhere(`role.slug='cpms-company'`)
                .addSelect('userRole')
                .addSelect('user.email')
                .addSelect('user.name')
                .addSelect('user.last_name')
                .orderBy('user.name', 'ASC')
                .getOne();
            let user;
            if (queriedData) {
                user = queriedData['userRole']['user'];
            }
            if (user) {
                user['full_name'] = (user.name === user.last_name) ? user.name : (user.name + ' ' + user.last_name);
                user['id'] = queriedData['userRole']['id'];
                delete user.name;
                delete user.last_name;
            }
            if (!user) {
                return res.status(404).json();
            }
            return res.status(200).json(user);
        } catch (e) {
            return res.status(500).json(e);
        }
    }

    // public async getCompanyUser(req: Request, res: Response) {
    //     try {
    //         // const licenses = HelperService.normalizeQueryParams(req.query['licenses']);
    //         const licenses = req.query['licenses'];
    //         console.log(licenses);
    //         const query = <any>await createQueryBuilder('UserRoleVendor')
    //             .select('UserRoleVendor.id')
    //             .select('UserRoleVendor.vendor_license_number')
    //             .leftJoin('UserRoleVendor.userRole', 'userRole')
    //             .leftJoin('userRole.user', 'user')
    //             .leftJoin('userRole.role', 'role')
    //             .addSelect('user.name')
    //             .addSelect('user.last_name')
    //             .addSelect('user.email')
    //             .addSelect('user.username')
    //             .addSelect('role.slug')
    //             .where(`UserRoleVendor.vendor_license_number in (:...licenses)`, {licenses: licenses})
    //             .getMany();
    //         query.forEach((item) => {
    //             item['full_name'] = (item.user.name === item.user.last_name) ? item.user.name : item.user.name + item.user.last_name;
    //             item['role_slug'] = item.role.slug;
    //             item['email'] = item.user.email;
    //             item['username'] = item.user.username;
    //             delete item.user;
    //             delete item.role;
    //         });
    //         return res.status(200).json(query);
    //     } catch (e) {
    //         console.log(e);
    //         return res.status(500).json(e);
    //     }
    // }

    public async getContractManagers(req: Request, res: Response) {
        try {
            const procurementEntityId = req.query['procurement_entity_id'];
            const user = await getUserByPE('cpms-contract-manager', procurementEntityId);
            return res.status(200).json(user);
        } catch (e) {
            console.log(e);
            return res.status(500).json(e);
        }
    }

    public async getCompanyUsers(req: Request, res: Response) {
        try {
            const procurementEntityId = req.query['procurement_entity_id'];
            const user = await getCompanies('cpms-company', procurementEntityId);
            return res.status(200).json(user);
        } catch (e) {
            console.log(e);
            return res.status(500).json(e);
        }
    }

    public async getAwardAuthority(req: Request, res: Response) {
        try {
            const procurementEntityId = req.query['procurement_entity_id'];
            const user = await getUserByPE('cpms-award-authority', procurementEntityId);
            return res.status(200).json(user);
        } catch (e) {
            console.log(e);
            return res.status(500).json(e);
        }
    }

    public async getCpmDirector(req: Request, res: Response) {
        try {
            const query = <any>await createQueryBuilder('UserRole')
                .select('UserRole.id')
                .leftJoin('UserRole.user', 'user')
                .leftJoin('UserRole.role', 'role')
                .leftJoin('user.status', 'status')
                .addSelect('user.name')
                .addSelect('user.last_name')
                .addSelect('user.email')
                .addSelect('role.id')
                .where(`role.slug= "cpms-cpm-director"`)
                .andWhere(`status.slug= "active"`)
                .getOne();
            if (query) {
                query['full_name'] = (query.user.name === query.user.last_name) ? query.user.name : query.user.name + ' ' + query.user.last_name;
                query['role_id'] = query.role.id;
                query['email'] = query.user.email;
                delete query.user;
                delete query.role;
            }
            if (!query) {
                return res.status(404).json();
            }
            return res.status(200).json(query);
        } catch (e) {
            console.log(e);
            return res.status(500).json(e);
        }
    }

    public async getContractSpecialist(req: Request, res: Response) {
        try {
            const contractId = req.query['contract_id'];
            const queriedData = await createQueryBuilder('UserRoleRecord')
                .leftJoin('UserRoleRecord.userRole', 'userRole')
                .leftJoin('userRole.role', 'role')
                .leftJoin('userRole.user', 'user')
                .where(`UserRoleRecord.record_id=:record_id`, {record_id: contractId})
                .andWhere(`role.slug='cpms-specialist'`)
                .addSelect('userRole')
                .addSelect('user.email')
                .addSelect('user.name')
                .addSelect('user.last_name')
                .getOne();

            if (!queriedData) {
                return res.status(404).json();
            }
            const user = queriedData['userRole']['user'];
            user['full_name'] = (user.name === user.last_name) ? user.name : (user.name + ' ' + user.last_name);
            user['id'] = queriedData['userRole']['id'];
            delete user.name;
            delete user.last_name;
            if (!user) {
                return res.status(404).json();
            }
            return res.status(200).json(user);
        } catch (e) {
            return res.status(500).json(e);
        }
    }

    public async getSpecialists(req: Request, res: Response) {
        try {
            const sectorId = req.query['sector_id'];
            const user = await getUserBySector('cpms-specialist', sectorId);
            return res.status(200).json(user);
        } catch (e) {
            console.log(e);
            return res.status(500).json(e);
        }
    }

    public async getCpmManager(req: Request, res: Response) {
        try {
            const sectorId = req.query['sector_id'];
            const user = await getUserBySector('cpms-cpm-manager', sectorId);
            return res.status(200).json(user);
        } catch (e) {
            console.log(e);
            return res.status(500).json(e);
        }
    }

    public async getUsersByIds(req: Request, res: Response) {
        try {
            // const ids = HelperService.normalizeQueryParams(req.query['ids']);
            const ids = req.body;
            const query = <any>await createQueryBuilder('UserRole')
                .select('UserRole.id')
                .leftJoin('UserRole.user', 'user')
                .leftJoin('UserRole.role', 'role')
                .addSelect('user.name')
                .addSelect('user.last_name')
                .addSelect('user.email')
                .addSelect('role.slug')
                .where(`UserRole.id in (:...ids)`, {ids: ids})
                .getMany();
            query.forEach((item) => {
                item['full_name'] = (item.user.name === item.user.last_name) ? item.user.name : item.user.name + ' ' + item.user.last_name;
                item['role_slug'] = item.role.slug;
                item['email'] = item.user.email;
                delete item.user;
                delete item.role;
            });
            return res.status(200).json(query);
        } catch (e) {
            console.log(e);
            return res.status(500).json(e);
        }
    }

    public async getRolesByIds(req: Request, res: Response) {
        try {
            const ids = HelperService.normalizeQueryParams(req.query['ids']);
            const query = <any>await createQueryBuilder('Role')
                .select('Role.id')
                .addSelect('Role.name')
                .addSelect('Role.slug')
                .where(`Role.id in (:...ids)`, {ids: ids})
                .getMany();
            return res.status(200).json(query);
        } catch (e) {
            console.log(e);
            return res.status(500).json(e);
        }
    }

    public async assignContractToUser(req: Request, res: Response) {
        const queryRunner = await Database.QueryRunner();
        await queryRunner.instance.startTransaction();
        try {
            const data: IUserRoleRecord = req.body;
            const dataToUpdate = <any>await createQueryBuilder('UserRoleRecord')
                .leftJoin('UserRoleRecord.userRole', 'userRole')
                .leftJoin('userRole.role', 'role')
                .where(`UserRoleRecord.record_id = :record_id`, {record_id: data.record_id})
                .andWhere(`role.slug = :slug`, {slug: 'cpms-contract-manager'})
                .getOne();
            if (dataToUpdate) {
                dataToUpdate.user_role_id = data.user_role_id;
                await queryRunner.model(UserRoleRecord).save(<any>dataToUpdate);

            } else {
                const userRoleRecordObject = <any>await queryRunner.model(UserRoleRecord).create(<any>data);
                await queryRunner.model(UserRoleRecord).save(<any>userRoleRecordObject);
            }


            await queryRunner.instance.commitTransaction();
            res.status(201).json({});
        } catch (e) {
            console.log(e);
            const error = e.toString();
            await queryRunner.instance.rollbackTransaction();
            res.status(409).json(error);
        } finally {
          return await queryRunner.instance.release();
        }
    }

    public async assignContractToCompanyUser(req: Request, res: Response) {
        const queryRunner = await Database.QueryRunner();
        await queryRunner.instance.startTransaction();
        try {
            const data: IUserRoleRecord = req.body;
            const dataToUpdate = <any>await createQueryBuilder('UserRoleRecord')
                .leftJoin('UserRoleRecord.userRole', 'userRole')
                .leftJoin('userRole.role', 'role')
                .where(`UserRoleRecord.record_id = :record_id`, {record_id: data.record_id})
                .andWhere(`UserRoleRecord.user_role_id = :user_role_id`, {user_role_id: data.user_role_id})
                .andWhere(`role.slug = :slug`, {slug: 'cpms-company'})
                .getOne();
            if (dataToUpdate) {
                dataToUpdate.user_role_id = data.user_role_id;
                await queryRunner.model(UserRoleRecord).save(<any>dataToUpdate);

            } else {
                const userRoleRecordObject = <any>await queryRunner.model(UserRoleRecord).create(<any>data);
                await queryRunner.model(UserRoleRecord).save(<any>userRoleRecordObject);
            }


            await queryRunner.instance.commitTransaction();
            res.status(201).json({});
        } catch (e) {
            console.log(e);
            const error = e.toString();
            await queryRunner.instance.rollbackTransaction();
            res.status(409).json(error);
        } finally {
            return await queryRunner.instance.release();
        }
    }
}

async function getUserBySector(roleSlug, sectorId?: undefined) {
    let data = [];
    if (sectorId) {
        data = <any>await createQueryBuilder('UserRole')
            .select('UserRole.id')
            .leftJoin('UserRole.user', 'user')
            .leftJoin('UserRole.role', 'role')
            .leftJoin('UserRole.userRoleSector', 'userRoleSector')
            .leftJoin('user.status', 'status')
            .addSelect('user.name')
            .addSelect('user.last_name')
            .addSelect('user.email')
            .addSelect('role.id')
            .where(`role.slug=:slug `, {slug: roleSlug})
            .andWhere(`status.slug= "active"`)
            .andWhere(`userRoleSector.sector_id=:sector_id`, {sector_id: sectorId})
            .getMany();
    } else {
        data = <any>await createQueryBuilder('UserRole')
            .select('UserRole.id')
            .leftJoin('UserRole.user', 'user')
            .leftJoin('UserRole.role', 'role')
            .leftJoin('user.status', 'status')
            .addSelect('user.name')
            .addSelect('user.last_name')
            .addSelect('user.email')
            .addSelect('role.id')
            .where(`role.slug=:slug`, {slug: roleSlug})
            .andWhere(`status.slug= "active"`)
            .getMany();
    }
    data.forEach((item) => {
        item.full_name = item.user.name;
        if (item.user.name !== item.user.last_name) {
            item.full_name = item.user.name + ' ' + item.user.last_name;
        }
        item.role_id = item.role.id;
        item.email = item.user.email;
        delete item.user;
        delete item.role;
    });
    return data;
}

async function getUserByPE(roleSlug, procurementEntityId?: undefined) {
    let data = [];
    if (procurementEntityId) {
        data = <any>await createQueryBuilder('UserRole')
            .select('UserRole.id')
            .leftJoin('UserRole.user', 'user')
            .leftJoin('UserRole.role', 'role')
            .leftJoin('UserRole.userRolePe', 'userRolePe')
            .leftJoin('user.status', 'status')
            .addSelect('user.name')
            .addSelect('user.last_name')
            .addSelect('user.email')
            .addSelect('role.id')
            .where(`role.slug=:slug `, {slug: roleSlug})
            .andWhere(`status.slug= "active"`)
            .andWhere(`userRolePe.procurement_entity_id=:procurement_entity_id `, {procurement_entity_id: procurementEntityId})
            .getMany();

    } else {
        data = <any>await createQueryBuilder('UserRole')
            .select('UserRole.id')
            .leftJoin('UserRole.user', 'user')
            .leftJoin('UserRole.role', 'role')
            .addSelect('user.name')
            .addSelect('user.last_name')
            .addSelect('user.email')
            .addSelect('role.id')
            .where(`role.slug=:slug`, {slug: roleSlug})
            .getMany();
    }
    data.forEach((item) => {
        item.full_name = item.user.name;
        if (item.user.name !== item.user.last_name) {
            item.full_name = item.user.name + ' ' + item.user.last_name;
        }
        item.role_id = item.role.id;
        item.email = item.user.email;
        delete item.user;
        delete item.role;
    });
    console.log('=========================================');
    console.log(data);
    console.log('=========================================');
    return data;
}


async function getCompanies(roleSlug, procurementEntityId?: undefined) {
    let data = [];
    if (procurementEntityId) {
        data = <any>await createQueryBuilder('UserRole')
            .select('UserRole.id')
            .leftJoin('UserRole.user', 'user')
            .leftJoin('UserRole.role', 'role')
            .leftJoin('UserRole.userRolePe', 'userRolePe')
            .leftJoin('user.status', 'status')
            .addSelect('user.name')
            .addSelect('user.last_name')
            .addSelect('user.email')
            .addSelect('role.id')
            .where(`role.slug=:slug `, {slug: roleSlug})
            .andWhere(`status.slug= "active"`)
            // .andWhere(`userRolePe.procurement_entity_id=:procurement_entity_id `, {procurement_entity_id: procurementEntityId})
            .getMany();

    } else {
        data = <any>await createQueryBuilder('UserRole')
            .select('UserRole.id')
            .leftJoin('UserRole.user', 'user')
            .leftJoin('UserRole.role', 'role')
            .addSelect('user.name')
            .addSelect('user.last_name')
            .addSelect('user.email')
            .addSelect('role.id')
            .where(`role.slug=:slug`, {slug: roleSlug})
            .getMany();
    }
    data.forEach((item) => {
        item.full_name = item.user.name;
        if (item.user.name !== item.user.last_name) {
            item.full_name = item.user.name + ' ' + item.user.last_name;
        }
        item.role_id = item.role.id;
        item.email = item.user.email;
        delete item.user;
        delete item.role;
    });
    console.log('=========================================');
    console.log(data);
    console.log('=========================================');
    return data;
}
