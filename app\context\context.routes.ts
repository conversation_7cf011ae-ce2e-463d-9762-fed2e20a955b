import {<PERSON><PERSON>out<PERSON>} from '../app.routes';
import {Context<PERSON>ontroller} from './context.controller';
import {Authentication} from '../middlewares/authentication';

export const CONTEXT_ROUTES: IRoute[] = [
  {
    url: '/dashboards',
    httpMethod: 'get',
    controllerMethod: ContextController.getInstance().getRoleDashboards,
    // middleware: [Authentication.validateToken]
  },
  {
    url: '/pages',
    httpMethod: 'get',
    controllerMethod: ContextController.getInstance().getRoleNonDashboards,
    // middleware: [Authentication.validateToken]
  },
  {
    url: '/:context/permissions',
    httpMethod: 'get',
    controllerMethod: ContextController.getInstance().getPermissions,
    middleware: [Authentication.validateToken]
  },
  {
    url: '/permission/map',
    httpMethod: 'get',
    controllerMethod: ContextController.getInstance().getPermissionMap
  }

];
