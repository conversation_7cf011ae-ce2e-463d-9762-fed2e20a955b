import {Component, OnInit, Inject} from '@angular/core';
import {MAT_SNACK_BAR_DATA} from '@angular/material';
import {TranslateService} from '../services/translate.service';

@Component({
    selector: 'npa-snackbar-message',
    templateUrl: './snackbar-message.component.html',
    styleUrls: ['./snackbar-message.component.styl'],
})
export class SnackbarMessageComponent implements OnInit {
    message = 'LOADING';

    constructor(@Inject(MAT_SNACK_BAR_DATA) public data: any, public translate: TranslateService) {
    }

    ngOnInit() {
        this.message = this.data ? this.data.message : this.message;
    }

}
