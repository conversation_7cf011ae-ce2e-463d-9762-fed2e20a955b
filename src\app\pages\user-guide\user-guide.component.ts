import {Component, OnInit} from '@angular/core';
import {AuthService} from '../../services/auth.service';
import {NotificationsService} from '../shared/services/notifications.service';
import {TranslateService} from '../shared/services/translate.service';

@Component({
    selector: 'npa-user-guide',
    templateUrl: './user-guide.component.html',
    styleUrls: ['./user-guide.component.styl']
})
export class UserGuideComponent implements OnInit {
    lang: string;

    constructor(private _notificationsService: NotificationsService,
                public authService: AuthService,
                private translate: TranslateService) {
    }

    ngOnInit() {
        this.lang = this.translate.lang;
        this._notificationsService.dismissLoading();
    }

}
