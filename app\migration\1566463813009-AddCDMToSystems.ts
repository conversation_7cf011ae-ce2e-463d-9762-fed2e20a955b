import {MigrationInterface, QueryRunner} from 'typeorm';

export class AddCDMToSystems1566463813009 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<any> {
        await queryRunner.query(`
            INSERT INTO
              systems (id, slug, url, description, icon_name)
            VALUES
              (5, 'cdm', '${process.env.CUSTOM_CDM_BASE_URL}', 'Common Data Manager', 'open_with')`);
    }

    public async down(queryRunner: QueryRunner): Promise<any> {
        await queryRunner.query(`Delete from systems where slug = 'cdm' `);
    }


}
