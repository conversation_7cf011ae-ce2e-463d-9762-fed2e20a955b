import {Component, OnInit} from '@angular/core';
import {Form<PERSON>uilder, FormGroup, Validators} from '@angular/forms';
import {AuthService} from '../../../services/auth.service';
import {IUserProfile} from '../user-profile-types';
import {HttpErrorResponse} from '@angular/common/http';
import {NPA_ALERTS} from '../../shared/consts/messages';
import {NotificationsService} from '../../shared/services/notifications.service';
import {HttpService} from '../../../services/http.service';
import {MatDialogRef} from '@angular/material';
import {FormValidationService} from '../../../services/form-validation.service';
import {TranslateService} from '../../shared/services/translate.service';

@Component({
    selector: 'npa-update-profile',
    templateUrl: './update-profile.component.html',
    styleUrls: ['./update-profile.component.styl']
})
export class UpdateProfileComponent implements OnInit {

    form: FormGroup;
    data: IUserProfile;

    constructor(private formBuilder: FormBuilder,
                private _authService: AuthService,
                public translate: TranslateService,
                private _httpService: HttpService,
                private dialogRef: MatDialogRef<UpdateProfileComponent>,
                public notificationsService: NotificationsService,
                public formValidationService: FormValidationService) {
    }

    ngOnInit() {
        this.notificationsService.startLoading();
        this._httpService.get(`api/users?user_role_id=${this._authService.loggedInUser.id}`)
            .subscribe((response: IUserProfile) => {
                this.notificationsService.dismissLoading();
                this.data = response;
                this.initProfileForm(this.data);
            }, (error: HttpErrorResponse) => {
                this.notificationsService.error(NPA_ALERTS.ERROR);
                console.error(error);
            });
    }


    initProfileForm(formData) {
        this.form = this.formBuilder.group({
            name: [
                formData && formData.name,
                [Validators.required]
            ],
            last_name: [
                formData && formData.last_name,
                [Validators.required]
            ],
            username: [
                formData && formData.username,
                [Validators.required, Validators.pattern(this._authService.usernamePattern)]
            ],
            phone_number: [
                formData && formData.phone_number, [
                    this.formValidationService.required.validator, this.formValidationService.phoneNumberAfg.validator
                ]
            ],
            email: [
                formData && formData.email,
                [Validators.required, Validators.email]
            ]
        });
    }

    updateProfile() {
        this.notificationsService.startLoading();
        this._httpService._put(`api/users?user_role_id=${this._authService.loggedInUser.id}`, this.form.value)
            .subscribe((response: IUserProfile) => {
                this.notificationsService.success(this.translate.translateKey('SUCCESSFULLY_DON'));
                this.data = this.form.value;
                this.dialogRef.close({
                    data: this.data,
                    status: true
                });
            }, (error: HttpErrorResponse) => {
                this.notificationsService.error(NPA_ALERTS.ERROR);
                console.error(error);
            });
    }
}
