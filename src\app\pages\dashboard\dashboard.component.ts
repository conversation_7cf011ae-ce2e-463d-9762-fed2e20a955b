import {Component, OnInit} from '@angular/core';
import {NotificationsService} from '../shared/services/notifications.service';
import {AuthService} from '../../services/auth.service';
import {TranslateService} from '../shared/services/translate.service';
import {ProcurementTypeContextService} from '../../services/procurement-type-context.service';
import {ActivatedRoute, Router} from '@angular/router';

@Component({
    selector: 'npa-dashboard',
    templateUrl: './dashboard.component.html',
    styleUrls: ['./dashboard.component.styl']
})

export class DashboardComponent implements OnInit {
    lang;

    constructor(private notificationsService: NotificationsService,
                public authService: AuthService,
                public contextService: ProcurementTypeContextService,
                private router: Router,
                private activatedRoute: ActivatedRoute,
                private translate: TranslateService) {
        this.lang = this.translate.lang;
    }

    ngOnInit() {

        this.notificationsService.dismissLoading();
    }
}
