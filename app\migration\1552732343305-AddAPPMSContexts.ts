import {MigrationInterface, QueryRunner} from 'typeorm';

export class AddAPPMSContexts1552732343305 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<any> {
        await queryRunner.query(`
            INSERT INTO
              contexts (slug, url, description, system_id, is_dashboard)
            VALUES
            ('created-plans-count-widget','','', 4, true),
            ('confirmed-plans-count-widget','','', 4, true),
            ('published-plans-count-widget','','', 4, true),
            ('transferred-plans-count-widget','','', 4, true),
            ('cancelled-plans-count-widget','','', 4, true),
            ('implemented-plans-count-widget','','', 4, true),
            ('planning-analyzed-plans-count-widget','','', 4, true),
            ('implemented-analyzed-plans-count-widget','','', 4, true),
            ('assigned-plans-count-widget','','', 4, true),
            ('confirmed-analyzed-plans-count-widget','','', 4, true),
            ('confirmed-plans-vs-created-plans-percentage-widget','','', 4, true),
            ('published-plans-vs-confirmed-plans-percentage-widget','','', 4, true),
            ('analyzed-plans-vs-assigned-plans-percentage-widget','','', 4, true),
            ('five-most-contingency-results-predicted','','', 4, true),
            ('five-most-frequent-cpv-divisions','','', 4, true),
            ('plan-vs-actual-projects-number-based-on-timeline-non-consultancy','','', 4, true),
            ('plan-vs-actual-projects-number-based-on-timeline-consultancy','','', 4, true),
            ('five-most-contingency-risks-predicted','','', 4, true),
            ('plan-vs-actual-project-number-and-amount-based-on-procurement-type','','', 4, true),
            ('project-number-and-amount-based-on-award-authority-threshold','','', 4, true),
            ('five-most-cpv-based-on-amount','','', 4, true),
            ('plan-vs-actual-project-number-and-amount-based-on-budget-type','','', 4, true),
            ('plan-vs-actual-project-number-and-amount-based-on-contract-type','','', 4, true),
            ('plan-vs-actual-project-number-and-amount-based-on-procurement-preference','','', 4, true),
            ('plan-vs-actual-project-number-and-amount-based-on-selection-method','','', 4, true),
            ('plan-vs-actual-project-number-and-amount-based-on-procurement-method','','', 4, true),
            ('plan-vs-actual-project-number-and-amount-based-on-plan-status','','', 4, true),
            ('plan-vs-amount-project-based-on-province','','', 4, true),
            ('plan-vs-actual-project-amount-based-on-sector','','', 4, true),
            ('plan-vs-actual-project-number-based-on-sector','','', 4, true),
            ('project','','', 4, false),
            ('lot','','', 4, false),
            ('project-planning','','', 4, false),
            ('project-planning-procurement-plan-general-details','','', 4, false),
            ('project-planning-cpv','','', 4, false),
            ('project-planning-location-domestic','','', 4, false),
            ('project-planning-location-foreign','','', 4, false),
            ('project-planning-procurement-process-progress-timeline','','', 4, false),
            ('project-planning-potential-risks','','', 4, false),
            ('project-planning-documents','','', 4, false),
            ('project-planning-challenges','','', 4, false),
            ('project-planning-analysis','','', 4, false),
            ('project-implementation','','', 4, false),
            ('project-implementation-procurement-plan-general-details','','', 4, false),
            ('project-implementation-cpv','','', 4, false),
            ('project-implementation-location-domestic','','', 4, false),
            ('project-implementation-location-foreign','','', 4, false),
            ('project-implementation-procurement-process-progress-timeline','','', 4, false),
            ('project-implementation-potential-risks','','', 4, false),
            ('project-implementation-documents','','', 4, false),
            ('project-implementation-challenges','','', 4, false),
            ('project-implementation-analysis','','', 4, false)
            `);
    }

    public async down(queryRunner: QueryRunner): Promise<any> {
    }

}
