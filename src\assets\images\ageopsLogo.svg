<svg width="640" height="479.99999999999994" xmlns="http://www.w3.org/2000/svg">
 <metadata id="metadata8">image/svg+xml</metadata>
 <defs>
  <clipPath id="clipPath20" clipPathUnits="userSpaceOnUse">
   <path id="path18" d="m2.000012,593.279988l841.89,0l0,-595.28l-841.89,0l0,595.28z"/>
  </clipPath>
  <clipPath id="clipPath52" clipPathUnits="userSpaceOnUse">
   <path id="path50" d="m705.787012,147.007988c-5.72,-5.249 -8.578,-12.204 -8.578,-20.889l0,0c0,-6.516 1.812,-11.907 5.469,-16.172l0,0c3.655,-4.282 9.937,-8.19 18.874,-11.72l0,0c1.532,-0.594 3.797,-1.438 6.813,-2.547l0,0c8.436,-3.11 14.391,-5.829 17.828,-8.157l0,0c2.343,-1.655 4.094,-3.717 5.265,-6.186l0,0c1.157,-2.468 1.751,-5.376 1.751,-8.719l0,0c0,-5.672 -1.984,-10.218 -5.94,-13.625l0,0c-3.952,-3.422 -9.248,-5.125 -15.874,-5.125l0,0c-5.969,0 -11.233,1.953 -15.794,5.876l0,0c-4.565,3.905 -7.799,9.28 -9.672,16.124l0,0l-11.72,-3.673c2.937,-9.343 7.577,-16.406 13.935,-21.171l0,0c6.346,-4.78 14.315,-7.156 23.909,-7.156l0,0c10.015,0 18.048,2.719 24.092,8.188l0,0c6.033,5.451 9.064,12.672 9.064,21.64l0,0c0,7.563 -1.939,13.641 -5.798,18.251l0,0c-3.859,4.608 -10.812,8.857 -20.875,12.732l0,0c-1.484,0.579 -3.577,1.36 -6.281,2.33l0,0c-8.499,3.157 -13.905,5.656 -16.235,7.499l0,0c-2.343,1.907 -4.077,3.922 -5.171,6.032l0,0c-1.094,2.108 -1.64,4.421 -1.64,6.922l0,0c0,5.312 1.64,9.546 4.938,12.688l0,0c3.295,3.138 7.734,4.718 13.311,4.718l0,0c5.892,0 10.83,-1.625 14.797,-4.861l0,0c3.969,-3.25 6.812,-7.952 8.5,-14.139l0,0l11.454,3.047c-2.516,8.406 -6.657,14.844 -12.438,19.282l0,0c-5.782,4.436 -12.813,6.671 -21.11,6.671l0,0c-9.53,0 -17.155,-2.626 -22.874,-7.86m-185.155,-7.202c-7.625,-10.032 -11.421,-23.487 -11.421,-40.361l0,0c0,-16.939 3.812,-30.421 11.469,-40.485l0,0c7.64,-10.06 17.812,-15.092 30.53,-15.092l0,0c12.75,0 22.937,5.032 30.563,15.092l0,0c7.625,10.064 11.438,23.546 11.438,40.485l0,0c0,16.97 -3.813,30.439 -11.438,40.439l0,0c-7.626,9.983 -17.813,14.984 -30.563,14.984l0,0c-12.766,0 -22.953,-5.031 -30.578,-15.062m8.532,-73.953c-5.312,7.983 -7.953,19.141 -7.953,33.482l0,0c0,14.377 2.641,25.547 7.953,33.549l0,0c5.313,7.983 12.654,11.984 22.046,11.984l0,0c9.376,0 16.718,-4.001 22.033,-11.984l0,0c5.311,-8.002 7.968,-19.172 7.968,-33.549l0,0c0,-14.341 -2.657,-25.499 -7.968,-33.482l0,0c-5.315,-8.002 -12.657,-11.985 -22.033,-11.985l0,0c-9.392,0 -16.733,3.983 -22.046,11.985m-190.556,74.326c-7.468,-9.795 -11.203,-23.576 -11.203,-41.328l0,0c0,-16.968 3.781,-30.374 11.345,-40.217l0,0c7.561,-9.844 17.858,-14.766 30.874,-14.766l0,0c6.297,0 11.782,1.079 16.437,3.265l0,0c4.658,2.189 8.671,5.532 12.017,10.047l0,0l0.42,-12.312l9.907,0l0,53l-37,0l0,-10.001l25,0l0,-7.749c0,-8.125 -2.361,-14.531 -7.064,-19.22l0,0c-4.701,-4.687 -11.062,-7.03 -19.093,-7.03l0,0c-9.702,0 -17.264,4.016 -22.703,12.077l0,0c-5.436,8.047 -8.14,19.297 -8.14,33.75l0,0c0,14.501 2.611,25.642 7.844,33.453l0,0c5.219,7.812 12.672,11.72 22.36,11.72l0,0c6.436,0 11.827,-1.999 16.156,-6l0,0c4.311,-4.001 7.532,-10.001 9.639,-18l0,0l12,2.422c-3.015,10.532 -7.64,18.421 -13.857,23.689l0,0c-6.235,5.249 -14.08,7.889 -23.516,7.889l0,0c-13.486,0 -23.955,-4.908 -31.423,-14.689m275.681,11.689l0,-107l11.999,0l0,48.001l22.531,0c10.609,0 18.843,2.593 24.689,7.812l0,0c5.844,5.204 8.78,12.563 8.78,22.078l0,0c0,9.938 -3,17.282 -8.984,22.016l0,0c-6.001,4.718 -15.266,7.093 -27.814,7.093l0,0l-31.201,0zm11.999,-9.999l17.001,0c9.718,0 16.655,-1.469 20.797,-4.392l0,0c4.125,-2.938 6.202,-7.795 6.202,-14.609l0,0c0,-7.203 -1.983,-12.344 -5.92,-15.406l0,0c-3.938,-3.064 -10.69,-4.595 -20.22,-4.595l0,0l-17.86,0l0,39.002zm-194.382,9.999l0,-107l65.999,0l0,10l-54,0l0,41.001l36.001,0l0,9.998l-36.001,0l0,36.002l51.002,0l0,9.999l-63.001,0zm-164.357,0l-33.065,-107l12.596,0l9.467,32.999l39.875,0l9.625,-32.999l12.439,0l-32.923,107l-18.014,0zm7.953,-8.046l1.67,0l15.985,-55.955l-33.782,0l16.127,55.955z"/>
  </clipPath>
 </defs>
 <g>
  <title>background</title>
  <rect x="-1" y="-1" width="642" height="482" id="canvas_background" fill="none"/>
 </g>
 <g>
  <title>Layer 1</title>
  <g transform="matrix(0.8215221383117485,0,0,-0.7955372460348832,-4.32184317845261,476.6272392228688) " id="g10">
   <path id="path12" d="m486.373,-0.75l295.203,0l0,595.28l-295.203,0l0,-595.28z" fill-rule="nonzero" fill="#2f5597"/>
   <g id="g14">
    <g clip-path="url(#clipPath20)" id="g16">
     <g id="g22">
      <path id="path24" d="m449.2178,560.11737l70.132,0c3.999,-23.91 8.071,-47.809 11.91,-71.745c0.493,-3.077 1.734,-4.262 4.618,-4.985c19.933,-4.996 38.66,-12.922 56.405,-23.293c1.493,-0.873 4.536,-0.682 6.02,0.292c7.012,4.597 13.724,9.65 20.557,14.521c12.903,9.197 25.816,18.38 38.417,27.349c16.68,-16.667 33.036,-33.009 49.69,-49.65c-0.929,-1.341 -2.039,-2.984 -3.191,-4.597c-12.831,-17.959 -25.623,-35.946 -38.558,-53.83c-1.837,-2.541 -2.066,-4.369 -0.354,-7.18c10.5,-17.237 18.137,-35.726 23.034,-55.296c0.845,-3.38 2.256,-4.65 5.718,-5.203c23.756,-3.794 47.468,-7.863 71.195,-11.844l0,-70.132c-23.736,-3.982 -47.455,-8.058 -71.222,-11.842c-3.492,-0.557 -4.845,-1.869 -5.69,-5.222c-4.932,-19.567 -12.544,-38.078 -23.062,-55.315c-1.743,-2.855 -1.398,-4.674 0.419,-7.181c10.394,-14.329 20.636,-28.768 30.925,-43.175c3.696,-5.175 7.381,-10.36 10.679,-14.992c-16.717,-16.776 -33.039,-33.158 -49.611,-49.79c-1.313,0.906 -2.968,2.011 -4.586,3.166c-17.972,12.829 -35.96,25.638 -53.882,38.536c-2.274,1.637 -3.939,2.234 -6.715,0.533c-17.538,-10.737 -36.407,-18.363 -56.28,-23.638c-1.731,-0.46 -3.918,-2.696 -4.242,-4.416c-2.491,-13.258 -4.556,-26.596 -6.792,-39.903c-1.798,-10.704 -3.648,-21.4 -5.464,-32.036l-69.816,0c-0.403,1.692 -0.869,3.24 -1.134,4.821c-3.707,22.122 -7.321,44.259 -11.192,66.352c-0.33,1.882 -2.043,4.62 -3.611,5.021c-21.36,5.468 -41.375,14.037 -60.232,25.611c-20.92,-14.905 -41.673,-29.69 -61.572,-43.867c-16.93,16.827 -33.319,33.115 -49.984,49.679c14.459,20.263 29.176,40.891 44.013,61.687c-11.678,19.021 -20.333,39.204 -25.715,60.784c-0.317,1.271 -2.333,2.736 -3.767,3.008c-8.776,1.669 -17.615,3.004 -26.428,4.479c-15.256,2.555 -30.508,5.132 -45.873,7.718l0,69.886c1.776,0.413 3.348,0.868 4.951,1.136c21.943,3.677 43.871,7.451 65.854,10.866c3.783,0.588 5.071,2.111 5.958,5.605c4.875,19.196 12.236,37.426 22.629,54.313c2.099,3.41 1.628,5.57 -0.555,8.576c-12.867,17.72 -25.537,35.582 -38.261,53.406c-1.122,1.572 -2.149,3.211 -2.931,4.384c16.712,16.761 32.934,33.032 49.472,49.62c1.417,-0.975 3.062,-2.074 4.672,-3.222c17.975,-12.826 35.973,-25.62 53.883,-38.537c2.488,-1.794 4.294,-2.198 7.179,-0.448c17.414,10.567 36.071,18.284 55.834,23.234c2.871,0.719 4.145,1.89 4.64,4.979c3.842,23.947 7.914,47.856 11.916,71.777" fill-rule="nonzero" fill="#ffffff"/>
     </g>
     <g id="g26">
      <path id="path28" d="m437.75589,314.66531c-5.619,-37.891 20.631,-73.336 58.518,-79.015c38.042,-5.702 73.544,20.54 79.222,58.557c5.68,38.034 -20.59,73.503 -58.631,79.163c-38.023,5.656 -73.463,-20.643 -79.109,-58.705m72.195,130.132c11.478,-1.711 22.955,-3.422 34.433,-5.133c0.214,-12.032 0.464,-24.063 0.597,-36.096c0.017,-1.548 0.54,-2.22 1.902,-2.786c9.421,-3.912 18.036,-9.174 25.989,-15.565c0.669,-0.537 2.177,-0.667 2.977,-0.297c3.779,1.744 7.444,3.734 11.155,5.624c7.009,3.572 14.021,7.135 20.863,10.617c6.971,-9.404 13.804,-18.625 20.764,-28.014c-0.555,-0.59 -1.22,-1.316 -1.904,-2.024c-7.615,-7.877 -15.211,-15.772 -22.87,-23.606c-1.088,-1.113 -1.335,-1.994 -0.7,-3.5c3.894,-9.231 6.29,-18.867 7.261,-28.834c0.168,-1.722 0.768,-2.449 2.428,-2.974c11.386,-3.601 22.729,-7.335 34.087,-11.025c-1.711,-11.477 -3.422,-22.955 -5.133,-34.433c-11.944,-0.217 -23.889,-0.483 -35.835,-0.601c-1.755,-0.018 -2.515,-0.564 -3.175,-2.148c-3.853,-9.246 -8.946,-17.777 -15.372,-25.469c-1.064,-1.275 -1.028,-2.194 -0.319,-3.557c4.053,-7.796 8.025,-15.636 12.022,-23.461c1.437,-2.811 2.865,-5.627 4.147,-8.142c-9.436,-7.014 -18.649,-13.862 -28.002,-20.815c-0.579,0.541 -1.31,1.204 -2.02,1.891c-7.885,7.613 -15.779,15.218 -23.635,22.863c-0.996,0.971 -1.77,1.385 -3.257,0.754c-9.397,-3.988 -19.219,-6.352 -29.362,-7.487c-0.883,-0.099 -2.121,-1.037 -2.406,-1.858c-2.193,-6.327 -4.184,-12.724 -6.255,-19.094c-1.667,-5.124 -3.357,-10.24 -5.028,-15.329c-11.618,1.733 -22.897,3.413 -34.277,5.111c-0.075,0.859 -0.19,1.653 -0.205,2.449c-0.201,11.133 -0.354,22.266 -0.638,33.396c-0.025,0.949 -0.665,2.418 -1.405,2.73c-10.087,4.248 -19.287,9.92 -27.698,16.982c-11.362,-5.786 -22.634,-11.527 -33.441,-17.03c-7.08,9.5 -13.934,18.696 -20.904,28.049c8.582,8.89 17.317,17.941 26.124,27.065c-4.342,10.194 -7.114,20.737 -8.177,31.725c-0.062,0.647 -0.945,1.514 -1.629,1.753c-4.187,1.462 -8.429,2.764 -12.648,4.134c-7.303,2.371 -14.603,4.752 -21.957,7.147c1.722,11.548 3.415,22.91 5.115,34.311c0.902,0.073 1.707,0.181 2.514,0.195c11.042,0.2 22.085,0.448 33.128,0.516c1.9,0.011 2.644,0.665 3.336,2.315c3.798,9.068 8.746,17.479 15.085,25.01c1.279,1.521 1.206,2.615 0.355,4.252c-5.02,9.641 -9.934,19.339 -14.876,29.021c-0.436,0.853 -0.821,1.734 -1.118,2.366c9.432,7.007 18.586,13.808 27.921,20.742c0.623,-0.583 1.351,-1.243 2.057,-1.925c7.887,-7.612 15.787,-15.211 23.635,-22.864c1.091,-1.062 1.947,-1.393 3.492,-0.745c9.323,3.913 19.048,6.337 29.113,7.321c1.462,0.142 2.174,0.624 2.643,2.105c3.638,11.476 7.388,22.916 11.103,34.368" fill-rule="nonzero" fill="#2f5597"/>
     </g>
     <g id="g30">
      <path id="path32" d="m392.02829,523.69409c-10.462,-22.968 -0.179,-50.249 22.922,-60.814c23.196,-10.609 50.577,-0.516 61.098,22.521c10.526,23.047 0.236,50.347 -22.967,60.928c-23.193,10.577 -50.544,0.436 -61.053,-22.635m69.623,69.152c7,-3.195 14,-6.39 21,-9.585c-2.05,-7.661 -4.075,-15.328 -6.177,-22.974c-0.269,-0.984 -0.059,-1.506 0.706,-2.114c5.29,-4.211 9.822,-9.129 13.727,-14.64c0.329,-0.464 1.266,-0.823 1.843,-0.736c2.723,0.41 5.42,0.996 8.127,1.511c5.113,0.973 10.226,1.941 15.217,2.887c2.732,-7.238 5.411,-14.336 8.137,-21.564c-0.46,-0.271 -1.016,-0.609 -1.58,-0.931c-6.28,-3.59 -12.554,-7.193 -18.855,-10.747c-0.895,-0.505 -1.212,-1.017 -1.081,-2.088c0.803,-6.564 0.58,-13.109 -0.613,-19.6c-0.205,-1.122 0.045,-1.693 1.006,-2.33c6.599,-4.375 13.147,-8.827 19.711,-13.254c-3.175,-6.956 -6.35,-13.911 -9.524,-20.867c-7.648,2.059 -15.304,4.088 -22.935,6.21c-1.121,0.312 -1.704,0.106 -2.413,-0.776c-4.134,-5.148 -8.927,-9.615 -14.417,-13.306c-0.909,-0.612 -1.054,-1.2 -0.85,-2.194c1.166,-5.684 2.273,-11.38 3.398,-17.073c0.403,-2.045 0.803,-4.091 1.161,-5.92c-7.283,-2.707 -14.397,-5.35 -21.616,-8.034c-0.271,0.449 -0.616,1.004 -0.944,1.569c-3.639,6.273 -7.286,12.542 -10.901,18.83c-0.459,0.797 -0.877,1.202 -1.938,1.076c-6.71,-0.798 -13.396,-0.488 -20.063,0.658c-0.581,0.1 -1.54,-0.266 -1.87,-0.734c-2.546,-3.605 -4.976,-7.291 -7.452,-10.945c-1.992,-2.939 -3.999,-5.868 -5.987,-8.785c-7.086,3.235 -13.965,6.374 -20.906,9.542c0.109,0.558 0.18,1.082 0.316,1.589c1.893,7.088 3.817,14.17 5.658,21.272c0.157,0.605 0.016,1.654 -0.399,1.987c-5.653,4.547 -10.483,9.831 -14.558,15.852c-8.288,-1.576 -16.51,-3.139 -24.394,-4.637c-2.784,7.32 -5.48,14.406 -8.221,21.612c7.082,4.053 14.29,8.18 21.556,12.339c-0.913,7.256 -0.764,14.444 0.554,21.601c0.078,0.421 -0.326,1.132 -0.719,1.409c-2.401,1.696 -4.867,3.301 -7.306,4.945c-4.221,2.845 -8.438,5.696 -12.688,8.565c3.195,6.999 6.337,13.884 9.491,20.794c0.588,-0.12 1.12,-0.199 1.637,-0.338c7.07,-1.905 14.148,-3.779 21.195,-5.768c1.212,-0.341 1.805,-0.064 2.545,0.853c4.067,5.046 8.746,9.464 14.152,13.068c1.091,0.728 1.243,1.435 0.998,2.628c-1.447,7.031 -2.815,14.077 -4.205,21.119c-0.122,0.621 -0.207,1.249 -0.282,1.705c7.281,2.704 14.347,5.328 21.553,8.003c0.291,-0.484 0.634,-1.036 0.96,-1.597c3.642,-6.273 7.293,-12.539 10.902,-18.83c0.501,-0.874 0.988,-1.242 2.089,-1.115c6.65,0.764 13.284,0.511 19.874,-0.717c0.957,-0.179 1.498,-0.005 2.066,0.847c4.402,6.6 8.868,13.157 13.315,19.728" fill-rule="nonzero" fill="#fcb416"/>
     </g>
     <g id="g34">
      <path id="path36" d="m260.26611,466.3186c-14.412,-16.867 -12.751,-42.062 3.701,-56.164c16.52,-14.159 41.693,-11.923 56.172,4.988c14.485,16.92 12.823,42.132 -3.71,56.27c-16.525,14.131 -41.686,11.849 -56.163,-5.094m74.917,42.122c4.987,-4.268 9.974,-8.535 14.961,-12.802c-3.616,-5.954 -7.214,-11.917 -10.869,-17.847c-0.47,-0.763 -0.427,-1.247 0.053,-1.932c3.318,-4.73 5.832,-9.88 7.679,-15.385c0.155,-0.463 0.84,-0.977 1.339,-1.035c2.355,-0.276 4.732,-0.397 7.099,-0.58c4.472,-0.347 8.944,-0.698 13.308,-1.039c0.443,-6.686 0.878,-13.241 1.32,-19.917c-0.449,-0.123 -0.993,-0.279 -1.54,-0.421c-6.094,-1.581 -12.185,-3.175 -18.288,-4.721c-0.866,-0.219 -1.257,-0.577 -1.417,-1.504c-0.982,-5.682 -2.809,-11.115 -5.422,-16.284c-0.452,-0.892 -0.389,-1.427 0.247,-2.18c4.359,-5.165 8.656,-10.383 12.974,-15.584c-4.37,-5.106 -8.739,-10.213 -13.109,-15.32c-5.808,3.463 -11.63,6.903 -17.408,10.414c-0.848,0.516 -1.383,0.476 -2.19,-0.101c-4.709,-3.375 -9.793,-6.029 -15.259,-7.874c-0.906,-0.305 -1.172,-0.766 -1.253,-1.645c-0.461,-5.028 -0.975,-10.052 -1.473,-15.077c-0.179,-1.805 -0.362,-3.61 -0.524,-5.224c-6.703,-0.613 -13.247,-1.212 -19.891,-1.82c-0.111,0.437 -0.257,0.981 -0.386,1.528c-1.436,6.083 -2.879,12.165 -4.292,18.254c-0.179,0.773 -0.423,1.207 -1.333,1.343c-5.748,0.856 -11.199,2.634 -16.425,5.11c-0.455,0.215 -1.339,0.126 -1.73,-0.191c-3.01,-2.441 -5.943,-4.977 -8.908,-7.476c-2.384,-2.011 -4.778,-4.009 -7.154,-6.001c-5.048,4.319 -9.949,8.513 -14.894,12.745c0.23,0.443 0.42,0.865 0.66,1.259c3.344,5.51 6.711,11.005 10.014,16.538c0.282,0.471 0.428,1.382 0.169,1.756c-3.535,5.094 -6.203,10.619 -8.062,16.59c-7.249,0.563 -14.441,1.121 -21.335,1.657c-0.467,6.766 -0.918,13.316 -1.378,19.977c6.873,1.787 13.868,3.606 20.921,5.44c1.064,6.288 2.99,12.276 5.876,17.973c0.17,0.335 0.013,1.023 -0.242,1.345c-1.56,1.966 -3.196,3.871 -4.8,5.803c-2.778,3.342 -5.55,6.689 -8.344,10.06c4.397,5.138 8.722,10.193 13.063,15.266c0.456,-0.234 0.877,-0.422 1.269,-0.656c5.368,-3.202 10.752,-6.381 16.08,-9.647c0.916,-0.563 1.476,-0.465 2.318,0.136c4.629,3.303 9.606,5.942 14.98,7.734c1.085,0.361 1.388,0.92 1.485,1.975c0.567,6.219 1.203,12.434 1.82,18.65c0.055,0.548 0.142,1.094 0.195,1.493c6.698,0.611 13.2,1.204 19.829,1.808c0.12,-0.471 0.266,-1.012 0.394,-1.556c1.437,-6.083 2.886,-12.164 4.292,-18.255c0.195,-0.846 0.505,-1.264 1.448,-1.408c5.69,-0.87 11.112,-2.591 16.254,-5.117c0.747,-0.367 1.237,-0.344 1.92,0.241c5.296,4.529 10.634,9.009 15.959,13.504" fill-rule="nonzero" fill="#4b8624"/>
     </g>
     <g id="g38">
      <path id="path40" d="m265.01321,323.81369c-6.828,-15.479 1.035,-34.016 17.53,-41.323c16.561,-7.337 35.589,-0.686 42.457,14.84c6.872,15.533 -0.997,34.083 -17.564,41.4c-16.558,7.315 -35.564,0.632 -42.423,-14.917m47.376,46.352l14.995,-6.629c-1.265,-5.172 -2.513,-10.349 -3.814,-15.512c-0.167,-0.664 -0.007,-1.019 0.545,-1.436c3.819,-2.886 7.12,-6.246 9.994,-10.002c0.242,-0.316 0.909,-0.565 1.313,-0.511c1.907,0.259 3.79,0.638 5.682,0.969c3.574,0.625 7.149,1.246 10.638,1.853c2.089,-4.918 4.137,-9.741 6.222,-14.652c-0.317,-0.181 -0.701,-0.406 -1.09,-0.62c-4.335,-2.389 -8.664,-4.787 -13.015,-7.15c-0.618,-0.336 -0.829,-0.681 -0.712,-1.407c0.717,-4.448 0.71,-8.878 0.022,-13.265c-0.118,-0.758 0.071,-1.145 0.761,-1.584c4.743,-3.006 9.452,-6.063 14.171,-9.104l-6.216,-14.062c-5.428,1.444 -10.86,2.869 -16.277,4.356c-0.796,0.219 -1.201,0.083 -1.679,-0.51c-2.789,-3.457 -6.058,-6.449 -9.834,-8.912c-0.626,-0.408 -0.714,-0.805 -0.547,-1.479c0.951,-3.856 1.861,-7.72 2.784,-11.58c0.331,-1.388 0.659,-2.776 0.954,-4.016c-5.061,-1.784 -10.003,-3.527 -15.021,-5.296c-0.2,0.306 -0.456,0.684 -0.699,1.069c-2.705,4.271 -5.415,8.54 -8.104,12.82c-0.341,0.543 -0.644,0.82 -1.388,0.741c-4.701,-0.495 -9.411,-0.241 -14.127,0.579c-0.412,0.071 -1.078,-0.17 -1.299,-0.484c-1.708,-2.423 -3.332,-4.903 -4.989,-7.36c-1.334,-1.976 -2.678,-3.947 -4.009,-5.908c-5.059,2.238 -9.97,4.408 -14.926,6.599c0.064,0.378 0.101,0.731 0.185,1.074c1.168,4.786 2.358,9.567 3.489,14.363c0.096,0.409 -0.027,1.12 -0.326,1.348c-4.082,3.115 -7.602,6.726 -10.607,10.828c-5.794,-1.011 -11.542,-2.014 -17.053,-2.976c-2.128,4.974 -4.188,9.789 -6.282,14.685c4.887,2.697 9.862,5.443 14.878,8.211c-0.81,4.918 -0.871,9.783 -0.109,14.619c0.045,0.284 -0.257,0.769 -0.539,0.959c-1.728,1.164 -3.5,2.267 -5.253,3.396c-3.035,1.954 -6.068,3.912 -9.123,5.884c2.085,4.716 4.136,9.356 6.195,14.013c0.417,-0.085 0.792,-0.142 1.159,-0.24c5.017,-1.336 10.04,-2.652 15.043,-4.045c0.861,-0.24 1.271,-0.056 1.77,0.561c2.744,3.388 5.934,6.348 9.653,8.752c0.751,0.485 0.842,0.963 0.642,1.772c-1.181,4.77 -2.306,9.549 -3.446,14.326c-0.101,0.421 -0.175,0.847 -0.238,1.156c5.059,1.782 9.969,3.511 14.976,5.274c0.216,-0.329 0.47,-0.705 0.713,-1.088c2.706,-4.271 5.419,-8.537 8.104,-12.82c0.372,-0.595 0.723,-0.847 1.495,-0.768c4.659,0.473 9.332,0.257 13.996,-0.618c0.677,-0.128 1.053,-0.013 1.433,0.559c2.944,4.439 5.935,8.849 8.91,13.268" fill-rule="nonzero" fill="#00b0f0"/>
     </g>
    </g>
   </g>
   <text id="text44" transform="matrix(1,0,0,-1,229.4854,46.8672) " fill-rule="nonzero" fill="#2f5597" font-family="'NewsGoth BT'" font-size="147.694702px" font-weight="normal" y="0.75" x="0.75">
    <tspan id="tspan42" y="0" x="0 88.912209 186.39072 270.7244 368.79367 457.70587">AGEOPS</tspan>
   </text>
   <g id="g46">
    <g clip-path="url(#clipPath52)" id="g48">
     <g id="g54">
      <path id="path56" d="m520.24121,-1.0305c1.113,6.724 2.128,12.965 3.185,19.199c2.785,16.432 5.691,32.845 8.319,49.301c0.723,4.528 2.793,6.861 7.292,8.073c18.588,5.008 36.282,12.262 52.732,22.341c2.926,1.793 5.229,1.617 7.966,-0.355c18.216,-13.132 36.509,-26.158 54.782,-39.21c1.001,-0.715 2.052,-1.361 3.152,-2.085c0.538,0.434 1.047,0.773 1.47,1.198c15.686,15.711 31.345,31.45 47.078,47.114c1.426,1.421 1.193,2.334 0.16,3.774c-13.052,18.164 -25.955,36.435 -39.142,54.501c-2.764,3.787 -2.919,6.693 -0.465,10.705c9.923,16.225 16.938,33.716 21.639,52.138c1.22,4.781 2.175,5.547 7.145,6.367c22.449,3.701 44.887,7.468 67.336,11.166c1.959,0.323 2.808,1.054 2.8,3.224c-0.078,21.76 -0.078,43.521 0,65.28c0.008,2.163 -0.824,2.911 -2.79,3.233c-22.715,3.721 -45.414,7.528 -68.135,11.208c-3.407,0.553 -5.348,2.172 -6.181,5.534c-4.814,19.417 -12.389,37.701 -22.834,54.77c-1.693,2.764 -1.202,5.015 0.598,7.517c13.325,18.514 26.546,37.1 39.87,55.614c1.307,1.818 1.397,2.919 -0.324,4.622c-15.4,15.249 -30.716,30.582 -45.982,45.962c-1.538,1.549 -2.498,1.757 -4.369,0.414c-18.24,-13.097 -36.59,-26.041 -54.839,-39.125c-3.377,-2.421 -6.339,-2.488 -9.969,-0.386c-16.657,9.648 -34.288,17 -52.974,21.746c-4.481,1.139 -5.199,2.07 -5.941,6.586c-3.615,22.015 -7.287,44.02 -10.932,66.03c-0.201,1.211 -0.331,2.434 -0.52,3.847l261.681,0l0,-2.908c0,-184.697 -0.008,-369.394 0.069,-554.091c0.001,-2.802 -0.844,-3.354 -3.419,-3.352c-83.236,0.058 -166.474,0.048 -249.71,0.048l-8.748,0z" fill-rule="nonzero" fill="#ffffff"/>
     </g>
    </g>
   </g>
  </g>
 </g>
</svg>