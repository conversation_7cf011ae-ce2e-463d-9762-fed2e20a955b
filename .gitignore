/.vscode
/public

/node_modules
/storage/*.key
/vendor
/.idea
/.vagrant
Homestead.json
Homestead.yaml
npm-debug.log
yarn-error.log
.env

# See http://help.github.com/ignore-files/ for more about ignoring files.

# compiled output
/dist
/tmp
/out-tsc
/environments
/gulp-tsc-temp*

# dependencies
/node_modules

# IDEs and editors
/.idea
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace

# IDE - VSCode
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# misc
/.sass-cache
/connect.lock
/coverage
/libpeerconnection.log
npm-debug.log
testem.log
/typings

# e2e
/e2e/*.js
/e2e/*.map

# System Files
.DS_Store
Thumbs.db
environment

.gulp-cache
/server
/server

keys/access-private.key
keys/refresh-private.key
keys/access-public.pem
keys/refresh-public.pem
keys/recover-password-private.key
keys/recover-password-public.pem

src/environments/environment.ts
src/environments/environment.prod.ts
resources/views/index.blade.php

laravel-scheduler.xml


**/nohup.out
