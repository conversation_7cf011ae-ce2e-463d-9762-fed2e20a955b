import {Injectable} from '@angular/core';
import {AbstractControl, ValidatorFn} from '@angular/forms';
import * as moment from 'moment';

@Injectable()

export class CustomValidationService {

    public errors = {
        range: {
            message: (start: Date, end: Date, message = '') => {
                if (message !== '') {
                    return message;
                }
                message = ' تاریخ داخل شده باید کوچکتر از ' + this.getString(end) + ' و بزرگتر از ' + this.getString(start) + ' باشد. ';
                return message;

            }
        }
    };

    getString(date: Date): string {
        return  date.getDate() + '-' + (date.getMonth() + 1) + '-' + date.getFullYear();
    }

    public dateRange(min: Date, max: Date): ValidatorFn {

        return (c: AbstractControl): { [key: string]: boolean } | null => {
            const momentDate = <any> c.value instanceof moment ? c.value : c.value instanceof Date ? moment(c.value) : undefined;
            if (momentDate !== undefined) {
                if (momentDate.isValid() && !momentDate.isBetween(moment(min), moment(max))) {
                    return {'range': true};
                }
            }
            return null;
        };
    }


}
