<mat-card [ngClass]="{'rotate' : isFlipped}" class="card">
    <button class="download" mat-icon-button [matMenuTriggerFor]="downloadMenu" *ngIf="!isFlipped">
        <mat-icon>file_download</mat-icon>
    </button>
    <mat-menu #downloadMenu="matMenu">
        <div class="menu">
            <button mat-menu-item (click)="downloadPng()">
                {{'DOWNLOAD_IN_FORMAT_OF_PNG'|translate}}
            </button>
            <button mat-menu-item (click)="downloadCsv()">
                {{'DOWNLOAD_IN_FORMAT_OF_CSV'|translate}}

            </button>
        </div>
    </mat-menu>
    <div class="action">
        <a mat-icon-button class="flip" *ngIf="!isFlipped" (click)="flip()">
            <mat-icon>more_vert</mat-icon>
        </a>
        <a mat-icon-button class="close" *ngIf="isFlipped" (click)="flip()">
            <mat-icon>close</mat-icon>
        </a>
    </div>

    <div class="front">
        <ng-content select="[front]">

        </ng-content>
    </div>
    <div class="back">
        <ng-content select="[back]">

        </ng-content>
    </div>
</mat-card>
