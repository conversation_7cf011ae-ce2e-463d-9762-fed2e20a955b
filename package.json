{"name": "usm", "version": "1.0.0", "license": "Proprietary", "scripts": {"ng": "ng", "start": "ng serve", "server": "gulp", "server:prod": "node ./server/index.js", "migration": "./node_modules/.bin/ts-node ./node_modules/typeorm/cli.js migration:run", "migration:create": "./node_modules/.bin/ts-node ./node_modules/typeorm/cli.js migration:create -n", "migration:rollback": "./node_modules/.bin/ts-node ./node_modules/typeorm/cli.js migration:revert", "build": "ng build", "test": "karma start ./karma.conf.js", "lint": "ng lint", "e2e": "protractor ./protractor.conf.js", "pree2e": "webdriver-manager update --standalone false --gecko false --quiet"}, "private": true, "dependencies": {"@angular/animations": "7.2.0", "@angular/cdk": "7.3.3", "@angular/common": "7.2.0", "@angular/compiler": "7.2.0", "@angular/core": "7.2.0", "@angular/flex-layout": "7.0.0-beta.23", "@angular/forms": "7.2.0", "@angular/http": "7.2.7", "@angular/material": "7.3.3", "@angular/material-moment-adapter": "7.3.3", "@angular/platform-browser": "7.2.0", "@angular/platform-browser-dynamic": "7.2.0", "@angular/router": "7.2.0", "@ngx-translate/core": "11.0.1", "@ngx-translate/http-loader": "4.0.0", "aws-sdk": "2.437.0", "bcrypt": "5.1.0", "body-parser": "1.18.3", "core-js": "2.5.4", "cors": "2.8.5", "dotenv": "6.2.0", "echarts": "4.1.0", "express": "4.16.4", "express-validator": "5.3.1", "jalali-moment": "3.3.1", "jsonwebtoken": "8.5.0", "keypair": "1.0.1", "material-design-icons": "3.0.1", "moment": "2.24.0", "mysql": "2.16.0", "ngx-echarts": "4.1.0", "nodemailer": "5.1.1", "reflect-metadata": "0.1.13", "rxjs": "6.4.0", "socket.io": "^4.7.2", "tslib": "1.9.0", "typeorm": "0.2.14", "types": "0.1.1", "underscore": "1.9.1", "zone.js": "0.8.26"}, "devDependencies": {"@angular-devkit/build-angular": "0.13.0", "@angular/cli": "7.3.3", "@angular/compiler-cli": "7.2.0", "@angular/language-service": "7.2.0", "@types/dotenv": "6.1.0", "@types/express": "4.16.1", "@types/jasmine": "2.8.8", "@types/jasminewd2": "2.0.3", "@types/node": "8.9.5", "codelyzer": "4.5.0", "gulp": "4.0.0", "gulp-nodemon": "^2.4.2", "gulp-typescript": "4.0.2", "jasmine-core": "2.99.1", "jasmine-spec-reporter": "4.2.1", "karma": "4.0.0", "karma-chrome-launcher": "2.2.0", "karma-coverage-istanbul-reporter": "2.0.1", "karma-jasmine": "1.1.2", "karma-jasmine-html-reporter": "0.2.2", "less-loader": "4.1.0", "protractor": "5.4.0", "sass": "^1.58.0", "ts-node": "7.0.0", "tslint": "5.11.0", "typescript": "3.2.4", "watchify": "3.11.1"}}