<div class="container">
    <mat-card [ngClass]="{'rotate' : isFlipped}" class="card">
        <div class="action">
            <a mat-icon-button class="flip frontcolor" *ngIf="!isFlipped" (click)="isFlipped = !isFlipped">
                <mat-icon>more_vert</mat-icon>
            </a>
            <a mat-icon-button class="close" *ngIf="isFlipped" (click)="isFlipped = !isFlipped">
                <mat-icon>close</mat-icon>
            </a>
        </div>

        <div class="front">
            <div class="title">
                <ng-content select="[title]"></ng-content>
            </div>
            <div class="value">
                <ng-content select="[value]"></ng-content>
            </div>
        </div>
        <div class="back">
            <ng-content select="[back]">

            </ng-content>
        </div>
    </mat-card>
</div>