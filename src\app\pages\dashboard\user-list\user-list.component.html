<mat-expansion-panel>
    <mat-expansion-panel-header>
        <mat-panel-title>{{ "SEARCH" | translate}}</mat-panel-title>
    </mat-expansion-panel-header>

        <mat-form-field fxFlex='25'>
            <input
                    matInput
                    type="text"
                    (input) = "search($event.target.value)"
                    placeholder="{{'EMAIL_ADDRESS'| translate}}"
                    lang="en">
        </mat-form-field>
</mat-expansion-panel>
<mat-card id="ng_test_card">
    <mat-card-header>{{'USER_LIST' |translate}}</mat-card-header>
    <mat-card-content>
        <mat-table #table *ngIf="dataSource" [dataSource]="dataSource">
            <ng-container matColumnDef="email">
                <mat-header-cell *matHeaderCellDef
                                 [class]="userListService.getClass('email')">
                    {{'EMAIL_ADDRESS'| translate}}
                </mat-header-cell>
                <mat-cell *matCellDef="let row"
                          [class]="userListService.getClass('email')">
                    {{row.email}}
                </mat-cell>
            </ng-container>
            <ng-container matColumnDef="phone_number">
              <mat-header-cell *matHeaderCellDef [class]="userListService.getClass('project_name')">
                  {{'PHONE_NUMBER'| translate}}
              </mat-header-cell>
              <mat-cell *matCellDef="let row" [class]="userListService.getClass('project_name')">
                  {{row.phone_number}}
              </mat-cell>
          </ng-container>
            <ng-container matColumnDef="user_name">
                <mat-header-cell *matHeaderCellDef [class]="userListService.getClass('project_name')">
                    {{'USER_NAME'| translate}}
                </mat-header-cell>
                <mat-cell *matCellDef="let row" [class]="userListService.getClass('project_name')">
                    {{row.username}}
                </mat-cell>
            </ng-container>
            <ng-container matColumnDef="options">
                <mat-header-cell *matHeaderCellDef [class]="userListService.getClass('options')">
                    {{'OPTIONS' | translate}}
                </mat-header-cell>
                <mat-cell *matCellDef="let row" [class]="userListService.getClass('options')">
                  <button
                          *ngIf="authService.user.value.role.slug === 'dev-ops'"
                          type='button' mat-icon-button
                          matTooltip="{{'UPDATE_INFORMATION' | translate}}" (click)="openAddEditDialog(row)">
                      <mat-icon>edit</mat-icon>
                  </button>
                  <mat-chip-list *ngIf="row.status_id !== 1">
                    <mat-chip class="inactive">
                      <span>Inactive</span>
                    </mat-chip>
                  </mat-chip-list>

                </mat-cell>
            </ng-container>
            <mat-header-row *matHeaderRowDef="chosenTitles" [style.width]="getWidth()"></mat-header-row>
            <mat-row [ngClass]="row?.withdrawal_reason ? 'red-background' : ''"
                     *matRowDef="let row; columns: chosenTitles" [style.width]="getWidth()"></mat-row>
        </mat-table>
        <div class="pagination-container">
            <mat-paginator [dir]="dir"></mat-paginator>
        </div>
    </mat-card-content>
    <mat-card-footer fxLayout="row wrap" fxLayoutAlign="end">
        <button
                *ngIf="authService.user.value.role.slug === 'dev-ops'"
                mat-fab class="add-row-button"
                type="button" color="primary"
                (click)="openAddEditDialog()"
                matTooltip='{{"ADD_ROW" | translate}}'>
            <mat-icon>add</mat-icon>
        </button>
    </mat-card-footer>
</mat-card>

