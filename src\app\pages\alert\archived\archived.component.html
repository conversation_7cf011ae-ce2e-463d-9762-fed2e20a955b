<mat-card>
    <mat-table [dataSource]="dataSource">
        <mat-header-row *matHeaderRowDef="displayedColumns" fxLayout="row wrap" fxLayoutAlign="start"></mat-header-row>
        <mat-row *matRowDef="let row; columns: displayedColumns;"></mat-row>

        <ng-container matColumnDef="id">
            <mat-header-cell *matHeaderCellDef fxFlex="5"> آی دی</mat-header-cell>
            <mat-cell *matCellDef="let row" fxFlex="5"> {{row.id}}</mat-cell>
        </ng-container>

        <ng-container matColumnDef="generation_date">
            <mat-header-cell *matHeaderCellDef fxFlex="15">تاریخ ایجاد</mat-header-cell>
            <mat-cell *matCellDef="let row" fxFlex="15"> {{row.generation_date | date:'yyyy-MM-dd'}}</mat-cell>
        </ng-container>

        <ng-container matColumnDef="type">
            <mat-header-cell *matHeaderCellDef fxFlex="15"> نوع هشدار</mat-header-cell>
            <mat-cell *matCellDef="let row" fxFlex="15"> {{row.type_name_da}}</mat-cell>
        </ng-container>

        <ng-container matColumnDef="title">
            <mat-header-cell *matHeaderCellDef fxFlex="45">عنوان</mat-header-cell>
            <mat-cell *matCellDef="let row" fxFlex="45"> {{row.title_da}}</mat-cell>
        </ng-container>

        <ng-container matColumnDef="options">
            <mat-header-cell *matHeaderCellDef fxFlex="20">اخیتارات</mat-header-cell>
            <mat-cell *matCellDef="let row" fxFlex="20">
                <div fxFlex="100">
                    <button fxFlex="20" type='button'
                            mat-icon-button (click)="openViewDialog(row.id)"
                            matTooltip="نمایش">
                        <mat-icon>visibility</mat-icon>
                    </button>
                </div>
            </mat-cell>
        </ng-container>
    </mat-table>
</mat-card>
