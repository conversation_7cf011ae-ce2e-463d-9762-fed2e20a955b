import {MigrationInterface, QueryRunner} from 'typeorm';

export class AddCDMContextAccesses1566466639079 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<any> {
        await queryRunner.query(`
            insert into
            context_accesses (operation_id, is_enabled, role_id, context_id)
            values (1, 1, (select id from roles where slug = "common-data-manager"), (select id from contexts where slug = "common")),
            (2, 1, (select id from roles where slug = "common-data-manager"), (select id from contexts where slug = "common")),
            (3, 1, (select id from roles where slug = "common-data-manager"), (select id from contexts where slug = "common")),
            (11, 1, (select id from roles where slug = "common-data-manager"), (select id from contexts where slug = "common")),
            (12, 1, (select id from roles where slug = "common-data-manager"), (select id from contexts where slug = "common"))
            `);
    }

    public async down(queryRunner: QueryRunner): Promise<any> {
        await queryRunner.query(`Delete from context_accesses where role_id = 
(select id from roles where slug = "common-data-manager")`);
    }

}
