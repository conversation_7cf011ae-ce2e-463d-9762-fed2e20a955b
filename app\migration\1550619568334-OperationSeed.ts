import {MigrationInterface, QueryRunner} from 'typeorm';

export class OperationSeed1550619568334 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<any> {
        await queryRunner.query(`
            INSERT INTO operations (id, slug, name, description)
            VALUES
                (1, 'view', '', ''),
                (2, 'delete', '', ''),
                (3, 'list', '', ''),
                (4, 'remark', '', ''),
                (5, 'approve', '', ''),
                (6, 'confirm', '', ''),
                (7, 'publish', '', ''),
                (8, 'generally-approve', '', ''),
                (9, 'generally-confirm', '', ''),
                (10, 'generally-publish', '', ''),
                (11, 'create', '', ''),
                (12, 'update', '', ''),
                (13, 'cancel-request', '', ''),
                (14, 'cancel-request-response', '', ''),
                (15, 'renew-request', '', ''),
                (16, 'renew-request-response', '', ''),
                (17, 'transfer-request', '', ''),
                (18, 'transfer-request-response', '', ''),
                (19, 'list-procurement-plan-managers', '', ''),
                (20, 'list-specialists', '', ''),
                (21, 'assign-specialist', '', '')
              `);

    }

    public async down(queryRunner: QueryRunner): Promise<any> {
        await queryRunner.query(`Delete from operstions`);
    }

}
