import {MigrationInterface, QueryRunner} from 'typeorm';

export class AddObserverRole1555739269461 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<any> {
        try {
            await queryRunner.query(`
                INSERT INTO
                roles (name, slug, party, can_be_acted, can_act, is_pe_based, is_sector_based, is_vendor_based, is_record_based, system_id)
                VALUES
                    ('observer','cpms-observer', 'npa', true, false, true, true, false, true, 2)
                    `);

        } catch (e) {
            throw e;
        }
    }

    public async down(queryRunner: QueryRunner): Promise<any> {
        await queryRunner.query(`Delete from roles where slug = 'cpms-observer' `);
    }

}
