import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { TitleValueWidgetComponent } from './title-value-widget.component';

describe('TitleValueWidgetComponent', () => {
  let component: TitleValueWidgetComponent;
  let fixture: ComponentFixture<TitleValueWidgetComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ TitleValueWidgetComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(TitleValueWidgetComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
