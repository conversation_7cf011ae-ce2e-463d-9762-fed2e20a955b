import {Routes} from '@angular/router';
import {UserGuideComponent} from './user-guide.component';
import {SystemInformationComponent} from './system-information/system-information.component';
import {ChangeLogComponent} from './change-log/change-log.component';
import {ContactInformationComponent} from './contact-information/contact-information.component';
import {UserManualComponent} from './user-manual/user-manual.component';
import {ResetSystemComponent} from './reset-system/reset-system.component';

export const USER_GUIDE_ROUTES: Routes = [
    {path: '', redirectTo: 'user-guide', pathMatch: 'full'},
    {path: '', component: UserGuideComponent},
    {path: 'system-information', component: SystemInformationComponent},
    {path: 'change-log', component: ChangeLogComponent},
    {path: 'contact-information', component: ContactInformationComponent},
    {path: 'user-manual', component: UserManualComponent},
    {path: 'reset-system', component: ResetSystemComponent},
];
