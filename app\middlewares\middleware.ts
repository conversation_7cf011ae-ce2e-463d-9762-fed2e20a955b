import * as bodyParser from 'body-parser';
import {Router} from 'express';
import * as CORS from 'cors';
import {validationResult} from 'express-validator/check';
import {IRoute} from '../app.routes';
import {HeadersMiddleware} from './headers';
import * as env from 'dotenv';
import * as path from 'path';


env.load({path: path.resolve(__dirname, '../../.env')});
const WHITE_LIST = [
    removeTrailingCharIfMatched(process.env.CUSTOM_FRONT_END_BASE_URL, '/'),
    process.env.CUSTOM_APPMS_BASE_URL,
    process.env.CUSTOM_ACPMS_BASE_URL,
    process.env.CUSTOM_AVRCS_BASE_URL,
];
const CORS_OPTIONS = {
    origin: (origin, callback) => {
        if (WHITE_LIST.indexOf(origin) !== -1) {
            callback(null, true);
        } else {
            callback(new Error('Not allowed by CORS'));
        }
    }
};

export class Middleware {
    router: Router = Router();
    default = [
        process.env.APP_ENV === 'production' ? CORS(CORS_OPTIONS) : CORS(),
        bodyParser.urlencoded({extended: false}),
        bodyParser.json(),
        this.router.use((req, res, next) => {
            HeadersMiddleware.accessControl(req, res, next);
        })
    ];


    public static directTo(router: Router, routes: Array<any>): Router {
        routes.forEach((route: IRoute) => {
            router[route.httpMethod](
                route.url,
                route.validatorsList && !!route.validatorsList.length ? [route.validatorsList, (req, res, next) => {
                    const errors = validationResult(req);
                    if (!errors.isEmpty()) {
                        return res.status(400).json(errors.mapped());
                    }
                    next();
                }] : [],
                [
                    (req, res, next) => {
                        if (route.middleware) {
                            route.middleware.forEach(async (item) => {
                                try {
                                    await item(req, res, next);
                                } catch (e) {
                                    res.send(e);
                                }
                            });
                        } else {
                            next();
                        }
                    }
                ],
                (req, res) => {
                    route.controllerMethod(req, res);
                });
        });
        return router;
    }


}

export const DefaultMiddleware = (new Middleware()).default;

function removeTrailingCharIfMatched(str: string, char: string): string {
    if (str && str.substr(-1) === char) {
        return str.slice(0, -1);
    }
    return str;
}
