import {Component, Input, OnInit} from '@angular/core';
import {TranslateService} from '../../shared/services/translate.service';


@Component({
    selector: 'npa-sidebar-main',
    templateUrl: './sidebar-main.component.html',
    styleUrls: ['./sidebar-main.component.styl']
})

export class SidebarMainComponent implements OnInit {

    @Input() isSidebarMainClose;
    lang: string;


    constructor(private translate: TranslateService) {
    }

    ngOnInit() {
        this.lang = this.translate.lang;
    }

}
