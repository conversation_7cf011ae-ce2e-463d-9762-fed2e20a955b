@import '.././../../../styles/definitions.styl'
.header
  cursor default
  position absolute
  top 0
  left 0
  right 0
  background-color #fff
  height sizeHeaderHeight
  display flex
  align-items center
  justify-content space-between
  z-index 4
  box-shadow 0 2px 1px -1px rgba(0,0,0,.2), 0 1px 1px 0 rgba(0,0,0,.14), 0 1px 3px 0 rgba(0,0,0,.12)
  .sidebar-header-main
    color colorSidebarLight
    background-color colorDarkSidebarHeader
    width sizeSidebarMainWidthOpen - 24 - 16
    height sizeHeaderHeight
    display flex
    align-items center
    justify-content space-between
    transition all .2s linear
    padding-right 24px
    padding-left 16px
    box-shadow 0 2px 1px -1px rgba(0,0,0,.2), 0 1px 1px 0 rgba(0,0,0,.14), 0 1px 3px 0 rgba(0,0,0,.12)
    .logo
      padding 5px
      height sizeHeaderIconWidth
      text-align center
      width sizeHeaderIconWidth
      cursor pointer
      font-weight bold
      border 1px solid colorSidebarLight
      border-radius 50%
    .app-title
      font-size 24px
      width 110px
      text-align right
      font-weight 300
      font-family Roboto,Helvetica Neue Light,Helvetica Neue,Helvetica,Arial,Lucida Grande, sans-serif
    .sidebar-toggle-icon
      cursor pointer

  .sidebar-header-main.close
    width sizeSidebarMainWidthClose
    overflow hidden
    padding-right 0
    padding-left 0
    display flex
    align-items center
    justify-content center
    .app-title, .sidebar-toggle-button
      width 0
      overflow hidden

  .header-remaining-items
    display flex
    align-items center
    justify-content flex-end
    margin-left 10px
    height sizeHeaderHeight

    .role
      font-size 14px

    .sidebar-toggle-icon-hidden
      width 0px
      overflow hidden
    .sidebar-toggle-icon:hover
      cursor pointer
      color gray
  .header-remaining-items.open .sidebar-toggle-button .to-open,
  .header-remaining-items.close .sidebar-toggle-button .to-close
    width 0
    overflow hidden

  .modules-sidebar-header-close
    .sidebar-toggle-icon
      width 0px
      overflow hidden
    .sidebar-toggle-icon-hidden
      width 20px
      overflow !hidden
    .sidebar-toggle-icon-hidden:hover
      cursor pointer
      color gray
  .new-alerts-marker
    display none
    border 6px solid colorWarn
    height 0
    width 0
    border-radius 50%
    box-shadow 0 0 2px colorWarn
    position absolute
    right 2px
    top 4px
  .new-alerts-marker.active
    display block
.menu
  mat-icon
    margin-right 0px !important
    margin-left 10px