<div fxLayout="row wrap">
    <div fxFlex="100" fxLayoutAlign="end">
        <button mat-dialog-close mat-icon-button matTooltip="{{'CLOSE' | translate}}">
            <mat-icon>close</mat-icon>
        </button>
    </div>
</div>
<mat-dialog-content class="dialog" fxLayout="row wrap" fxLayoutAlign="start">
    <div fxLayout="row wrap" fxLayoutAlign="start" fxFlex="100">
      <mat-form-field fxFlex="100">
        <input matInput placeholder='{{"SEARCH" | translate}}' (input)="search($event.target.value)">
      </mat-form-field>
    </div>
    <div fxLayout="row wrap" fxLayoutAlign="start" fxFlex="100">
      <div *ngFor="let entity of searched" fxFlex="50" fxLayout="row wrap">
        <mat-checkbox [checked]="isSelected(entity)" [value]="entity" (change)="select($event.checked, $event.source.value)" >{{ entity?.name_da }}</mat-checkbox>
      </div>
    </div>

</mat-dialog-content>
<mat-dialog-actions fxLayoutAlign="start">
    <button mat-raised-button color="primary"
            (click)="add()" [disabled]="selected.length == 0">{{'ADD'|translate}}
    </button>
    <button mat-button type="button" [mat-dialog-close]="true">{{'CANCEL'|translate}}</button>
</mat-dialog-actions>
