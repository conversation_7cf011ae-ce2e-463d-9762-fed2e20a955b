<mat-card *ngIf="!isGeneralConfirmation && style === verificationStyles.SEPARATE_CARD">
    <mat-card-header>{{"CONFIRMATION" | translate}}</mat-card-header>
    <mat-card-content>
        <hr>
        <mat-progress-bar mode="indeterminate" *ngIf="!hasError"></mat-progress-bar>
        <div fxLayout="row wrap" fxLayoutAlign="start">
            <npa-view-element fxFlex="20">
                <div class="npa-label">{{ "SECRETARY_CONFIRMATION" | translate }}:</div>
                <div class="npa-value">{{"NO" | translate}}</div>
            </npa-view-element>
            <npa-view-element fxFlex="20">
                <div class="npa-label">{{"SECRETARY_CONFIRMATION_DATE" | translate}}:</div>
                <div class="npa-value">
                    <div class="npa-number">
                    </div>
                </div>
            </npa-view-element>

            <npa-view-element fxFlex="30">
                <div class="npa-label">{{ "DIRECTORATE_APPROVAL" | translate }}:</div>
                <div class="npa-value">{{ "NO" | translate}}</div>
            </npa-view-element>
            <npa-view-element fxFlex="30">
                <div class="npa-label">{{ "DIRECTORATE_APPROVAL_DATE" | translate}}:</div>
                <div class="npa-value">
                    <div class="npa-number">
                    </div>
                </div>
            </npa-view-element>
        </div>
        <button mat-raised-button
                *ngIf="!hasError && authService.user.value.role.slug === 'secretary'"
                [disabled]="isProcessing">
            <mat-icon>undo</mat-icon>
            {{"SECRETARY_UNCONFIRMATION" | translate}}
        </button>

        <button mat-raised-button
                *ngIf="authService.user.value.role.slug === 'secretary'"
                (click)="showConfirmationDialog()"
                [disabled]="isProcessing">
            <mat-icon>check_circle</mat-icon>
            {{ "SECRETARY_CONFIRMATION" | translate }}
        </button>

        <button mat-raised-button
                *ngIf="authService.user.value.role.slug === 'director'"
                (click)="showConfirmationDialog()"
                [disabled]="isProcessing">
            <mat-icon>check_circle</mat-icon>
            {{ "DIRECTORATE_APPROVAL" | translate}}
        </button>

        <button mat-raised-button
                *ngIf="!hasError && authService.user.value.role.slug === 'director'"
                [disabled]="isProcessing">
            <mat-icon>undo</mat-icon>
            {{ "DIRECTORATE_UNAPPROVE" | translate}}
        </button>
    </mat-card-content>
</mat-card>

<mat-card *ngIf="isGeneralConfirmation && style === verificationStyles.SEPARATE_CARD">
    <mat-card-header>{{ "GENERAL_CONFIRMATION" | translate}}</mat-card-header>
    <br>
    <mat-card-content>
        <hr>
        <mat-progress-bar mode="indeterminate" *ngIf="!hasError"></mat-progress-bar>
        <div fxLayout="row wrap" fxLayoutAlign="start">
            <npa-view-element fxFlex="20">
                <div class="npa-label">{{"DATA_GENERAL_CONFIRMATION" | translate}}:</div>
                <div class="npa-value">{{'NO' | translate}}</div>
            </npa-view-element>
            <npa-view-element fxFlex="20">
                <div class="npa-label">{{"DATA_GENERAL_CONFIRMATION_DATE" | translate}}:</div>
                <div class="npa-value">
                    <div class="npa-number">
                    </div>
                </div>
            </npa-view-element>

            <npa-view-element fxFlex="30">
                <div class="npa-label">{{ "DATA_GENERAL_APPROVAL" | translate}}</div>
                <div class="npa-value">{{'NO' | translate}}</div>
            </npa-view-element>
            <npa-view-element fxFlex="30">
                <div class="npa-label">{{"DATA_GENERAL_APPROVAL_DATE" | translate}}:</div>
                <div class="npa-value">
                    <div class="npa-number">
                    </div>
                </div>
            </npa-view-element>
        </div>
        <button mat-raised-button
                *ngIf="!hasError"
                [disabled]="isProcessing">
            <mat-icon>undo</mat-icon>
            {{"DATA_GENERAL_UNCONFIRMATION" | translate }}
        </button>

        <button mat-raised-button
                (click)="showConfirmationDialog()"
                [disabled]="isProcessing">
            <mat-icon>check_circle</mat-icon>
            {{ "DATA_GENERAL_CONFIRMATION" | translate}}
        </button>

        <button mat-raised-button
                *ngIf="!hasError"
                [disabled]="isProcessing">
            <mat-icon>check_circle</mat-icon>
            {{ "CONFIRM_DATA_GENERAL_APPROVAL"| translate}}

        </button>

        <button mat-raised-button
                *ngIf="!hasError"
                [disabled]="isProcessing">
            <mat-icon>undo</mat-icon>
            {{ "<>" | translate}}
        </button>
    </mat-card-content>
</mat-card>
