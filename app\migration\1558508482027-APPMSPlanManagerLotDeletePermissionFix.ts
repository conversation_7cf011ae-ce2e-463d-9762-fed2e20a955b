import {MigrationInterface, QueryRunner} from 'typeorm';

export class APPMSPlanManagerLotDeletePermissionFix1558508482027 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<any> {
        await queryRunner.query(`
            insert into
              context_accesses (operation_id, is_enabled, role_id, context_id)
            select 2, 1, 12, id
            from contexts where slug in (
              'lot'
            )`
        );
    }

    public async down(queryRunner: QueryRunner): Promise<any> {
    }

}
