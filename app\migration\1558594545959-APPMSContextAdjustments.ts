import {MigrationInterface, QueryRunner} from 'typeorm';

export class APPMSContextAdjustments1558594545959 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<any> {
        await queryRunner.query(`
            update
              contexts
            set slug = 'project-planning-potential-risk'
              where slug = 'project-planning-potential-risks';
              `);
        await queryRunner.query(`
            update
              contexts
            set slug = 'project-implementation-potential-risk'
              where slug = 'project-implementation-potential-risks';
              `);
        await queryRunner.query(`
            update
              contexts
            set slug = 'project-planning-challenge'
              where slug = 'project-planning-challenges';
          `);
        await queryRunner.query(`
            update
              contexts
            set slug = 'project-implementation-challenge'
              where slug = 'project-implementation-challenges';
              `);
        await queryRunner.query(`
            update
              contexts
            set slug = 'project-planning-document'
              where slug = 'project-planning-documents';
              `);
        await queryRunner.query(`
            update
              contexts
            set slug = 'project-implementation-document'
              where slug = 'project-implementation-documents';
          `);
    }

    public async down(queryRunner: QueryRunner): Promise<any> {
    }

}
