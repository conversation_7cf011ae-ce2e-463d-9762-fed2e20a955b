import {IDropdown} from '../shared/types/general.types';

export interface IAlertListItem {
    id: number;
    instance_id: number;
    created_at: Date;
    contents: string;
    status_id?: number;
    status_name_da?: string;
    status_slug?: string;
}

export interface IAlert {
    id?: Date | any;
    contents: string | any;
    status?: IDropdown | any;
    status_id?: number | any;
}

export interface IAlertInstance {
    id?: number | any;
    alert_id: number | any;
    recipient_user_id: number | any;
    created_at: Date | any;
    alert?: IAlert | any;
}

export interface IAlertInstanceStatusLog {
    id?: number | any;
    alert_instance_id: number | any;
    alert_status_id: number | any;
}

export interface IAlertRole {
    id?: number;
    name_da: string;
    name_pa: string;
    name_en: string;
    module_id: number;
    slug: string;
    module: IDropdown;
}

export interface IAlertRecipientRole {
    id?: number;
    alert_subcategory_id: number;
    role_id: number;
    role: IAlertRole;
}

export interface IAppRealTimeSate {
    newAlerts?: number;
}
