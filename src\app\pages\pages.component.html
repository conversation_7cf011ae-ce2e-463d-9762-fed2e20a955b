<ng-container>
    <npa-header (sidebarMainToggling)="toggleMainSidebar($event)"
                (sidebarUtilitiesToggling)="toggleModulesSidebar($event)">
    </npa-header>
    <div class="wrapper">
        <npa-banner [scrollTop]="scrollTop" [isSidebarMainClose]="isSidebarMainClose"
                    [isSidebarUtilitiesClose]="isSidebarUtilitiesClose"></npa-banner>
        <div class="container"
             [ngClass]="{'sidebar-main-close': isSidebarMainClose, 'sidebar-main-open': !isSidebarMainClose, 'sidebar-modules-close': isSidebarUtilitiesClose, 'sidebar-modules-open': !isSidebarUtilitiesClose}">
            <npa-sidebar-main [isSidebarMainClose]="isSidebarMainClose"></npa-sidebar-main>

            <div class="app-center-container" app-center-container></div>

            <div #contents class="contents" [lang]="lang" (scroll)="calculateScrollTop()">
                <router-outlet></router-outlet>
            </div>
        </div>
        <npa-sidebar-utilities [isSidebarUtilitiesClose]='isSidebarUtilitiesClose'></npa-sidebar-utilities>
    </div>
</ng-container>

