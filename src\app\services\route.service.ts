import {Injectable} from '@angular/core';
import {Router} from '@angular/router';
import {AuthService} from './auth.service';


import {Location} from '@angular/common';
import {environment} from '../../environments/environment';
import {HelperService} from './helper.service';

@Injectable()
export class RouteService {
    constructor(private _router: Router,
                private _auth: AuthService,
                private _location: Location) {
    }

    lang = 'prs';

    setProperRoute() {
        this.lang = localStorage.getItem('lang') ? localStorage.getItem('lang') : this.lang;
        const path = this._location.prepareExternalUrl(this._location.path());
        const pathArray = path.split('/');
        const regex = new RegExp('recover-password-form*');
        const recoverPage = new RegExp('recover-password');
        if (this._auth.isUserAuthenticated) {
            if (pathArray[2] === 'unauthorized-access') {
                return;
            }
            if (HelperService.hasRedirectedSystemAccess.value) {
                HelperService.hasRedirectedSystemAccess.next(false);
                this._router.navigate([this.lang, 'unauthorized-access']);
                return;
            }
            if (pathArray[2] !== this._auth.user.value.role.slug && pathArray[2] !== 'unauthorized-access') {
                pathArray[1] === '' || pathArray[1] === this.lang || pathArray[1] === 'login' ?
                    this._router.navigateByUrl(`${this.lang}/${this._auth.user.value.role.slug}/dashboard`) :
                    this.navigateBasedManualRoleEntry(pathArray[1]);
            }
        } else if (pathArray[3] && (regex.test(pathArray[3]) || recoverPage.test(pathArray[3]))) {
            return;
        } else {
            if (!path.startsWith('/auth/recover-password-form') && !path.startsWith('/auth/recover-password')) {
                this.navigateToLoginForm();
            }
        }

    }

    navigateToLoginForm() {
        localStorage.removeItem('session');
        this._router.navigate([localStorage.getItem('lang'), 'login']);
    }

    navigateBasedManualRoleEntry(role): boolean {
        const selectedRole = this._auth.roles.filter(el => {
            if (el.slug === role) {
                return true;
            }
            return false;
        });
        if (!environment.production) {
            this.checkRole(selectedRole[0]);
        } else {
            if (this._auth.loggedInUser.role.can_act) {
                this.checkRole(selectedRole[0]);
            } else {
                this._router.navigateByUrl('unauthorized-access');
                return false;
            }

        }

    }

    checkRole(role) {
        if (role) {
            this._auth.user.value.role = role;
            return true;
        } else {
            this._router.navigateByUrl('unauthorized-access');
            return false;
        }
    }

}
