import {Injectable} from '@angular/core';
import * as moment from 'jalali-moment';
import {IDropdown, IScheduleListItem} from '../types/general.types';
import {DropDownsService} from '../../../services/dropDowns.service';

export const NPA_DATE_FORMATS = {
    parse: {
        dateInput: 'Y-MM-DD',
    },
    display: {
        dateInput: 'Y-MM-DD',
        monthYearLabel: 'MMM YYYY',
        dateA11yLabel: 'LL',
        monthYearA11yLabel: 'MMMM YYYY',
    },
};


@Injectable()
export class DateService {

    constructor(private _dropDownsService: DropDownsService) {
    }

    jalaliMonthDifference(passedStartDate: Date, passedEndDate: Date): number {
        if (
            !passedStartDate ||
            passedStartDate.toString() === 'Invalid Date' ||
            !passedEndDate ||
            passedEndDate.toString() === 'Invalid Date'
        ) {
            return 0;
        }

        const date1 = moment(passedStartDate).locale('fa');
        const date2 = moment(passedEndDate).locale('fa');
        const y1 = date1.jYear();
        const y2 = date2.jYear();
        const m1 = date1.jMonth();
        const m2 = date2.jMonth();
        const d1 = date1.jDate();
        const d2 = date2.jDate();
        let months = 0;
        if (y1 < y2) {
            if (y2 - y1 > 1) {
                months += (y2 - y1 - 1) * 12;
            }
            months += (11 - m1);
            months += m2 + 1;
        } else if (y1 === y2) {
            months += m2 - m1;
        }
        const date1DaysCount = moment(`${y1}-${m1 + 1}-01`, 'jYYYY-jM-jD')
            .add(1, 'jMonth')
            .subtract(1, 'jDay')
            .jDate();

        months -= d1 / date1DaysCount;

        const date2DaysCount = moment(`${y2}-${m2 + 1}-01`, 'jYYYY-jM-jD')
            .add(1, 'jMonth')
            .subtract(1, 'jDay')
            .jDate();

        months += d2 / date2DaysCount;

        return +months.toFixed(2);
    }


    dayDifference(passedStartDate: Date | string, passedEndDate: Date | string): number {
        if (typeof passedStartDate === 'string') {
            passedStartDate = new Date(passedStartDate);
        }

        if (typeof passedEndDate === 'string') {
            passedEndDate = new Date(passedEndDate);
        }

        let startDate: Date;
        let endDate: Date;
        if (!this.isValid(passedStartDate)) {
            return 0;
        } else {
            startDate = passedStartDate;
        }

        if (!this.isValid(passedEndDate)) {
            return 0;
        } else {
            endDate = passedEndDate;
        }

        const days = (endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24);
        return Math.floor(days + 1);
    }

    isValid(date: Date): boolean {
        if (!date) {
            return false;
        } else if (date instanceof Date) {
            return date.toString() !== 'Invalid Date';
        } else {
            return false;
        }
    }

    getJalaliMonthsInBetween(passedStartDate: Date, passedEndDate: Date): IScheduleListItem[] {

        const date1 = moment(passedStartDate).locale('fa');
        const date2 = moment(passedEndDate).locale('fa');
        const y1 = date1.jYear();
        const y2 = date2.jYear();
        const m1 = date1.jMonth();
        const m2 = date2.jMonth();

        const returnResult: IScheduleListItem[] = [];

        if (y1 < y2) {

            let indexMonth = m1, indexYear = y1;
            while ((indexMonth <= 11 && indexYear === y1) || indexYear < y2 || (indexMonth <= m2 && indexYear === y2)) {
                returnResult.push({
                    year: indexYear,
                    month: <IDropdown>this._dropDownsService.get('month', indexMonth + 1),
                    month_id: indexMonth + 1
                });

                if (indexMonth === 11) {
                    indexYear++;
                    indexMonth = 0;
                } else {
                    indexMonth++;
                }
            }

        } else if (y1 === y2) {
            for (let indexMonth = m1; indexMonth <= m2; indexMonth++) {
                returnResult.push({
                    year: y1,
                    month: <IDropdown>this._dropDownsService.get('month', indexMonth + 1),
                    month_id: indexMonth + 1
                });
            }
        }

        return returnResult;

    }

    getJalaliDate(date: Date) {
        if (date) {
            return moment(new Date(date)).format('jYYYY-jM-jD');
        }
    }
}
