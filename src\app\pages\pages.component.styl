@import '../../styles/definitions.styl';

.wrapper
  position relative
  top sizeHeaderHeight
  height "calc(100vh - %s)" % sizeHeaderHeight
  overflow-x hidden
  overflow-y auto
  background-color #f2f2f2

.contents[lang='prs'], .contents[lang='ps']
  position absolute
  left 0
  overflow hidden
  overflow-y scroll
  height "calc(100vh - %s - %s - 12.5px )" % (sizeHeaderHeight sizeBannerHeight)
  padding-top sizeBannerHeight

.sidebar-main-close
  .contents[lang='prs'], .contents[lang='ps']
    right sizeSidebarMainWidthClose

.sidebar-main-open
  .contents[lang='prs'], .contents[lang='ps']
    right sizeSidebarMainWidthOpen

.contents[lang='en']
  position absolute
  right 0
  text-align left
  overflow hidden
  overflow-y scroll
  height "calc(100vh - %s - %s - 12.5px )" % (sizeHeaderHeight sizeBannerHeight)
  padding-top sizeBannerHeight

.sidebar-main-close
  .contents[lang='en']
    left sizeSidebarMainWidthClose

.sidebar-main-open
  .contents[lang='en']
    left sizeSidebarMainWidthOpen

::-webkit-scrollbar
  width 0px
