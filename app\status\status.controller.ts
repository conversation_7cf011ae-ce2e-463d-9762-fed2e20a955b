import {Request, Response} from 'express';
import {InstanceService} from '../services/instance.service';
import {Model} from '../config/database';
import {IController} from '../types/controller';
import {IStatus, Status} from '../entity/status';


export class StatusController implements IController {
    public static instance: StatusController;

    public static getInstance(): StatusController {
        return InstanceService.getInstance(StatusController);

    }

    public index(req: Request, res: Response) {
        Model(Status).find().then(data => {
            if (data.length !== 0) {
                return res.status(200).json(data);
            }
            return res.status(404).json(data);
        }).catch(error => {
            return res.status(500).send(error);
        });
    }


    public store(req: Request, res: Response) {
        const statusModel = Model(Status);
        const data: IStatus = req.body;
        const status = statusModel.create(data);
        statusModel.save(status).then(() => {
            return res.status(201).json([]);
        }).catch(error => {
                return res.status(500).send(error);
            }
        );

    }

    public show(req: Request, res: Response) {
        Model(Status).findOne(req.params.id).then(data => {
            data ?
                res.status(200).json(data) :
                res.status(404).json(data);
        }).catch(error => {
            res.status(500).send(error);
        });
    }

    public update(req: Request, res: Response) {
        Model(Status).update({id: req.params.id}, req.body).then(result => {
            console.log(result);
            result.raw.affectedRows !== 0 ?
                res.status(204).json([]) :
                res.status(404).json(result);
        }).catch(error => {
                res.status(500).send(error);
            }
        );
    }

    public destroy(req: Request, res: Response) {
        Model(Status).delete(req.params.id).then(result => {
            result.raw.affectedRows !== 0 ?
                res.status(204).json([]) :
                res.status(404).json(result);
        }).catch(error => {
            res.status(500).send(error);
        });
    }
}
