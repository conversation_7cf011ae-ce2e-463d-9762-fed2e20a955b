const gulp = require("gulp");
const {series} = require('gulp');
const ts = require("gulp-typescript");
const gulpNodeWatch = require('gulp-nodemon');

const serverSourceFiles = 'app/**/*.ts'
const ServerDestination = {
    dir: 'server',
    entryPoint: 'server/index.js'
};


async function transpile() {
    return await gulp.src(serverSourceFiles)
        .pipe(ts({
            noImplicitAny: false,
            importHelpers: true,
            sourceMap: true,
            declaration: false,
            moduleResolution: "node",
            emitDecoratorMetadata: true,
            experimentalDecorators: true,
            target: "es5",
            lib: [
                "es2015",
                "dom"
            ],
            typeRoots: [
                "node_modules/@types"
            ],
        }))
        .pipe(gulp.dest(ServerDestination.dir));
}

async function runServer() {
    return await setTimeout(async () => {
        return await gulpNodeWatch({
            script: ServerDestination.entryPoint,
            ext: 'ts',
            watch: serverSourceFiles,
            stdout: true,
            readable: false,
            quiet: false,
            tasks: ['transpile']
        });
    }, 4000)
}

exports.transpile = transpile;
exports.default = series(runServer, transpile);
