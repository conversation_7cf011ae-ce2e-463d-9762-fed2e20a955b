import {Injectable} from '@angular/core';
import {HttpClient, HttpErrorResponse, HttpHeaders, HttpResponse} from '@angular/common/http';
import {Observable, Subject} from 'rxjs';
import {environment as localEnvironment} from '../../environments/environment';
import {HelperService} from './helper.service';
import {map} from 'rxjs/operators';

@Injectable()
export class HttpService {
    baseUrl: string;
    env = localEnvironment;

    constructor(private _http: HttpClient) {
        this.baseUrl = this.env.baseUrl.backend.main;
    }

    post(url: string, data, options?: {}, shouldSendAuthBearerToken: boolean = true): Observable<any> {
        data = this.prepareDataFormat(data);
        const formData: FormData = this.toFormData(data);
        return this.simpleXhrRequest(formData, url, shouldSendAuthBearerToken);
    }

    _post(url: string, data, options?: {}): Observable<any> {
        data = this.prepareDataFormat(data);
        return this._http.post(this.baseUrl + url, data, options ? {...options, ...{observe: 'response'}} : {observe: 'response'}).pipe(map(res => {
            if (!options) {
                return res['body'];
            }
            return res;
        }));
    }

    postSpecial(url: string, data, options?: {}): Observable<any> {
        return this._http.post(this.baseUrl + url, data, options ? {...options, ...{observe: 'response'}} : {observe: 'response'}).pipe(map(res => {
            if (!options) {
                return res['body'];
            }
            return res;
        }));
    }

    get(url: string, options?: {}): Observable<any> {
        return this._http.get(this.baseUrl + url, options ? {...options, ...{observe: 'response'}} : {observe: 'response'}).pipe(map(res => {
            if (!options) {
                return res['body'];
            }
            return res;
        }));
    }

    getExternal(url: string, options?: {}): Observable<any> {
      return this._http.get( url, options ? {...options, ...{observe: 'response'}} : {observe: 'response'}).pipe(map(res => {
          if (!options) {
              return res['body'];
          }
          return res;
      }));
    }

    delete(url: string, options?: {}): Observable<any> {
        return this._http.delete(this.baseUrl + url, options ? {...options, ...{observe: 'response'}} : {observe: 'response'}).pipe(map(res => {
            if (!options) {
                return res['body'];
            }
            return res;
        }));
    }

    head(url: string, options?: {}): Observable<any> {
        return this._http.head(this.baseUrl + url, options ? {...options, ...{observe: 'response'}} : {observe: 'response'}).pipe(map(res => {
            if (!options) {
                return res['body'];
            }
            return res;
        }));
    }

    put(url: string, data, options?: {}): Observable<any> {
        data = this.prepareDataFormat(data);
        const formData: FormData = this.toFormData(data);
        formData.append('_method', 'PUT');
        return this.simpleXhrRequest(formData, url);

        // return this._http.put(this.baseUrl + url, new FormData(data), options);
    }

    _put(url: string, data, options?: {}): Observable<any> {
        data = this.prepareDataFormat(data);
        return this._http.put(this.baseUrl + url, data, options ? {...options, ...{observe: 'response'}} : {observe: 'response'}).pipe(map(res => {
            if (!options) {
                return res['body'];
            }
            return res;
        }));
    }

    options(url: string, options?: {}): Observable<any> {
        return this._http.options(this.baseUrl + url, options ? {...options, ...{observe: 'response'}} : {observe: 'response'}).pipe(map(res => {
            if (!options) {
                return res['body'];
            }
            return res;
        }));
    }

    patch(url: string, data, options?: {}): Observable<any> {
        data = this.prepareDataFormat(data);
        return this._http.patch(this.baseUrl + url, data, options ? {...options, ...{observe: 'response'}} : {observe: 'response'}).pipe(map(res => {
            if (!options) {
                return res['body'];
            }
            return res;
        }));
    }

    jsonp(url: string, options = ''): Observable<any> {
        return this._http.jsonp(this.baseUrl + url, options);
    }

    download(url: string): Observable<any> {
        return this._http.get(this.baseUrl + url, {responseType: 'blob'});
    }

    prepareDataFormat(data): FormData {
        if (data.constructor.name === 'Array') {
            this.iterateArray(data);
        } else if (data.constructor.name === 'Object') {
            this.iterateObject(data);
        }
        return data;
    }

    iterateArray(data) {
        for (let i = 0; i < data.length; i++) {
            const property = data[i];
            if (property instanceof Date) {
                data[i] = property.getFullYear() + '-' + (property.getMonth() + 1) + '-' + property.getDate();
            } else if (property && property.constructor.name === 'Array') {
                this.iterateArray(property);
            } else if (property && property.constructor.name === 'Object') {
                this.iterateObject(property);
            }
        }
    }

    iterateObject(data) {
        for (const property in data) {
            if (data.hasOwnProperty(property)) {
                if (data[property] instanceof Date) {
                    data[property] = data[property].getFullYear() + '-' + (data[property].getMonth() + 1) + '-' + data[property].getDate();
                } else if (data[property] && data[property].constructor.name === 'Array') {
                    this.iterateArray(data[property]);
                } else if (data[property] && data[property].constructor.name === 'Object') {
                    this.iterateObject(data[property]);
                }
            }
        }
    }

    toFormData(obj) {
        const fd = new FormData();
        for (const property in obj) {
            if (obj.hasOwnProperty(property)) {
                if (typeof obj[property] !== 'object' && !(obj[property] instanceof File)) {
                    fd.append(property, obj[property]);
                } else if (typeof obj[property] === 'object') {
                    if (obj[property] && 'file' in obj[property]) {
                        fd.append('__' + property, obj[property]['file']);
                    }
                    const temp = JSON.stringify(obj[property]);
                    fd.append(property, temp);
                }
            }
        }

        return fd;
    }

    simpleXhrRequest(data, url, shouldSendAuthBearerToken?): Observable<any> {
        const observer = new Subject();
        const xhr = new XMLHttpRequest();
        xhr.open('POST', `${this.baseUrl}` + url);
        if (shouldSendAuthBearerToken) {
            xhr.setRequestHeader('Authorization', localStorage.getItem('session'));
        }
        xhr.onreadystatechange = (result) => {
            if (xhr.readyState === 4 && xhr.status < 300) {
                const headers = new HttpHeaders(xhr.getAllResponseHeaders());
                const newSession = headers.get('authorization');
                if (newSession) {
                    localStorage.setItem('session', newSession);
                }
                observer.next(new HttpResponse({
                    body: JSON.parse(xhr.responseText || '{}'),
                    headers: headers,
                    status: xhr.status,
                    statusText: xhr.statusText
                }));
            }
            if (xhr.status === 401) {
                HelperService.navigatedIfUnauthorized();
            }
            if (xhr.readyState === 4 && xhr.status >= 300) {
                observer.error(new HttpErrorResponse({
                    error: JSON.parse(xhr.responseText || '{}'),
                    headers: new HttpHeaders(xhr.getAllResponseHeaders()),
                    status: xhr.status,
                    statusText: xhr.statusText
                }));
                // HttpErrorResponse should be returned
            }
        };
        xhr.send(data);
        return observer;
    }
}
