@import '.././../../../styles/definitions.styl'

.container[lang=en]
  transition: height 0.2s linear
  color colorSidebarText
  height sizeBannerHeight
  background colorBanner url("../../../../assets/images/banner.png") repeat top
  margin-bottom 0
  position fixed
  z-index 2
  left sizeSidebarMainWidthOpen
  right sizeSidebarUtilitiesOpen
  box-shadow 0 2px 1px -1px rgba(0, 0, 0, 0.2), 0 1px 1px 0 rgba(0, 0, 0, 0.14), 0 1px 3px 0 rgba(0, 0, 0, 0.12)

.container
  transition: height 0.2s linear
  color colorSidebarText
  height sizeBannerHeight
  background colorBanner url("../../../../assets/images/banner.png") repeat top
  margin-bottom 0
  position fixed
  z-index 2
  right sizeSidebarMainWidthOpen
  left sizeSidebarUtilitiesOpen
  box-shadow 0 2px 1px -1px rgba(0, 0, 0, 0.2), 0 1px 1px 0 rgba(0, 0, 0, 0.14), 0 1px 3px 0 rgba(0, 0, 0, 0.12)

  //box-shadow 1px 1px 3px 0px rgba(123, 122, 122, 0.98);
  .lowerRow
    display flex
    width 95%
    margin 0 auto
    justify-content space-between
    align-items flex-start
    margin-top 10px
    opacity 1
    transition opacity 0.1s linear
    .title
      overflow hidden
      text-overflow ellipsis
      white-space nowrap
      font-size 35px
  .upperRow
    display flex
    width 95%
    margin 0 auto
    padding-top 20px
    justify-content space-between
    color colorDarkSidebarHeader
    .breadcrumb
      width 65%
      white-space nowrap
      overflow hidden
      display flex
      padding 5px 10px
      padding-right 0
      align-items center
      font-size 12px
      color colorWhiteSidebarHeader
      .breadcrumb-name
        text-decoration none
        display flex
        align-items center
        color colorWhiteSidebarHeader
        div
          display flex
          align-items center
        .mat-icon
          font-size 18px
          display flex
          align-items center
    .contract-spec
      direction ltr
      text-overflow ellipsis
      white-space nowrap
      overflow hidden
      font-size 20px
      text-align left

.container[lang='en'].sidebar-main-close
  left sizeSidebarMainWidthClose

.container[lang='en'].sidebar-utilities-close
  right 0

.container[lang=prs].sidebar-main-close, .container[lang=ps].sidebar-main-close
  right sizeSidebarMainWidthClose

.container[lang=prs].sidebar-utilities-close, .container[lang=ps].sidebar-utilities-close
  left 0

.sticky
  height 50px !important
  overflow hidden
  transition height 0.1s linear

  .lowerRow
    opacity 0
    transition opacity 0.1s linear
  .upperRow
    display flex
    width 95%
    margin 0 auto
    padding-top 10px
    justify-content space-between
    .breadcrumb
      display flex
      padding 5px 10px
      align-items center
      font-size 12px
      mat-icon
        display flex
        align-items center
        font-size 15px
        justify-content center

//.full-width
//  width: "calc(100% - %s)" % sizeSidebarMainWidthClose !important

.contract-spec:hover
  cursor pointer !important

.english-font
  color: #FFF
  font-family Roboto, 'Helvetica Neue Light', 'Helvetica Neue', Helvetica, Arial, 'Lucida Grande', sans-serif
  margin-right -5px

.name
  margin-left 5px
