<div fxLayout="row wrap">
  <div fxFlex="100" fxLayoutAlign="end">
    <button mat-dialog-close mat-icon-button matTooltip="{{'CLOSE' | translate}}">
      <mat-icon>close</mat-icon>
    </button>
  </div>
</div>
<mat-dialog-content class="dialog" fxLayout="row wrap" fxLayoutAlign="start">
  <div fxLayout="row wrap" fxLayoutAlign="start" fxFlex="100">
    <ng-container>

    </ng-container>
  </div>
</mat-dialog-content>
