<button mat-icon-button matTooltip="بستن" [mat-dialog-close]="true" class="dialog-close-button">
    <mat-icon>close</mat-icon>
</button>
<div mat-dialog-title #container>{{'LIST_OF_UPLOADED_FILES'|translate}}</div>
<mat-dialog-content>
    <mat-table #table *ngIf="dataSource" [dataSource]="dataSource">
        <ng-container matColumnDef="number">
            <mat-header-cell *matHeaderCellDef fxFlex="10">{{'ID'|translate}}</mat-header-cell>
            <mat-cell *matCellDef="let row" fxFlex="10"> {{row.number}}</mat-cell>
        </ng-container>
        <ng-container matColumnDef="original_name">
            <mat-header-cell *matHeaderCellDef fxFlex="40">{{'FILE_NAME'|translate}}</mat-header-cell>
            <mat-cell *matCellDef="let row" fxFlex="40"> {{row.original_name}}</mat-cell>
        </ng-container>
        <ng-container matColumnDef="created_at">
            <mat-header-cell *matHeaderCellDef fxFlex="35">{{'UPLOAD_DATE'|translate}}</mat-header-cell>
            <mat-cell *matCellDef="let row" fxFlex="35"> {{row.created_at}}</mat-cell>
        </ng-container>
        <ng-container matColumnDef="option">
            <mat-header-cell *matHeaderCellDef fxFlex="15">{{'OPERATIONS'|translate}}</mat-header-cell>
            <mat-cell *matCellDef="let row" fxFlex="15">
                <button
                        mat-icon-button
                        (click)="downloadFile(row)"
                        matTooltip="{{'DOWNLOAD'|translate}}"
                        [disabled]="notificationsService.isLoading"
                >
                    <mat-icon>file_download</mat-icon>
                </button>
                <button
                        mat-icon-button
                        *ngIf="data?.showDeleteOption"
                        color="warn"
                        (click)="deleteFile(row?.id)"
                            matTooltip="{{'DELETE'|translate}}"
                        [disabled]="notificationsService.isLoading"
                >
                    <mat-icon>delete</mat-icon>
                </button>
            </mat-cell>
        </ng-container>

        <mat-header-row *matHeaderRowDef="columns"></mat-header-row>
        <mat-row *matRowDef="let row; columns: columns;" fxLayout="row wrap" fxLayoutAlign="center"></mat-row>
    </mat-table>
</mat-dialog-content>
<mat-dialog-actions>
    <button mat-raised-button mat-dialog-close>{{'CLOSE'|translate}}</button>
</mat-dialog-actions>