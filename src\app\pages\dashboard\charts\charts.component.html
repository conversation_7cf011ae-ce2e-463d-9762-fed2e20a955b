<mat-expansion-panel>
    <mat-expansion-panel-header>
        <mat-panel-title>{{'FILTERING'|translate}}</mat-panel-title>
    </mat-expansion-panel-header>
    <form [formGroup]="form" fxLayout="row wrap"
          fxLayoutAlign="start center" class="search-options">
        <mat-form-field fxFlex="20">
            <mat-select placeholder="{{'COMPLAINT_TYPE'|translate}}" formControlName="complaint_type">
                <mat-option *ngFor="let procurement_type of procurement_types" [value]="procurement_type">
                    {{ procurement_type.name_da }}
                </mat-option>
            </mat-select>
        </mat-form-field>
        <mat-form-field fxFlex="20">
            <input matInput
                   placeholder="{{'COMPLAINANT_COMPANY'|translate}}"
                   formControlName="complainant_company">
        </mat-form-field>
        <mat-form-field fxFlex="20">
            <mat-select placeholder="{{'PROCUREMENT_TYPE'|translate}}" formControlName="procurement_type">
                <mat-option *ngFor="let procurement_type of procurement_types" [value]="procurement_type">
                    {{ procurement_type.name_da }}
                </mat-option>
            </mat-select>
        </mat-form-field>
        <mat-form-field fxFlex="20" *ngIf="authService.user.value.role.slug !== 'procurement-entity'">
            <mat-select placeholder="{{'RELATED_PROCUREMENT_ENTITY'|translate}}" formControlName="related_procurement_entity">
                <mat-option *ngFor="let procurement_type of procurement_types" [value]="procurement_type">
                    {{ procurement_type.name_da }}
                </mat-option>
            </mat-select>
        </mat-form-field>

        <mat-form-field fxFlex="20">
            <input matInput
                   placeholder="{{'COMPLAINT_LODGING_DATE'|translate}}" type="number"
                   formControlName="date_of_saved_complaint">
        </mat-form-field>
        <mat-form-field fxFlex="20">
            <input matInput
                   placeholder="{{'USER_NAME'|translate}}"
                   formControlName="project_name">
        </mat-form-field>
        <mat-form-field fxFlex="20">
            <mat-select placeholder="{{'COMPLAINT_STATUS'|translate}}" formControlName="complaint_status">
                <mat-option *ngFor="let procurement_type of procurement_types" [value]="procurement_type">
                    {{ procurement_type.name_da }}
                </mat-option>
            </mat-select>
        </mat-form-field>
        <mat-form-field fxFlex="20">
            <input matInput
                   placeholder="{{'COMPLAINT_NUMBER'|translate}}" type="number"
                   formControlName="complaint_id">
        </mat-form-field>
        <mat-form-field fxFlex="20">
            <mat-select placeholder="{{'YEAR'|translate}}" formControlName="year">
                <mat-option *ngFor="let procurement_type of procurement_types" [value]="procurement_type">
                    {{ procurement_type.name_da }}
                </mat-option>
            </mat-select>
        </mat-form-field>
        <mat-form-field fxFlex="20" *ngIf="authService.user.value.role.slug === 'director'">
            <mat-select placeholder="{{'SPECIFIED_MEMBER'|translate}}" formControlName="specified_members">
                <mat-option *ngFor="let procurement_type of procurement_types" [value]="procurement_type">
                    {{ procurement_type.name_da }}
                </mat-option>
            </mat-select>
        </mat-form-field>

    </form>
    <mat-action-row fxLayout="row " fxLayoutAlign="start">
        <div class="search" fxFlex="50">
            <button mat-raised-button
                    color="primary"
                    type="button"
                    (click)="isValid()"
                    [disabled]="!isValid()|| notificationsService.isLoading">{{'FILTER'|translate}}
            </button>
            <button type="button"
                    mat-button
                    (click)="this.form.reset()"
                    [disabled]="notificationsService.isLoading">{{'RESET'|translate}}
            </button>
        </div>
    </mat-action-row>
</mat-expansion-panel>
<!-------------------------   charts   ------------------------->
<!--<npa-charts-list *ngIf="authService.user.value.role.slug === 'procurement-entity'"></npa-charts-list>-->
