@import '../../styles/definitions.styl';
.H2SoFe {
  overflow: hidden;
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  -webkit-flex-direction: column;
  flex-direction: column;
  min-height: 100vh;
  position: relative;
  background-image: url('../../assets/images/half-wheel.svg')
  background-size:contain;
  background-repeat: no-repeat;
  background-position: right;
}


.H2SoFe:before, .H2SoFe:after {
  -webkit-box-flex: 1;
  box-flex: 1;
  -webkit-flex-grow: 1;
  flex-grow: 1;
  content: '';
  display: block;
  height: 24px;
}

.H2SoFe.LZgQXe:after {
  min-height: 64px;
}

.H2SoFe:before, .H2SoFe:after {
  -webkit-box-flex: 1;
  ex: 1;
  -webkit-flex-grow: 1;
  flex-grow: 1;
  content: '';
  display: block;
  height: 24px;
}

.H2SoFe, .H2SoFe:before, .H2SoFe:after {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}

.RAYh1e.LZgQXe {
  min-height: 0;
}

.RAYh1e {
  border: 1px solid #dadce0;
}

.RAYh1e {
  -webkit-flex-shrink: 0;
  flex-shrink: 0;
  background: #fff;
  -webkit-border-radius: 8px;
  border-radius: 8px;
  -webkit-box-shadow: none;
  box-shadow: none;
  display: block;
  margin: 0 auto;
  min-height: 0;
  width: 450px;
}

//.RAYh1e, .xkfVF {
//  -webkit-transition: .2s;
//  transition: .2s;
//}
//.RAYh1e {
//  display: -webkit-box;
//  display: -moz-box;
//  display: -ms-flexbox;
//  display: -webkit-flex;
//  display: flex;
//  -webkit-flex-direction: column;
//  flex-direction: column;
//  background: #fff;
//  max-width: 100%;
//  position: relative;
//  z-index: 2;
//}
.H2SoFe *, .H2SoFe *:before, .H2SoFe *:after {
  -webkit-box-sizing: inherit;
  box-sizing: inherit;
}

.H2SoFe *, .H2SoFe *:before, .H2SoFe *:after {
  -webkit-box-sizing: inherit;
  box-sizing: inherit;
}

.xkfVF {
  height: auto;
  min-height: 400px;
  overflow-y: auto;
}

.xkfVF {
  padding: 48px 40px 15px;
}

.RAYh1e, .xkfVF {
  -webkit-transition: .2s;
  transition: .2s;
}

.fctIrd {
  height: 57px;
}

.SSBsw {
  height: 37px;
  margin: 0 auto;
  overflow: visible;
  position: relative;
  width: 74px;
}

.DRS7Fe {
  overflow: hidden;
}

.k6Zj8d {
  padding-left: 24px;
  padding-right: 24px;
}

.bxPAYd {
  margin: auto -40px;
}

.jXeDnc {
  text-align: center;
}

.jXeDnc h1 {
  font-family: 'Google Sans', arial, sans-serif;
}

.jXeDnc h1 {
  padding-bottom: 0;
  padding-top: 16px;
  font-size: 24px;
  font-weight: 400;
  line-height: 1.3333;
  margin-bottom: 0;
  margin-top: 0;
}

.RwBngc {
  height: 16.8px;
  padding: 24px 0 0;
  position: absolute;
  width: 100%;
}
4
.u7land {
  height: 16.8px;
  margin: 8px 0;
}

.u7land {
  margin: 0;
}

.TkU0Xc.TkU0Xc {
  font-size: inherit;
  font-weight: inherit;
  margin: -8px 0 0 -16px;
}

form span
  margin 10px

//////////////////////////////////////////////////////////////////////////////

.RwBngc {
  height: 16.8px;
  position: absolute;
  width: 100%;
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  -webkit-flex-wrap: wrap;
  flex-wrap: wrap;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  font-size: 12px;
  line-height: 1.4;
  padding: 0 24px 14px;
}
.Bgzgmd {
  list-style: none;
  margin: 8px -16px;
  padding: 0;
}
.Bgzgmd li {
  display: inline-block;
  margin: 0;
}
.Bgzgmd a {
  -webkit-border-radius: 2px;
  border-radius: 2px;
  color: #757575;
  padding: 6px 16px;
  -webkit-transition: background .2s;
  transition: background .2s;
}
a, a:hover, a:visited, a[href].uBOgn, button[type=button].uBOgn {
  color: #1a73e8;
  cursor: pointer;
  font-weight: 500;
  text-decoration: none;
  outline: none;
}
form
  margin-top 10px
.links
  display flex
  flex-direction column
  align-items center
  margin-bottom 15px
  font-size 14px
.margin-top
  margin-bottom  10px
