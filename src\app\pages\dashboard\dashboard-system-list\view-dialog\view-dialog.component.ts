import {Component, Inject, OnInit} from '@angular/core';
import {MAT_DIALOG_DATA, MatDialogRef} from '@angular/material';
import {TranslateService} from '../../../shared/services/translate.service';


@Component({
  selector: 'npa-view-dialog',
  templateUrl: './view-dialog.component.html',
  styleUrls: ['./view-dialog.component.styl']
})
export class ViewDialogComponent implements OnInit {

  constructor(private dialogRef: MatDialogRef<ViewDialogComponent>,
              @Inject(MAT_DIALOG_DATA) public data) {
  }
  ngOnInit() {
  }
}
