import * as env from 'dotenv';
import * as path from 'path';
import 'reflect-metadata';
import {Connection, createConnection, createQ<PERSON><PERSON><PERSON><PERSON>er, EntitySchema, getConnection, getConnectionOptions, QueryRunner, Repository} from 'typeorm';
import {User} from '../entity/user';
import {Status} from '../entity/status';
import {System} from '../entity/system';
import {UserRole} from '../entity/user-role';
import {Session} from '../entity/session';
import {Role} from '../entity/role';
import {InitialSeed1546235144497} from '../migration/1546235144497-InitialSeed';
import {Context} from '../entity/context';
import {UserRolePe} from '../entity/user-role-pe';
import {UserRoleSector} from '../entity/user-role-sector';
import {UserRoleVendor} from '../entity/user-role-vendor';
import {UserRoleRecord} from '../entity/user-role-record';
import {ACPMSDashboardSeed1547619568335} from '../migration/1547619568335-ACPMSDashboardSeed';
import {ContextAccess} from '../entity/context-access';
import {TriggerToAllowOnlyOneVendorInformationApprovalRoleInUserRoleTable1548913608524} from '../migration/1548913608524-TriggerToAllowOnlyOneVendorInformationApprovalRoleInUserRoleTable';
import {ACPMSDashboardContextsDistributionToRoles1550619568335} from '../migration/1550619568335-ACPMSDashboardContextsDistributionToRoles';
import {AddAPPMSToSystems1552712540241} from '../migration/1552712540241-AddAPPMSToSystems';
import {AddAPPMSRoles1552718528193} from '../migration/1552718528193-AddAPPMSRoles';
import {AddAPPMSContexts1552732343305} from '../migration/1552732343305-AddAPPMSContexts';
import {Operation} from '../entity/operation';
import {OperationSeed1550619568334} from '../migration/1550619568334-OperationSeed';
import {PeResourceNotApplicable} from '../entity/pe-resource-not-applicable';
import {SectorResourceNotApplicable} from '../entity/sector-resource-not-applicable';
import {UserRoleThreshold} from '../entity/user-role-threshold';
import {AddAPPMSContextAccesses1553754860138} from '../migration/1553754860138-AddAPPMSContextAccesses';
import {AddObserverRole1555739269461} from '../migration/1555739269461-AddObserverRole';
import {APPMSProcurementPlanManagerRoleAdjustment1557562935378} from '../migration/1557562935378-APPMSProcurementPlanManagerRoleAdjustment';
import {APPMSPlanManagerLotDeletePermissionFix1558508482027} from '../migration/1558508482027-APPMSPlanManagerLotDeletePermissionFix';
import {APPMSContextAdjustments1558594545959} from '../migration/1558594545959-APPMSContextAdjustments';
import {AddACPMSContextForLog1559213046739} from '../migration/1559213046739-AddACPMSContextForLog';
import {AddACPMSContextAccessForLog1559213216789} from '../migration/1559213216789-AddACPMSContextAccessForLog';
import {AppmsContextSlugFix1561806281529} from '../migration/1561806281529-AppmsContextSlugFix';
import {AppmsContextAccessFix1561886591548} from '../migration/1561886591548-AppmsContextAccessFix';
import {AlterAPPMSRolePermissions1562131837619} from '../migration/1562131837619-AlterAPPMSRolePermissions';
import {AddACPMSContextAccesses1562821279347} from '../migration/1562821279347-AddACPMSContextAccesses';
import {DeleteTriggerToAllowOnlyOneVendorInformationApproveRoleInUserRoleTable1563774310740} from '../migration/1563774310740-DeleteTriggerToAllowOnlyOneVendorInformationApproveRoleInUserRoleTable';
import {AlterAPPMSContextAccess1564291508513} from '../migration/1564291508513-AlterAPPMSContextAccess';
import {InsertAvrcsSigtasContextns1562131837620} from '../migration/1562131837620-InsertAvrcsSigtasContext';
import {AddAppmsImplementationManagerRole1564291508550} from '../migration/1564291508550-AddAppmsImplementationManagerRole';
import {AddCDMToSystems1566463813009} from '../migration/1566463813009-AddCDMToSystems';
import {AddCDMRoles1566463996759} from '../migration/1566463996759-AddCDMRoles';
import {AddCDMContexts1566465574533} from '../migration/1566465574533-AddCDMContexts';
import {AddCDMContextAccesses1566466639079} from '../migration/1566466639079-AddCDMContextAccesses';
import {AddCDMUsers1566467449207} from '../migration/1566467449207-AddCDMUsers';
import {AddCDMUserRoles1566468106589} from '../migration/1566468106589-AddCDMUserRoles';
import {AddBelowThreshouldContextsToImplementationManager1573706657392} from '../migration/1573706657392-AddBelowThreshouldContextsToImplementationManager';
import {AddAcpmsAssignerPublisherRole1700000000000} from '../migration/1700000000000-AddAcpmsAssignerPublisherRole';

env.load({path: path.resolve(__dirname, '../../.env')});


const Entities = [
    Status,
    User,
    System,
    Role,
    UserRole,
    Context,
    Operation,
    ContextAccess,
    Session,
    UserRolePe,
    UserRoleSector,
    UserRoleVendor,
    UserRoleRecord,
    PeResourceNotApplicable,
    SectorResourceNotApplicable,
    UserRoleThreshold,
];
const Migrations = [
    InitialSeed1546235144497,
    ACPMSDashboardSeed1547619568335,
    TriggerToAllowOnlyOneVendorInformationApprovalRoleInUserRoleTable1548913608524,
    ACPMSDashboardContextsDistributionToRoles1550619568335,
    AddAPPMSToSystems1552712540241,
    AddAPPMSRoles1552718528193,
    AddAPPMSContexts1552732343305,
    OperationSeed1550619568334,
    ACPMSDashboardContextsDistributionToRoles1550619568335,
    AddAPPMSContextAccesses1553754860138,
    AddObserverRole1555739269461,
    APPMSProcurementPlanManagerRoleAdjustment1557562935378,
    APPMSPlanManagerLotDeletePermissionFix1558508482027,
    APPMSContextAdjustments1558594545959,
    AddACPMSContextForLog1559213046739,
    AddACPMSContextAccessForLog1559213216789,
    AppmsContextSlugFix1561806281529,
    AppmsContextAccessFix1561886591548,
    AlterAPPMSRolePermissions1562131837619,
    AddACPMSContextAccesses1562821279347,
    DeleteTriggerToAllowOnlyOneVendorInformationApproveRoleInUserRoleTable1563774310740,
    AlterAPPMSContextAccess1564291508513,
    InsertAvrcsSigtasContextns1562131837620,
    AddAppmsImplementationManagerRole1564291508550,
    AddCDMToSystems1566463813009,
    AddCDMRoles1566463996759,
    AddCDMContexts1566465574533,
    AddCDMContextAccesses1566466639079,
    AddCDMUsers1566467449207,
    AddCDMUserRoles1566468106589,
    AddBelowThreshouldContextsToImplementationManager1573706657392,
    AddAcpmsAssignerPublisherRole1700000000000,
];

interface IRunner {
    model: Repository<EntitySchema> | any;
    instance: QueryRunner | any;
}

export class Database {

    public static async connect(): Promise<Connection> {
        try {
            const options = <any>await getConnectionOptions();
            options.entities = Entities;
            options.migrations = Migrations;
            return await createConnection(options);
        } catch (e) {
            return Promise.reject(e);
        }
    }

    public static async QueryRunner(): Promise<IRunner> {
        try {
            const queryRunner = getConnection().createQueryRunner();
            await queryRunner.connect();
            return {
                model: (model) => {
                    return queryRunner.manager.getRepository(model);
                },
                instance: queryRunner
            };

        } catch (e) {
            return Promise.reject(e);
        }

    }

}

export function Model(entity) {
    return getConnection().getRepository(entity);
}


export async function QBuilderGetOne(params) {

    return await createQueryBuilder(params.entity)
        .innerJoinAndSelect(`${params.entity}.${params.relation.name}`, params.relation.name, prepareCondition(params.relation.name, params.relation.conditions))
        .where(prepareCondition(params.entity, params.conditions))
        .getOne();
}


export async function QBuilderGetMany(params) {

    return await createQueryBuilder(params.entity)
        .innerJoinAndSelect(`${params.entity}.${params.relation.name}`, params.relation.name, `${params.relation.name}.${params.relation.condition.name}=${params.relation.condition.value}`)
        .where(`${params.entity}.${params.condition.name}= ${params.condition.value}`)
        .getMany();
}


function prepareCondition(entity, condition): string {
    let conditionString = '';
    let counter = 1;
    for (const item in condition) {
        if (condition.hasOwnProperty(item)) {
            conditionString += `${entity}.${item}=${condition[item]} `;
            conditionString += (Object.keys(condition).length > counter++) ? ' AND ' : '';
        }
    }
    return conditionString;
}
