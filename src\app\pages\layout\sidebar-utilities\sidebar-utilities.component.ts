import {Component, Input, OnInit} from '@angular/core';
import {NotificationsService} from '../../shared/services/notifications.service';
import {MatDialog} from '@angular/material';
import {AddRemarkDialogComponent} from './add-remark-dialog/add-remark-dialog.component';
import {NPA_COMMON_DIALOG_WIDTH} from '../../shared/consts/sizes';


@Component({
    selector: 'npa-sidebar-utilities',
    templateUrl: './sidebar-utilities.component.html',
    styleUrls: ['./sidebar-utilities.component.styl']
})
export class SidebarUtilitiesComponent implements OnInit {
    @Input() isSidebarUtilitiesClose;

    constructor(private _dialog: MatDialog,
                public notificationsService: NotificationsService) {
    }

    ngOnInit() {

    }

    openAddDialog() {
        const dialogRef = this._dialog.open(AddRemarkDialogComponent, {
            width: NPA_COMMON_DIALOG_WIDTH,
            data: {},
            disableClose: true,
        });
        dialogRef.afterClosed();

    }
}