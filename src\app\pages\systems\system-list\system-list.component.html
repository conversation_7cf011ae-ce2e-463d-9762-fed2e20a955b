<mat-card id="ng_test_card">
  <mat-card-header>{{'SYSTEMS_LIST' |translate}}</mat-card-header>
  <mat-card-content>
    <mat-table #table *ngIf="dataSource" [dataSource]="dataSource">

      <ng-container matColumnDef="system_icon">
        <mat-header-cell *matHeaderCellDef>
          {{'SYSTEM_ICON'| translate}}
        </mat-header-cell>
        <mat-cell *matCellDef="let row">
          {{row.system_icon}}
        </mat-cell>
      </ng-container>

      <ng-container matColumnDef="system_slug">
        <mat-header-cell *matHeaderCellDef>
          {{'SYSTEM_SLUG'| translate}}
        </mat-header-cell>
        <mat-cell *matCellDef="let row">
          {{row.system_slug}}
        </mat-cell>
      </ng-container>

      <ng-container matColumnDef="system_url">
        <mat-header-cell *matHeaderCellDef>
          {{'SYSTEM_URL'| translate}}
        </mat-header-cell>
        <mat-cell *matCellDef="let row">
          {{row.system_url}}
        </mat-cell>
      </ng-container>

      <ng-container matColumnDef="options">
        <mat-header-cell *matHeaderCellDef [class]="systemListService.getClass('options')">
          {{'OPTIONS' | translate}}
        </mat-header-cell>
        <mat-cell *matCellDef="let row" [class]="systemListService.getClass('options')">
          <button
            *ngIf="authService.user.value.role.slug === 'dev-ops'"
            type='button' mat-icon-button
            matTooltip="{{'UPDATE_INFORMATION' | translate}}" (click)="openAddEditDialog(row)">
            <mat-icon>edit</mat-icon>
          </button>

          <button
            type='button' mat-icon-button
            matTooltip="{{'VIEW_INFORMATION' | translate}}" (click)="openViewDialog(row)">
            <mat-icon>visibility</mat-icon>
          </button>
          <button
            *ngIf="authService.user.value.role.slug === 'dev-ops'"
            type='button' mat-icon-button color="warn" matTooltip="{{'DELETE' | translate}}">
            <mat-icon>delete</mat-icon>
          </button>
        </mat-cell>
      </ng-container>
      <mat-header-row *matHeaderRowDef="chosenTitles" [style.width]="getWidth()"></mat-header-row>
      <mat-row [ngClass]="row?.withdrawal_reason ? 'red-background' : ''"
               *matRowDef="let row; columns: chosenTitles" [style.width]="getWidth()"></mat-row>
    </mat-table>
    <div class="pagination-container">
      <mat-paginator [dir]="dir"></mat-paginator>
    </div>
  </mat-card-content>
  <mat-card-footer fxLayout="row wrap" fxLayoutAlign="end">
    <button
      *ngIf="authService.user.value.role.slug === 'dev-ops'"
      mat-fab class="add-row-button"
      type="button" color="primary"
      (click)="openAddEditDialog()"
      matTooltip='{{"ADD_ROW" | translate}}'>
      <mat-icon>add</mat-icon>
    </button>
  </mat-card-footer>
</mat-card>
