import {BaseEntity, Column, Entity, Index, <PERSON>inColumn, ManyToOne, OneToMany, PrimaryGeneratedColumn} from 'typeorm';
import {Status} from './status';
import {ContextAccess} from './context-access';
import {UserRole} from './user-role';

@Entity('users')
@Index('unique_email', ['email'], {unique: true})
@Index('unique_username', ['username'], {unique: true})

export class User extends BaseEntity implements IUser {

    @PrimaryGeneratedColumn()
    id: number;

    @Column()
    name: string;

    @Column({name: 'last_name'})
    last_name: string;

    @Column({unique: true})
    username: string;

    @Column()
    password: string;

    @Column({default: false})
    is_single_use_password: boolean;

    @Column({unique: true})
    email: string;

    @Column({name: 'phone_number'})
    phone_number: string;

    @Column()
    status_id: number;

    @ManyToOne(type => Status, status => status.id)
    @JoinColumn({name: 'status_id'})
    status: Status;

    @OneToMany(type => UserRole, userRole => userRole.user)
    userRole: UserRole[];

}

export interface IUser {
    id?: number;
    name: string;
    last_name: string;
    username: string;
    password: string;
    email: string;
    phone_number: string;
    is_single_use_password?: boolean;
    status_id?: number | Status;

}

export const FIELDS = ['id', 'name', 'last_name', 'username', 'email', 'status_id', 'phone_number'];
