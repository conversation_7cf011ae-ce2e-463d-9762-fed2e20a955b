import {NgModule} from '@angular/core';
import {CommonModule} from '@angular/common';
import {alertComponent, alertRoute} from './alert.routes';
import {ArchivedComponent} from './archived/archived.component';
import {NewComponent} from './new/new.component';
import {
    MatButtonModule,
    MatCardModule,
    MatCheckboxModule,
    MatDatepickerModule,
    MatDialogModule,
    MatExpansionModule,
    MatFormFieldModule,
    MatIconModule,
    MatInputModule,
    MatNativeDateModule,
    MatRadioModule,
    MatSelectModule,
    MatTableModule,
    MatTabsModule,
    MatToolbarModule,
    MatTooltipModule
} from '@angular/material';
import {FormsModule, ReactiveFormsModule} from '@angular/forms';
import {FlexLayoutModule} from '@angular/flex-layout';
import {SharedModule} from '../shared/shared.module';
import {AlertViewDialogComponent} from './alert-view-dialog/alert-view-dialog.component';
import {ReminderDialogComponent} from './new/reminder-dialog/reminder-dialog.component';
import {ArchivedReminderDialogComponent} from './archived/archived-reminder-dialog/archived-reminder-dialog.component';
import {ArchivedViewDialogComponent} from './archived/archived-view-dialog/archived-view-dialog.component';

@NgModule({
    imports: [
        CommonModule,
        alertRoute,
        MatTabsModule,
        MatTableModule,
        MatButtonModule,
        MatCardModule,
        MatCheckboxModule,
        MatDatepickerModule,
        MatDialogModule,
        MatExpansionModule,
        MatFormFieldModule,
        MatIconModule,
        MatInputModule,
        MatNativeDateModule,
        MatRadioModule,
        MatSelectModule,
        MatToolbarModule,
        MatTooltipModule,
        FormsModule,
        SharedModule,
        FlexLayoutModule,
        ReactiveFormsModule,
    ],
    declarations: [
        ...alertComponent,
        ArchivedComponent,
        NewComponent,
        AlertViewDialogComponent,
        ReminderDialogComponent,
        ArchivedReminderDialogComponent,
        ArchivedViewDialogComponent
    ],
    entryComponents: [
        AlertViewDialogComponent,
        ReminderDialogComponent,
    ],
    providers: []
})
export class AlertModule {
}
