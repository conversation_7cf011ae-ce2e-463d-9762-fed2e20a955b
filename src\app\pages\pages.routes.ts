import {RouterModule, Routes} from '@angular/router';
import {PagesComponent} from './pages.component';
import {RouteGuardsService as RouteGuard} from '../services/route-guards.service';
import {UserProfileComponent} from './user-profile/user-profile.component';
import {HeaderComponent} from './layout/header/header.component';
import {SidebarMainComponent} from './layout/sidebar-main/sidebar-main.component';
import {SidebarSubMenuComponent} from './layout/sidebar-main/sidebar-sub-menu/sidebar-sub-menu.component';
import {SidebarUtilitiesComponent} from './layout/sidebar-utilities/sidebar-utilities.component';
import {TopBannerComponent} from './layout/top-banner/top-banner.component';
import {ComplaintGeneralInformationDialogComponent} from './layout/top-banner/complaint-general-information-dialog/complaint-general-information-dialog.component';
import {AddRemarkDialogComponent} from './layout/sidebar-utilities/add-remark-dialog/add-remark-dialog.component';
import {RangeInputComponent} from './shared/components/range-input/range-input.component';
import {ResetPasswordComponent} from './user-profile/reset-password/reset-password.component';
import {UpdateProfileComponent} from './user-profile/update-profile/update-profile.component';

export const pagesComponents = [
    PagesComponent,
    HeaderComponent,
    SidebarMainComponent,
    SidebarSubMenuComponent,
    SidebarUtilitiesComponent,
    TopBannerComponent,
    UserProfileComponent,
    ComplaintGeneralInformationDialogComponent,
    AddRemarkDialogComponent,
    RangeInputComponent,
    ResetPasswordComponent,
    UpdateProfileComponent,
];
const routes: Routes = [

    {
        path: ':role',
        component: PagesComponent,
        canActivate: [RouteGuard],
        children: [
            {
                path: '',
                redirectTo: 'dashboard',
                pathMatch: 'full',
            },
            {
                path: 'dashboard',
                loadChildren: 'app/pages/dashboard/dashboard.module#DashboardModule',
                canActivate: [RouteGuard]
            },
            {
                path: 'systems',
                loadChildren: 'app/pages/systems/systems.module#SystemsModule',
                canActivate: [RouteGuard]
            },
            {
                path: 'contexts',
                loadChildren: 'app/pages/contexts/contexts.module#ContextsModule',
                canActivate: [RouteGuard]
            },
            {
                path: 'roles',
                loadChildren: 'app/pages/roles/roles.module#RolesModule',
                canActivate: [RouteGuard]
            },
            {
                path: 'sessions',
                loadChildren: 'app/pages/sessions/sessions.module#SessionsModule',
                canActivate: [RouteGuard]
            },
            {
                path: 'user-profile',
                component: UserProfileComponent,
                canActivate: [RouteGuard]
            },
            {
                path: 'user-guide',
                loadChildren: 'app/pages/user-guide/user-guide.module#UserGuideModule',
                canActivate: [RouteGuard]
            },
            {
                path: 'alert',
                loadChildren: 'app/pages/alert/alert.module#AlertModule',
            },
            {
                path: '**',
                redirectTo: 'dashboard',
                pathMatch: 'full',
            }
        ]
    },
];

export const pagesRoutes = RouterModule.forChild(routes);
