<div fxLayout="row wrap" fxFlexAlign="start">
  <div fxFlex="95">{{'VIEW_INFORMATION' | translate}}</div>
  <div fxFlex="5">
    <button mat-dialog-close mat-icon-button matTooltip="بستن">
      <mat-icon>close</mat-icon>
    </button>
  </div>
</div>
<mat-dialog-content class="dialog" fxLayout="row wrap" fxLayoutAlign="start">
  <npa-view-element fxFlex="25">
    <div class="npa-label">{{'ID' | translate}}</div>
    <div class="npa-value">{{data?.id}}</div>
  </npa-view-element>
  <npa-view-element fxFlex="25">
    <div class="npa-label">{{'SYSTEM_ICON' | translate}}</div>
    <div class="npa-value"><mat-icon>{{data?.icon_name}}</mat-icon></div>
  </npa-view-element>
  <npa-view-element fxFlex="25">
    <div class="npa-label">{{'SYSTEM_SLUG' | translate}}</div>
    <div class="npa-value">{{data?.slug}}</div>
  </npa-view-element>
  <npa-view-element fxFlex="25">
    <div class="npa-label">{{'SYSTEM_URL' | translate}}</div>
    <div class="npa-value"><a href="{{data.url}}">{{data.url}}</a></div>
  </npa-view-element>
  <npa-view-element fxFlex="100">
    <div class="npa-label">{{'SYSTEM_DESCRIPTION' | translate}}</div>
    <div class="npa-value">{{data?.description}}</div>
  </npa-view-element>
</mat-dialog-content>
