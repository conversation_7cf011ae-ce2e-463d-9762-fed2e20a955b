import {Component, ElementRef, EventEmitter, Input, OnChanges, Output, ViewChild} from '@angular/core';
import * as moment from 'jalali-moment';
import {FormControl} from '@angular/forms';

moment().format('jYYYY/jM/jD');
moment.defineLocale('fa', {
    jMonths: ('حمل_ثور_جوزا_سرطان_اسد_سنبله_میزان_عقرب_قوس_جدی_دلو_حوت').split('_'),
    jMonthsShort: ('حمل_ثور_جوزا_سرطان_اسد_سنبله_میزان_عقرب_قوس_جدی_دلو_حوت').split('_')
});

@Component({
    selector: 'npa-date-time-picker',
    templateUrl: './npa-date-time-picker.component.html',
    styleUrls: ['./npa-date-time-picker.component.styl']
})
export class NpaDateTimePickerComponent implements OnChanges {

    @ViewChild('datePicker') nativeInputFile: ElementRef;
    @Input() npaDateModel: FormControl;
    @Input() valueChange;
    @Input() placeholder = 'تاریخ شمسی';
    @Output() npaDateModelChange = new EventEmitter();
    detectedChange = true;
    date;
    datePickerConfig = {
        format: 'jYYYY-jMM-jDD',
    };
    isFocused = false;
    momentFunction = moment;

    constructor() {

    }

    ngOnChanges() {
        if (typeof this.npaDateModel.value === 'string') {
            return;
        }
        if (!this.detectedChange) {
            if (!this.npaDateModel.value.getTime) {
                this.npaDateModel.setValue(this.npaDateModel.value.toDate());
            }

            if (this.npaDateModel.value.getTime() !== new Date(this.date).getTime()) {
                this.date = moment(this.npaDateModel.value);
            }
        }
        if (this.detectedChange) {
            if (this.npaDateModel.value) {
                if (this.npaDateModel.value === undefined) {
                    return;
                }
                if (this.npaDateModel.value.toString() === 'Invalid Date') {
                    return;
                }
                this.date = this.npaDateModel ? moment(this.npaDateModel.value) : moment(new Date());
                this.detectedChange = false;
            }


        }

    }

    onChangeInput(event) {
        const convertedDate = event instanceof Date ? event : new Date(event);
        if (moment(convertedDate).isValid()) {
            this.npaDateModel.setValue(convertedDate);
            this.npaDateModelChange.emit(this.npaDateModel);
        }
    }

    markAsTouched() {
        (<any>this.npaDateModel).touched = true;
    }

}
