<form [formGroup]="form" [dir]="dir">
    <h1 mat-dialog-title>{{data.title}}</h1>
    <mat-dialog-content>
        <div fxLayout="row wrap" fxLayoutAlign="start">
                <mat-checkbox formGroupName="checkBox">موجودیت وجوه مالی
                </mat-checkbox>
                <mat-form-field fxFlex="100">
                <textarea [(ngModel)]="reasonText" name="reason" matInput placeholder="دلایل عدم موجودیت وجوه مالی"
                          formControlName="reason">
                </textarea>
                <mat-error *ngIf="form?.get('reason')?.errors?.required">
                    {{formValidationService.required.message()}}
                </mat-error>
            </mat-form-field>
        </div>
    </mat-dialog-content>
    <mat-dialog-actions>
        <button mat-raised-button color="accent" (click)="confirm()" [disabled]="!isValid()">
            {{'درخواست'}}
        </button>
        <button mat-button (click)="cancel()">لغو</button>
    </mat-dialog-actions>
</form>
