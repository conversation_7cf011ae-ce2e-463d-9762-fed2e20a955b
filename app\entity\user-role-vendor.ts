import {BaseEntity, Column, Entity, Index, JoinColumn, ManyToOne, PrimaryGeneratedColumn} from 'typeorm';
import {UserRole} from './user-role';

@Entity('user_role_vendor')
@Index('unique_vendor_license_number', ['vendor_license_number'], {unique: true})
@Index('unique_user_role_id', ['user_role_id'], {unique: true})
export class UserRoleVendor extends BaseEntity implements IUserRoleVendor {

    @PrimaryGeneratedColumn()
    id: number;

    @Column({name: 'vendor_license_number'})
    vendor_license_number: string;

    @Column({name: 'user_role_id'})
    user_role_id: number;

    @ManyToOne(type => UserRole, userRole => userRole.id, {onDelete: 'CASCADE'})
    @JoinColumn({name: 'user_role_id'})
    userRole: UserRole;

}

export interface IUserRoleVendor {
    id?: number;
    vendor_license_number: string;
    user_role_id: number;
    userRole?: any | UserRole;
}


