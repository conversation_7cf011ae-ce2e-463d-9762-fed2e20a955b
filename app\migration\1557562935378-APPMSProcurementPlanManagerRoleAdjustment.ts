import {MigrationInterface, QueryRunner} from 'typeorm';

export class APPMSProcurementPlanManagerRoleAdjustment1557562935378 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<any> {
        await queryRunner.query(`
            UPDATE
              roles
            SET
              is_threshold_based = true,
              is_record_based = true,
              is_sector_based = true,
              is_pe_based = true
            WHERE slug = 'appms-procurement-plan-manager'
              `);
    }

    public async down(queryRunner: QueryRunner): Promise<any> {
    }

}
