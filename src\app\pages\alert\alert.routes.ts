import {RouterModule, Routes} from '@angular/router';
import {AlertComponent} from './alert.component';
import {NewComponent} from './new/new.component';
import {ArchivedComponent} from './archived/archived.component';

export const alertComponent = [
    AlertComponent,
    NewComponent,
    ArchivedComponent
];
const routes: Routes = [
    {
        path: '',
        component: AlertComponent,
        children: [
            {
                path: '',
                pathMatch: 'full',
                redirectTo: 'new',
            },
            {
                path: 'new',
                component: NewComponent,
            },
            {
                path: 'archived',
                component: ArchivedComponent,
            }
        ]
    }
];

export const alertRoute = RouterModule.forChild(routes);

