import {IDropdown} from '../types/general.types';
import {Injectable} from '@angular/core';

@Injectable()
export class FormService {
    selectCompare(a: IDropdown, b: IDropdown): boolean {
        return a && b && a.id === b.id;
    }

    selectCompareWithSlug(a: IDropdown, b: IDropdown): boolean {
        return a && b && a.slug === b.slug;
    }

    isAdding(data: any) {
        return !data.id;
    }
}
