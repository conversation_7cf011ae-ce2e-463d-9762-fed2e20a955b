<button mat-icon-button (click)="close()" matTooltip="بستن" class="dialog-close-button">
    <mat-icon>close</mat-icon>
</button>

<h1 mat-dialog-title>نمایش هشدار</h1>
<mat-dialog-content class="dialog" fxLayout="row wrap" fxLayoutAlign="start" *ngIf="alert">
    <npa-view-element fxFlex="25">
        <div class="npa-label">آی دی اختصاصی:</div>
        <div class="npa-value">{{alert.instance_id}}</div>
    </npa-view-element>
    <npa-view-element fxFlex="25">
        <div class="npa-label">زمان ایجاد (شمسی):</div>
        <div class="npa-value">
            <span class="npa-number">
                {{alert.created_at | jalaliDateTime}}
            </span>
        </div>
    </npa-view-element>
    <npa-view-element fxFlex="25">
        <div class="npa-label">زمان ایجاد (میلادی):</div>
        <div class="npa-value">
            <span class="npa-number">
                {{alert.created_at | date:'yyyy-MM-dd HH:mm:ss'}}
            </span>
        </div>
    </npa-view-element>
    <npa-view-element fxFlex="25">
        <div class="npa-label">حالت:</div>
        <div class="npa-value">
            {{alert.status_name_da}}
        </div>
    </npa-view-element>
    <npa-view-element fxFlex="100">
        <div class="npa-label">متن:</div>
        <div class="npa-value">{{alert.contents}}</div>
    </npa-view-element>
</mat-dialog-content>
<mat-dialog-actions>
    <button mat-raised-button (click)="close()">بستن</button>
</mat-dialog-actions>
