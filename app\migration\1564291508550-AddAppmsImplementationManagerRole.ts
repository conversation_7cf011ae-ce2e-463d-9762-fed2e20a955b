import {MigrationInterface, QueryRunner} from 'typeorm';

export class AddAppmsImplementationManagerRole1564291508550 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<any> {
        await queryRunner.query(`
            INSERT INTO
              roles (id,name, slug, party, can_be_acted, can_act, is_pe_based, is_sector_based, is_vendor_based, is_record_based, system_id)
            VALUES
              (20, 'implementation-manager','appms-implementation-manager', 'pe', true, false, true, false, false, false, 4)
              `);
        await queryRunner.query(`
            INSERT INTO
              roles (id,name, slug, party, can_be_acted, can_act, is_pe_based, is_sector_based, is_vendor_based, is_record_based, system_id)
            VALUES
              (21, 'implementation-approver','appms-implementation-approver', 'pe', true, false, true, false, false, false, 4)
              `);
        await queryRunner.query(`
            insert into
              context_accesses (operation_id, is_enabled, role_id, context_id)
            select 1, 1, 20, id
            from contexts where slug in (
              'created-plans-count-widget',
              'confirmed-plans-count-widget',
              'published-plans-count-widget',
              'transferred-plans-count-widget',
              'implemented-plans-count-widget',
              'cancelled-plans-count-widget',
              'confirmed-plans-vs-created-plans-percentage-widget',
              'published-plans-vs-confirmed-plans-percentage-widget',
              'five-most-contingency-risks-predicted',
              'five-most-contingency-results-predicted',
              'five-most-frequent-cpv-divisions',
              'five-most-cpv-based-on-amount',
              'plan-vs-amount-project-based-on-province',
              'plan-vs-actual-projects-number-based-on-timeline-non-consultancy',
              'plan-vs-actual-projects-number-based-on-timeline-consultancy',
              'plan-vs-actual-project-number-and-amount-based-on-procurement-type',
              'plan-vs-actual-project-number-and-amount-based-on-contract-type',
              'plan-vs-actual-project-number-and-amount-based-on-budget-type',
              'plan-vs-actual-project-number-and-amount-based-on-procurement-preference',
              'plan-vs-actual-project-number-and-amount-based-on-selection-method',
              'plan-vs-actual-project-number-and-amount-based-on-procurement-method',
              'project-number-and-amount-based-on-award-authority-threshold',
              'plan-vs-actual-project-number-and-amount-based-on-plan-status',
              'project',
              'lot',
              'project-planning',
              'project-planning-procurement-plan-general-details',
              'project-planning-cpv',
              'project-planning-location-domestic',
              'project-planning-location-foreign',
              'project-planning-procurement-process-progress-timeline',
              'project-planning-potential-risks',
              'project-planning-documents',
              'project-planning-challenges',
              'project-planning-analysis',
              'project-implementation',
              'project-implementation-procurement-plan-general-details',
              'project-implementation-cpv',
              'project-implementation-location-domestic',
              'project-implementation-location-foreign',
              'project-implementation-procurement-process-progress-timeline',
              'project-implementation-potential-risk',
              'project-implementation-document',
              'project-implementation-challenge',
              'project-implementation-analysis'
              )`);
        await queryRunner.query(`
            insert into
              context_accesses (operation_id, is_enabled, role_id, context_id)
            select 2, 1, 20, id
            from contexts where slug in (
              'project-implementation-cpv',
              'project-implementation-location-domestic',
              'project-implementation-location-foreign',
              'project-implementation-potential-risk',
              'project-implementation-challenge'
              )`);
        await queryRunner.query(`
            insert into
              context_accesses (operation_id, is_enabled, role_id, context_id)
            select 3, 1, 20, id
            from contexts where slug in (
              'project',
              'lot',
              'project-planning-cpv',
              'project-planning-location-domestic',
              'project-planning-location-foreign',
              'project-planning-potential-risks',
              'project-planning-challenges',
              'project-implementation-cpv',
              'project-implementation-location-domestic',
              'project-implementation-location-foreign',
              'project-implementation-potential-risk',
              'project-implementation-challenge'
              )`);
        await queryRunner.query(`
            insert into
              context_accesses (operation_id, is_enabled, role_id, context_id)
            select 4, 1, 20, id
            from contexts where slug in (
              'project-implementation',
              'project-implementation-procurement-plan-general-details',
              'project-implementation-cpv',
              'project-implementation-location-domestic',
              'project-implementation-location-foreign',
              'project-implementation-procurement-process-progress-timeline',
              'project-implementation-potential-risk',
              'project-implementation-document',
              'project-implementation-challenge',
              'project-implementation-analysis'
              )`);
        await queryRunner.query(`
            insert into
              context_accesses (operation_id, is_enabled, role_id, context_id)
            select 6, 1, 20, id
            from contexts where slug in (
              'project-implementation-procurement-plan-general-details',
              'project-implementation-cpv',
              'project-implementation-location-domestic',
              'project-implementation-location-foreign',
              'project-implementation-procurement-process-progress-timeline',
              'project-implementation-potential-risk',
              'project-implementation-document',
              'project-implementation-challenge'
              )`);
        await queryRunner.query(`
            insert into
              context_accesses (operation_id, is_enabled, role_id, context_id)
            select 9, 1, 20, id
            from contexts where slug in (
              'project-implementation'
              )`);
        await queryRunner.query(`
            insert into
              context_accesses (operation_id, is_enabled, role_id, context_id)
            select 11, 1, 20, id
            from contexts where slug in (
              'project-implementation-procurement-plan-general-details',
              'project-implementation-cpv',
              'project-implementation-location-domestic',
              'project-implementation-location-foreign',
              'project-implementation-procurement-process-progress-timeline',
              'project-implementation-potential-risk',
              'project-implementation-document',
              'project-implementation-challenge'
              )`);
        await queryRunner.query(`
            insert into
              context_accesses (operation_id, is_enabled, role_id, context_id)
            select 12, 1, 20, id
            from contexts where slug in (
              'project-implementation-procurement-plan-general-details',
              'project-implementation-cpv',
              'project-implementation-location-domestic',
              'project-implementation-location-foreign',
              'project-implementation-procurement-process-progress-timeline',
              'project-implementation-potential-risk',
              'project-implementation-document',
              'project-implementation-challenge'
              )`);


        // await queryRunner.query(`
        //     delete from context_accesses where
        //     operation_id in
        //         (select id from operations where slug in (
        //             'delete',
        //             'confirm',
        //             'generally-confirm',
        //             'create',
        //             'update',
        //             'cancel-request',
        //             'renew-request',
        //             'transfer-request'
        //         ))
        //     and context_id in
        //         (select id from contexts where slug in (
        //             'project-implementation',
        //             'project-implementation-procurement-plan-general-details',
        //             'project-implementation-cpv',
        //             'project-implementation-location-domestic',
        //             'project-implementation-location-foreign',
        //             'project-implementation-procurement-process-progress-timeline',
        //             'project-implementation-potential-risk',
        //             'project-implementation-document',
        //             'project-implementation-challenge'
        //         )) and role_id = (select id from roles where slug = 'appms-procurement-plan-manager')
        //     `);


// Implementation approvar

        await queryRunner.query(`
            insert into
              context_accesses (operation_id, is_enabled, role_id, context_id)
            select 1, 1, 21, id
            from contexts where slug in (
              'created-plans-count-widget',
              'confirmed-plans-count-widget',
              'published-plans-count-widget',
              'transferred-plans-count-widget',
              'implemented-plans-count-widget',
              'cancelled-plans-count-widget',
              'confirmed-plans-vs-created-plans-percentage-widget',
              'published-plans-vs-confirmed-plans-percentage-widget',
              'five-most-contingency-risks-predicted',
              'five-most-contingency-results-predicted',
              'five-most-frequent-cpv-divisions',
              'five-most-cpv-based-on-amount',
              'plan-vs-amount-project-based-on-province',
              'plan-vs-actual-projects-number-based-on-timeline-non-consultancy',
              'plan-vs-actual-projects-number-based-on-timeline-consultancy',
              'plan-vs-actual-project-number-and-amount-based-on-procurement-type',
              'plan-vs-actual-project-number-and-amount-based-on-contract-type',
              'plan-vs-actual-project-number-and-amount-based-on-budget-type',
              'plan-vs-actual-project-number-and-amount-based-on-procurement-preference',
              'plan-vs-actual-project-number-and-amount-based-on-selection-method',
              'plan-vs-actual-project-number-and-amount-based-on-procurement-method',
              'project-number-and-amount-based-on-award-authority-threshold',
              'plan-vs-actual-project-number-and-amount-based-on-plan-status',
              'project',
              'lot',
              'project-planning',
              'project-planning-procurement-plan-general-details',
              'project-planning-cpv',
              'project-planning-location-domestic',
              'project-planning-location-foreign',
              'project-planning-procurement-process-progress-timeline',
              'project-planning-potential-risk',
              'project-planning-document',
              'project-planning-challenge',
              'project-planning-analysis',
              'project-implementation',
              'project-implementation-procurement-plan-general-details',
              'project-implementation-cpv',
              'project-implementation-location-domestic',
              'project-implementation-location-foreign',
              'project-implementation-procurement-process-progress-timeline',
              'project-implementation-potential-risk',
              'project-implementation-document',
              'project-implementation-challenge',
              'project-implementation-analysis'
              )`);
        await queryRunner.query(`
            insert into
              context_accesses (operation_id, is_enabled, role_id, context_id)
            select 3, 1, 21, id
            from contexts where slug in (
              'project',
              'lot',
              'project-planning-cpv',
              'project-planning-location-domestic',
              'project-planning-location-foreign',
              'project-planning-potential-risk',
              'project-planning-challenge',
              'project-implementation-cpv',
              'project-implementation-location-domestic',
              'project-implementation-location-foreign',
              'project-implementation-potential-risk',
              'project-implementation-challenge'
              )`);
        await queryRunner.query(`
            insert into
              context_accesses (operation_id, is_enabled, role_id, context_id)
            select 4, 1, 21, id
            from contexts where slug in (
              'project-planning',
              'project-planning-procurement-plan-general-details',
              'project-planning-cpv',
              'project-planning-location-domestic',
              'project-planning-location-foreign',
              'project-planning-procurement-process-progress-timeline',
              'project-planning-potential-risk',
              'project-planning-document',
              'project-planning-challenge',
              'project-planning-analysis',
              'project-implementation',
              'project-implementation-procurement-plan-general-details',
              'project-implementation-cpv',
              'project-implementation-location-domestic',
              'project-implementation-location-foreign',
              'project-implementation-procurement-process-progress-timeline',
              'project-implementation-potential-risk',
              'project-implementation-document',
              'project-implementation-challenge',
              'project-implementation-analysis'
              )`);
        await queryRunner.query(`
            insert into
              context_accesses (operation_id, is_enabled, role_id, context_id)
            select 5, 1, 21, id
            from contexts where slug in (
              'project-implementation-procurement-plan-general-details',
              'project-implementation-cpv',
              'project-implementation-location-domestic',
              'project-implementation-location-foreign',
              'project-implementation-procurement-process-progress-timeline',
              'project-implementation-potential-risk',
              'project-implementation-document',
              'project-implementation-challenge'
              )`);
        await queryRunner.query(`
            insert into
              context_accesses (operation_id, is_enabled, role_id, context_id)
            select 8, 1, 21, id
            from contexts where slug in (
              'project-implementation'
              )`);
        // await queryRunner.query(`
        //     delete from context_accesses where
        //     operation_id in
        //         (select id from operations where slug in (
        //             'approve',
        //             'generally-approve'
        //         ))
        //     and context_id in
        //         (select id from contexts where slug in (
        //               'project-implementation-procurement-plan-general-details',
        //               'project-implementation',
        //               'project-implementation-cpv',
        //               'project-implementation-location-domestic',
        //               'project-implementation-location-foreign',
        //               'project-implementation-procurement-process-progress-timeline',
        //               'project-implementation-potential-risk',
        //               'project-implementation-document',
        //               'project-implementation-challenge'
        //         )) and role_id = (select id from roles where slug = 'appms-award-authority')
        //     `);
    }

    public async down(queryRunner: QueryRunner): Promise<any> {
        await queryRunner.query(`
            delete from context_accesses where role_id = (select id from roles where slug = 'appms-implementation-manager')
            `);
        await queryRunner.query(`
            delete from roles where slug = 'appms-implementation-manager'
            `);
        // await queryRunner.query(`
        //     insert into
        //       context_accesses (operation_id, is_enabled, role_id, context_id)
        //     select 2, 1, 12, id
        //     from contexts where slug in (
        //       'project-implementation-cpv',
        //       'project-implementation-location-domestic',
        //       'project-implementation-location-foreign',
        //       'project-implementation-potential-risk',
        //       'project-implementation-challenge'
        //       )`);
        // await queryRunner.query(`
        //     insert into
        //       context_accesses (operation_id, is_enabled, role_id, context_id)
        //     select 6, 1, 12, id
        //     from contexts where slug in (
        //       'project-implementation-procurement-plan-general-details',
        //       'project-implementation-cpv',
        //       'project-implementation-location-domestic',
        //       'project-implementation-location-foreign',
        //       'project-implementation-procurement-process-progress-timeline',
        //       'project-implementation-potential-risk',
        //       'project-implementation-document',
        //       'project-implementation-challenge'
        //       )`);
        // await queryRunner.query(`
        //     insert into
        //       context_accesses (operation_id, is_enabled, role_id, context_id)
        //     select 11, 1, 12, id
        //     from contexts where slug in (
        //       'project-implementation-procurement-plan-general-details',
        //       'project-implementation-cpv',
        //       'project-implementation-location-domestic',
        //       'project-implementation-location-foreign',
        //       'project-implementation-procurement-process-progress-timeline',
        //       'project-implementation-potential-risk',
        //       'project-implementation-document',
        //       'project-implementation-challenge'
        //       )`);
        // await queryRunner.query(`
        //     insert into
        //       context_accesses (operation_id, is_enabled, role_id, context_id)
        //     select 12, 1, 12, id
        //     from contexts where slug in (
        //       'project-implementation-procurement-plan-general-details',
        //       'project-implementation-cpv',
        //       'project-implementation-location-domestic',
        //       'project-implementation-location-foreign',
        //       'project-implementation-procurement-process-progress-timeline',
        //       'project-implementation-potential-risk',
        //       'project-implementation-document',
        //       'project-implementation-challenge'
        //       )`);
        // await queryRunner.query(`
        //     insert into
        //       context_accesses (operation_id, is_enabled, role_id, context_id)
        //     select 9, 1, 12, id
        //     from contexts where slug in (
        //       'project-implementation'
        //       )`);

        await queryRunner.query(`
            delete from context_accesses where role_id = (select id from roles where slug = 'appms-implementation-approver')
            `);
        await queryRunner.query(`
            delete from roles where slug = 'appms-implementation-approver'
            `);
        // await queryRunner.query(`
        //     insert into
        //       context_accesses (operation_id, is_enabled, role_id, context_id)
        //     select 5, 1, 13, id
        //     from contexts where slug in (
        //       'project-implementation-procurement-plan-general-details',
        //       'project-implementation-cpv',
        //       'project-implementation-location-domestic',
        //       'project-implementation-location-foreign',
        //       'project-implementation-procurement-process-progress-timeline',
        //       'project-implementation-potential-risk',
        //       'project-implementation-document',
        //       'project-implementation-challenge'
        //       )`);
        // await queryRunner.query(`
        //     insert into
        //       context_accesses (operation_id, is_enabled, role_id, context_id)
        //     select 8, 1, 13, id
        //     from contexts where slug in (
        //       'project-implementation'
        //       )`);


    }

}
