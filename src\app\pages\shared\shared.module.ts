import {CommonModule} from '@angular/common';
import {
    MAT_DATE_FORMATS, MatButtonModule, MatCardModule, MatCheckboxModule, MatDialogModule, MatIconModule, MatInputModule, MatProgressBarModule, MatProgressSpinnerModule, MatSnackBarModule,
    MatTableModule, MatToolbarModule, MatTooltipModule
} from '@angular/material';

import {FormsModule, ReactiveFormsModule} from '@angular/forms';
import {NgModule} from '@angular/core';
import {NotificationDialogComponent} from './notification-dialog/notification-dialog.component';
import {SnackbarMessageComponent} from './snackbar-message/snackbar-message.component';
import {FlexLayoutModule} from '@angular/flex-layout';
import {MaterialModule} from '../../../material.module';
import {MatMomentDateModule} from '@angular/material-moment-adapter';
import {JalaliPipe} from './pipes/jalali.pipe';
import {JalaliDateTimePipe} from './pipes/jalali-date-time.pipe';
import {NotificationsService} from './services/notifications.service';
import {ViewElementComponent} from './components/view-element/view-element.component';
import {ChartFlipComponent} from './components/chart-flip/chart-flip.component';
import {UnauthorizedAccessComponent} from './components/errors-handling/unauthorized-access/unauthorized-access.component';
import {FormService} from './services/form.service';
import {NotFoundComponent} from './components/errors-handling/not-found/not-found.component';
import {NpaDateTimePickerComponent} from './components/npa-date-time-picker/npa-date-time-picker.component';
import {ConfirmationDialogComponent} from './confirmation-dialog/confirmation-dialog.component';
import {ConfirmationAndJustificationService} from './services/confirmation-and-justification.service';
import {ConfirmationAndJustificationDialogComponent} from './confirmation-and-justification-dialog/confirmation-and-justification-dialog.component';
import {ConfirmationService} from './services/confirmation.service';
import {VerificationComponent} from './components/verification/verification.component';
import {VerificationService} from './components/verification/verification.service';
import {TranslateLoader, TranslateModule} from '@ngx-translate/core';
import {HttpClient} from '@angular/common/http';
import {TranslateHttpLoader} from '@ngx-translate/http-loader';
import {FileDownloadComponent} from './components/file-download/file-download.component';
import {FileService} from './services/file.service';
import {NPA_DATE_FORMATS} from './services/date.service';
import {TranslateService} from './services/translate.service';
import {FileUploaderComponent} from './components/file-uploader/file-uploader.component';
import {FileDownloadDialogComponent} from './components/file-download/file-download-dialog/file-download-dialog.component';
import {NpaScorecardComponent} from './components/npa-scorecard/npa-scorecard.component';
import {TitleValueWidgetComponent} from './components/title-value-widget/title-value-widget.component';
import {KeyValuePipe} from './pipes/key-value.pipe';

@NgModule({
    imports: [
        MatIconModule,
        MatCheckboxModule,
        CommonModule,
        FormsModule,
        MatToolbarModule,
        MatTooltipModule,
        MatButtonModule,
        MatInputModule,
        MatDialogModule,
        MatSnackBarModule,
        MatProgressSpinnerModule,
        MatProgressBarModule,
        FlexLayoutModule,
        MaterialModule,
        MatCardModule,
        MatMomentDateModule,
        MatTableModule,
        ReactiveFormsModule,
        TranslateModule.forRoot({
            loader: {
                provide: TranslateLoader,
                useFactory: (createTranslateLoader),
                deps: [HttpClient]
            }

        }),

    ],
    declarations: [
        SnackbarMessageComponent,
        NotificationDialogComponent,
        JalaliPipe,
        KeyValuePipe,
        JalaliDateTimePipe,
        ViewElementComponent,
        ChartFlipComponent,
        UnauthorizedAccessComponent,
        NotFoundComponent,
        NpaDateTimePickerComponent,
        ConfirmationDialogComponent,
        ConfirmationAndJustificationDialogComponent,
        VerificationComponent,
        FileUploaderComponent,
        FileDownloadComponent,
        FileDownloadDialogComponent,
        NpaScorecardComponent,
        TitleValueWidgetComponent
    ],
    exports: [
        MatMomentDateModule,
        JalaliPipe,
        KeyValuePipe,
        JalaliDateTimePipe,
        ViewElementComponent,
        ChartFlipComponent,
        UnauthorizedAccessComponent,
        NpaDateTimePickerComponent,
        ConfirmationAndJustificationDialogComponent,
        MatProgressBarModule,
        VerificationComponent,
        TranslateModule,
        FileUploaderComponent,
        FileDownloadComponent,
        FileDownloadDialogComponent,
        NpaScorecardComponent,
        TitleValueWidgetComponent
    ],
    entryComponents: [
        SnackbarMessageComponent,
        NotificationDialogComponent,
        ConfirmationDialogComponent,
        ConfirmationAndJustificationDialogComponent,
        FileDownloadDialogComponent
    ],
    providers: [
        NotificationsService,
        FormService,
        {
            provide: MAT_DATE_FORMATS,
            useValue: NPA_DATE_FORMATS
        },
        ConfirmationAndJustificationService,
        ConfirmationService,
        VerificationService,
        FileService,
        TranslateService
    ]
})


export class SharedModule {
}

export function createTranslateLoader(http: HttpClient) {
    return new TranslateHttpLoader(http, '../../assets/i18n/', '.json');
}
