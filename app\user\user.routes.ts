import {UserController} from './user.controller';
import {Validator} from '../services/validator.service';
import {IRoute} from '../app.routes';
import {Authentication} from '../middlewares/authentication';

export const USER_ROUTES: IRoute[] = [
    {
        url: '',
        httpMethod: 'get',
        controllerMethod: UserController.getInstance().index,
        validatorsList: [],
        middleware: [Authentication.validateToken]
    },

    {
        url: '',
        httpMethod: 'post',
        controllerMethod: UserController.getInstance().store,
        validatorsList: Validator.user.onRegister,
    },
    {
        url: '',
        httpMethod: 'put',
        controllerMethod: UserController.getInstance().update,
        validatorsList: [Validator.user.onSystemUserRegister],
        middleware: [Authentication.validateToken]
    },
    {
        url: '/change-password',
        httpMethod: 'put',
        controllerMethod: UserController.getInstance().changePassword,
        validatorsList: Validator.user.verifyPassword,
    },
    {
        url: '/reset-password',
        httpMethod: 'put',
        controllerMethod: UserController.getInstance().resetPassword,
        validatorsList: Validator.user.onPasswordReset,
    },
    {
        url: '/:lang/recover-password',
        httpMethod: 'post',
        controllerMethod: UserController.getInstance().recoverPassword,
    },
    {
        url: '/create-vendor',
        httpMethod: 'post',
        controllerMethod: UserController.getInstance().createVendor,
        validatorsList: Validator.user.onSystemUserRegister,
    },
    {
        url: '/create-acpms-user',
        httpMethod: 'post',
        controllerMethod: UserController.getInstance().createACPMSUser,
    },
    {
        url: '/create-appms-user',
        httpMethod: 'post',
        controllerMethod: UserController.getInstance().createAPPMSUser,
    },
    {
        url: '/create-user',
        httpMethod: 'post',
        controllerMethod: UserController.getInstance().createUser,
    },
    {
        url: '/:id',
        httpMethod: 'get',
        controllerMethod: UserController.getInstance().show,
        validatorsList: [],
        middleware: [Authentication.validateToken]
    },
    {
        url: '/:id',
        httpMethod: 'put',
        controllerMethod: UserController.getInstance().update,
        validatorsList: [Validator.user.onSystemUserRegister],
        middleware: [Authentication.validateToken]
    },
    {
        url: '/:id',
        httpMethod: 'delete',
        controllerMethod: UserController.getInstance().destroy,
        validatorsList: [],
        middleware: [Authentication.validateToken]
    }
];
