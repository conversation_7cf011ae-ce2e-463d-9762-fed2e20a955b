<mat-expansion-panel>
    <mat-expansion-panel-header>
        <mat-panel-title>{{ "SEARCH" | translate}}</mat-panel-title>
    </mat-expansion-panel-header>
    <form [formGroup]="form" fxLayout="row wrap"
          fxLayoutAlign="start center" class="search-options"
          #f="ngForm">
        <mat-form-field fxFlex='25'>
            <input
                    matInput
                    type="text"
                    formControlName="user_identification_number"
                    placeholder="{{'USER_IDENTIFICATION_NUMBER'| translate}}"
                    lang="en">
        </mat-form-field>
        <mat-action-row fxLayout="row " fxLayoutAlign="start" fxFlex='100'>
            <div class="search" fxFlex="50">
                <button mat-raised-button
                        color="primary"
                        type="submit">{{"SEARCH" | translate}}
                </button>
                <button type="button"
                        mat-button
                        (click)="this.form.reset()">{{"RESET" | translate}}
                </button>
            </div>
            <div class="actions" fxLayout="row " fxLayoutAlign="end" fxFlex='50'>
                <button mat-button type="button">
                    {{"DOWNLOAD" | translate}}
                </button>
            </div>
        </mat-action-row>
    </form>
</mat-expansion-panel>
