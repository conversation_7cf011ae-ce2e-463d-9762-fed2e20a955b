import {MigrationInterface, QueryRunner} from 'typeorm';

export class TriggerToAllowOnlyOneVendorInformationApprovalRoleInUserRoleTable1548913608524 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<any> {
        await queryRunner.query('drop trigger if exists `allow_only_one_role_for_vendor_information_approval_on_insert`');
        await queryRunner.query('' +
            ' create trigger `allow_only_one_role_for_vendor_information_approval_on_insert`\n' +
            '        before insert on `user_roles`\n' +
            '        for each row\n' +
            '        begin\n' +
            '            declare var_roles_id integer default null;\n' +
            '           declare var_user_roles_role_id integer default null;\n' +
            '            select id into var_roles_id\n' +
            '                from roles \n' +
            '                where slug = \'avrcs-vendor-information-approval\';\n' +
            '           select role_id into var_user_roles_role_id from user_roles \n' +
            '                where role_id = var_roles_id;\n' +
            '            if var_user_roles_role_id = new.role_id\n' +
            '            then     \n' +
            '            \tsignal sqlstate  \'45000\' set message_text = \'|||NPA-USM-0002|||\';\n' +
            '            end if;\n' +
            '        end;');

        await queryRunner.query('drop trigger if exists `allow_only_one_role_for_vendor_information_approval_on_update`');
        await queryRunner.query('' +
            ' create trigger `allow_only_one_role_for_vendor_information_approval_on_update`\n' +
            '        before update on `user_roles`\n' +
            '        for each row\n' +
            '        begin\n' +
            '            declare var_roles_id integer default null;\n' +
            '           declare var_user_roles_role_id integer default null;\n' +
            '            select id into var_roles_id\n' +
            '                from roles \n' +
            '                where slug = \'avrcs-vendor-information-approval\';\n' +
            '           select role_id into var_user_roles_role_id from user_roles \n' +
            '                where role_id = var_roles_id;\n' +
            '            if var_user_roles_role_id = new.role_id\n' +
            '            then     \n' +
            '            \tsignal sqlstate  \'45000\' set message_text = \'|||NPA-USM-0002|||\';\n' +
            '            end if;\n' +
            '        end;');
    }

    public async down(queryRunner: QueryRunner): Promise<any> {
        await queryRunner.query('drop trigger if exists `allow_only_one_role_for_vendor_information_approval_on_insert`');
        await queryRunner.query('drop trigger if exists `allow_only_one_role_for_vendor_information_approval_on_update`');
    }

}
