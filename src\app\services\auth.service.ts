import {Injectable} from '@angular/core';
import {environment} from '../../environments/environment';
import {of as observableOf, Observable, BehaviorSubject} from 'rxjs';
import {IRole, IUser} from './auth.types';
import {HttpService} from './http.service';

@Injectable()
export class AuthService {
    defaultUserLoggedInUrl: string = null;

    public user = new BehaviorSubject<IUser>(undefined);
    public isUserAuthenticated = environment.loginThroughDataBase;
    private _loggedInUser: IUser;
    private _rolesList: IRole[];
    public passwordPattern = /^(?=.*?[A-Z])(?=.*?[a-z])(?=.*?[0-9])(?=.*?[#?!@$%^&*-]).{8,}$/;
    public usernamePattern = /^([a-zA-Z]+[_\-\.]?[a-zA-Z]+)+$/;

    constructor(private _http: HttpService) {
        if (!environment.production) {
            if (!environment.loginThroughDataBase) {
                this.allCredentials = environment.logInData.user;
                this._rolesList = environment.logInData.roles;
                return;
            }
        }
    }

    clearAllCredentials() {
        localStorage.removeItem('session');
        this.isUserAuthenticated = false;
        this.user.next(undefined);
        this._loggedInUser = undefined;
        this._rolesList = undefined;

        return true;
    }

    get loggedInUser() {
        return this._loggedInUser;
    }

    set actingRole(role) {
        this.defaultUserLoggedInUrl = `/${localStorage.getItem('lang')}/${role.slug}/`;
        const temp = this.user.value;
        temp.role = role;
        this.user.next(temp);
    }

    get roles() {
        return this._rolesList;
    }

    set roles(roles) {
        this._rolesList = roles;
    }

    get token() {
        return localStorage.getItem('token');
    }

    get allAccessibleRoles(): IRole[] {
        if (this.loggedInUser.role.can_act || environment.production === false) {
            return environment.production && environment.loginThroughDataBase ? this._rolesList.filter(role => {
                return role.can_be_acted || this._loggedInUser.role === role;
            }) : this._rolesList;
        }
    }

    set allCredentials(userCredentials: IUser) {
        this._loggedInUser = userCredentials;
        this.user.next(Object.assign({}, userCredentials));
    }

        logoutAPI(): Observable<any> {
        return this._http.get('api/revoke-session');
    }


    getUserContexts(roleId: number): Observable<any[]> {
        if (environment.production || environment.loginThroughDataBase) {
            return this._http.get('api/contexts?role_id=' + roleId);
        } else {
            for (let i = 0; i < environment.contextsList.length; i++) {
                if (environment.contextsList[i].roleId === roleId) {
                    return observableOf(environment.contextsList[i].accessibleContexts);
                }
            }
        }
    }

}

