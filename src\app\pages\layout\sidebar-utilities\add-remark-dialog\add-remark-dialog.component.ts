import {Component, OnInit} from '@angular/core';
import {NotificationsService} from '../../../shared/services/notifications.service';
import {FormService} from '../../../shared/services/form.service';
import {FormBuilder, FormGroup} from '@angular/forms';
import {FormValidationService} from '../../../../services/form-validation.service';

@Component({
    selector: 'npa-add-remark-dialog',
    templateUrl: './add-remark-dialog.component.html',
    styleUrls: ['./add-remark-dialog.component.styl']
})
export class AddRemarkDialogComponent implements OnInit {
    data;
    form: FormGroup;
    alert_subcategories = [
        {
            id: 1,
            name_da: 'test1',
        },
        {
            id: 2,
            name_da: 'test2',
        },
        {
            id: 3,
            name_da: 'test3',
        },
    ];

    constructor(public notificationsService: NotificationsService,
                public formValidationService: FormValidationService,
                public formService: FormService,
                private _formBuilder: FormBuilder) {
    }

    ngOnInit() {
        this.initForm();
    }

    initForm() {
        this.form = this._formBuilder.group({
            title: [''],
            contents: [''],
            is_selected: [''],
            message: [''],
        });
    }
}
