import {Request, Response} from 'express';

export interface IController {
    index: (req: Request, res: Response) => Promise<Response> | any;
    store: (req: Request, res: Response) => Promise<Response> | any;
    show: (req: Request, res: Response) => Promise<Response> | any;
    update: (req: Request, res: Response) => Promise<Response> | any;
    destroy: (req: Request, res: Response) => Promise<Response> | any;
}
