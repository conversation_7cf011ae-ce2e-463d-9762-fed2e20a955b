<div class="container no-select" [lang] ="lang"
     [ngClass]="{ 'sidebar-main-close': isSidebarMainClose, 'sticky': scrollTop > 0, 'full-width': isSidebarMainClose, 'sidebar-utilities-close': isSidebarUtilitiesClose}">
    <div class="upperRow">
        <div class="breadcrumb">
            <a class="breadcrumb-name" href="#" [routerLink]="[contextService.context, 'dashboard']">
                <mat-icon class="home">home</mat-icon>
            </a>
            <a class="breadcrumb-name" href="#" *ngFor="let item of routeUrl" [routerLink]="[item.url]">
                <div *ngIf="item.url !== 'dashboard'">
                    <mat-icon>{{ arrowIcon }}</mat-icon>
                    <span *ngIf="item.key" class="name">{{item.key | translate}}</span>
                    <span *ngIf="!item.key" class="name english-font">{{decodeURIComponent(item.name)}}</span>
                </div>
            </a>
        </div>
        <span class="contract-spec no-select" (click)="openProjectGeneralInfoDialog()">
            <span class="english-font">{{ complaintIdentificationNumber }}</span>
        </span>
    </div>
    <div class="lowerRow">
        <span class="title no-select">{{
            routeUrl !== [] ?
            routeUrl[routeUrl.length - 1]?.key ? (routeUrl[routeUrl.length - 1]?.key | translate) : decode(routeUrl[routeUrl.length - 1]?.name)
            : '' }}</span>
    </div>
</div>

