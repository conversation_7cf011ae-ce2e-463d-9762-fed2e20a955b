import {RouterModule, Routes} from '@angular/router';
import {DashboardComponent} from './dashboard.component';
import {ChartsComponent} from './charts/charts.component';
import {UserListComponent} from './user-list/user-list.component';
import {DashboardSystemListComponent} from './dashboard-system-list/dashboard-system-list.component';

export const dashboardComponents = [
    DashboardComponent,
    ChartsComponent,
    UserListComponent,
    DashboardSystemListComponent
];
const routes: Routes = [
    {
        path: '',
        component: DashboardComponent,
        children: [
            {
                path: '',
                component: DashboardSystemListComponent,
            },
        ]
    },
    {
        path: 'charts',
        component: ChartsComponent,
    },

    {
        path: 'user-list',
        component: UserListComponent
    },
    {
        path: 'user-list/:id',
        children: [
            {
                path: '',
                redirectTo: 'complaint',
                pathMatch: 'full'
            },
            {
                path: 'reports',
                loadChildren: 'app/pages/reports/reports.module#ReportsModule',
            },
            {
                path: 'systems',
                loadChildren: 'app/pages/systems/systems.module#SystemsModule',
            },
            {
                path: 'sessions',
                loadChildren: 'app/pages/sessions/sessions.module#SessionsModule',
            }
        ]
    }
];

export const DashboardRoutes = RouterModule.forChild(routes);
