import * as express from 'express';
import * as http from 'http';
import 'reflect-metadata';
import {DefaultMiddleware} from './middlewares/middleware';
import {Routes} from './app.routes';
import {Database} from './config/database';
import {JwtTokenService} from './services/jwt-token.service';
import * as path from 'path';
import * as env from 'dotenv';

env.load({path: path.resolve(__dirname, '../.env')});

class App {
    public express: express.Application;
    public server: http.Server;

    constructor() {
        this.init();
    }

    init() {
        this.express = express();

        this.express.use(DefaultMiddleware);
        JwtTokenService.generateRSAs();

        Database.connect()
            .then(connection => {
                if (connection.isConnected) {
                    if (process.env.APP_ENV === 'production') {
                        this.express.use(express.static(path.join(__dirname, '../dist')));
                    }
                    this.express.use(Routes);
                    this.express.use('/api/*', (req, res) => {
                        return res.status(404).json();
                    });
                    this.express.use(/^\/assets\/(?!(styles.*|inline.*|polyfills.*|main.*)).*/, (req, res) => {
                        res.status(404).json();
                    });
                    if (process.env.APP_ENV === 'production') {
                        this.express.use('*', (req, res) => {
                            res.sendFile(path.join(__dirname, '../dist/index.html'));
                        });
                    }
                    console.log('Database is connected...');
                } else {
                    console.error('Database connection failed...');
                    return;
                }

            }).catch(error => console.log(error));

        this.server = http.createServer(this.express);
    }

}

export const server = new App().server;
