import {BaseEntity, Column, Entity, Index, JoinColumn, ManyToOne, OneToOne, PrimaryGeneratedColumn} from 'typeorm';
import {UserRole} from './user-role';
import {PeResourceNotApplicable} from './pe-resource-not-applicable';

@Entity('user_role_pes')
@Index('unique_procurement_entity_id_and_user_role_id', ['procurement_entity_id', 'user_role_id'], {unique: true})
export class UserRolePe extends BaseEntity implements IUserRolePe {

    @PrimaryGeneratedColumn()
    id: number;

    @Column({name: 'procurement_entity_id'})
    procurement_entity_id: number;

    @Column({name: 'user_role_id'})
    user_role_id: number;

    @ManyToOne(type => UserRole, userRole => userRole.id, {onDelete: 'CASCADE'})
    @JoinColumn({name: 'user_role_id'})
    userRole: UserRole;

    @OneToOne(type => PeResourceNotApplicable,
        peResourceNotApplicable => peResourceNotApplicable.userRolePe) // specify inverse side as a second parameter
    peResourceNotApplicable: PeResourceNotApplicable;

}

export interface IUserRolePe {
    id?: number;
    procurement_entity_id: number;
    user_role_id: number;
    userRole: any | UserRole;
    peResourceNotApplicable: PeResourceNotApplicable | any;
}



