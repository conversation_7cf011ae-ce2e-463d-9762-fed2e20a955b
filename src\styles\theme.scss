@import '~@angular/material/theming';
@import 'usm.palette';
// Plus imports for other components in your app.

// Include the common styles for Angular Material. We include this here so that you only
// have to load a single css file for Angular Material in your app.
// Be sure that you only ever include this mixin once!
@include mat-core();

// Define the palettes for your theme using the Material Design palettes available in palette.scss
// (imported above). For each palette, you can optionally specify a default, lighter, and darker
// hue.
$npa-app-primary: mat-palette($mat-usm-primary);
$npa-app-accent: mat-palette($mat-usm-secondary, A200, A100, A400);

// The warn palette is optional (defaults to red).
$npa-app-warn: mat-palette($mat-red);

// Create the theme object (a Sass map containing all of the palettes).
$npa-app-theme: mat-light-theme($npa-app-primary, $npa-app-accent, $npa-app-warn);

// Include theme styles for core and each component used in your app.
// Alternatively, you can import and @include the theme mixins for each component
// that you are using.
@include angular-material-theme($npa-app-theme);
