import {BaseEntity, Column, Entity, Index, PrimaryGeneratedColumn} from 'typeorm';

@Entity('statuses')
@Index('unique_slug', ['slug'], { unique: true })

export class Status extends BaseEntity implements IStatus {

    @PrimaryGeneratedColumn()
    id: number;

    @Column({unique: true})
    slug: string;

    @Column()
    description: string;

}

export interface IStatus {
    id?: number;
    slug: string;
    description: string;
}

