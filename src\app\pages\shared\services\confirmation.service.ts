import {Injectable} from '@angular/core';
import {ConfirmationDialogComponent} from '../confirmation-dialog/confirmation-dialog.component';
import {MatDialog} from '@angular/material';


import {IConfirmationDialogInput, IConfirmationDialogOutput} from '../types/confirmation.type';
import {BehaviorSubject} from 'rxjs';
import {map} from 'rxjs/operators';

@Injectable()

export class ConfirmationService {

    constructor(private _dialog: MatDialog) {

    }

    progressBarValue = new BehaviorSubject<boolean>(false);

    openDialog(data: IConfirmationDialogInput): Promise<IConfirmationDialogOutput> {
        const dialogRef = this._dialog.open(ConfirmationDialogComponent, {
            width: '250px',
            data: data,
            disableClose: true,
        });

        return dialogRef
            .afterClosed()
            .pipe(map(result => {
                return result;
            })).toPromise().catch();

    }
}
