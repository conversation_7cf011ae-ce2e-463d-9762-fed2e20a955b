import {Component, OnInit} from '@angular/core';
import {NotificationsService} from '../../shared/services/notifications.service';
import {TranslateService} from '../../shared/services/translate.service';

@Component({
    selector: 'npa-change-log',
    templateUrl: './change-log.component.html',
    styleUrls: ['./change-log.component.styl']
})
export class ChangeLogComponent implements OnInit {

    constructor(private _notificationsService: NotificationsService,
                public translate: TranslateService
    ) {
    }

    ngOnInit() {
        this._notificationsService.dismissLoading();
    }
}
