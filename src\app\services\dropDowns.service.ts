import {Injectable} from '@angular/core';
import {HttpService} from './http.service';
import {HttpErrorResponse} from '@angular/common/http';
import {forkJoin as observableForkJoin, of as observableOf, Observable, BehaviorSubject} from 'rxjs';
import {map} from 'rxjs/operators';

@Injectable()
export class DropDownsService {

    dropDowns = [
    ];

    constructor(private _http: HttpService) {
    }

    setDropDowns(): Observable<any> {
        return this._http.get(`api/dropDown/lastUpdateTimestamp`).pipe(
            map((response) => {
                const actualValue = response.value;
                const storedValue = localStorage.getItem('lastUpdateTimestamp');
                const applicationInitializedOn = localStorage.getItem('applicationInitializedOn');
                if (!!applicationInitializedOn && actualValue === storedValue) {
                    return;
                }
                localStorage.setItem('lastUpdateTimestamp', actualValue);
                return this.performTheProcess();
            },
            (error: HttpErrorResponse) => {
                console.error(error);
            }
        ));
    }

    getAll(tableName: string) {
        return JSON.parse(localStorage.getItem(tableName));
    }

    get(tableName: string, id: number) {
        const items = JSON.parse(localStorage.getItem(tableName));
        const item = items.filter(current => {
            if (current.id === id) {
                return true;
            }
            return false;
        });
        return item[0];
    }

    getBySlug(tableName: string, slug: string) {
        if (slug == null) {
            return false;
        }
        const items = JSON.parse(localStorage.getItem(tableName));
        const item = items.filter(current => {
            if (current.slug === slug) {
                return true;
            }
            return false;
        });
        return item[0];
    }

    getWithCondition(tableName: string, fieldName: string, fieldValue: string | number): any[] {
        const returnArray = [];
        const items = JSON.parse(localStorage.getItem(tableName));
        items.forEach(current => {
            if (current[fieldName] === fieldValue) {
                returnArray.push(current);
            }
        });
        return returnArray;
    }

    private performTheProcess(): Observable<any> {
        const allCalls = [];
        for (const dropDown of this.dropDowns) {
            const oneCall = this._http.get(`api/dropDown?path=${encodeURIComponent(dropDown.path)}`)
                .pipe(map(currencies => {
                    localStorage.setItem(dropDown.name, JSON.stringify(currencies));
                    return observableOf(true);
                }, (error: HttpErrorResponse) => {
                    console.error(error);
                }));
            allCalls.push(oneCall);
        }

        const a = new BehaviorSubject(true);
        observableForkJoin(Object.keys(allCalls).map(keys => allCalls[keys])).subscribe(() => {
            localStorage.setItem('applicationInitializedOn', (new Date()).getTime() + '');
            a.next(true);
        }, (error: any) => {
            alert('خطا در عیار سازی سیستم. لطفاً صفحه را بازنشانی مجدد نمایید.');
            console.error(error);
            window.stop();
        });
        return a;
    }

}
