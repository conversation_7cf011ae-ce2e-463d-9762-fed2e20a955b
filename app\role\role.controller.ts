import {Request, Response} from 'express';
import {InstanceService} from '../services/instance.service';
import {Model} from '../config/database';
import {IController} from '../types/controller';
import {IRole, Role} from '../entity/role';
import {ISystem, System} from '../entity/system';


export class RoleController implements IController {
    public static instance: RoleController;

    public static getInstance(): RoleController {
        return InstanceService.getInstance(RoleController);

    }

    public async index(req: Request, res: Response) {
        const systemName = req.query['system'];
        const roleSlug = req.query['slug'];
        try {
            if (systemName) {
                const system = <ISystem | any>await Model(System).findOne({slug: systemName});
                const data = await Model(Role).find({system_id: system.id});
                if (data.length !== 0) {
                    return res.status(200).json(data);
                }
                return res.status(404).json(data);
            }
            if (roleSlug) {
                const role = await Model(Role).findOne({slug: roleSlug});
                return res.status(200).json(role);
            }
            const roles = (await Model(Role).find()).filter((r: IRole) => r.slug !== 'cpms-cpm-director');
            if (roles.length !== 0) {
                return res.status(200).json(roles);
            }
            return res.status(404).json(roles);
        } catch (e) {
            return res.status(500).json(e);
        }
    }


    public store(req: Request, res: Response) {
        const roleModel = Model(Role);
        const data: IRole = req.body;
        const role = roleModel.create(data);
        roleModel.save(role).then(() => {
            return res.status(201).json([]);
        }).catch(error => {
                return res.status(500).send(error);
            }
        );

    }

    public show(req: Request, res: Response) {
        Model(Role).findOne(req.params.id).then(data => {
            data ?
                res.status(200).json(data) :
                res.status(404).json(data);
        }).catch(error => {
            res.status(500).send(error);
        });
    }

    public update(req: Request, res: Response) {
        Model(Role).update({id: req.params.id}, req.body).then(result => {
            console.log(result);
            result.raw.affectedRows !== 0 ?
                res.status(204).json([]) :
                res.status(404).json(result);
        }).catch(error => {
                res.status(500).send(error);
            }
        );
    }

    public destroy(req: Request, res: Response) {
        Model(Role).delete(req.params.id).then(result => {
            result.raw.affectedRows !== 0 ?
                res.status(204).json([]) :
                res.status(404).json(result);
        }).catch(error => {
            res.status(500).send(error);
        });
    }
}
