import {Injectable} from '@angular/core';

@Injectable()
export class ArrayAndObjectService {

    constructor() {
    }

    union(inputArray: any[][]) {
        const outputArray: any[] = [];
        inputArray.forEach((singleArray: any[]) => {
            singleArray.forEach((singleElement: any) => {
                const foundIndex = outputArray.indexOf(singleElement);
                if (foundIndex < 0) {
                    outputArray.push(singleElement);
                }
            });
        });
        return outputArray;
    }

}
