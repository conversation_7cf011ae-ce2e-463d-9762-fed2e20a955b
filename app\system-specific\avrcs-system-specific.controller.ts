import {Request, Response} from 'express';
import {InstanceService} from '../services/instance.service';
import {Database, Model} from '../config/database';
import {IUserRole, UserRole} from '../entity/user-role';
import {FIELDS, IUser, User} from '../entity/user';
import {IUserRoleVendor, UserRoleVendor} from '../entity/user-role-vendor';
import {IRole, Role} from '../entity/role';
import {createQueryBuilder} from 'typeorm';
import {IUserRoleRecord, UserRoleRecord} from '../entity/user-role-record';
import {HelperService} from '../services/helper.service';


export class AvrcsSystemSpecificController {
    public static instance: AvrcsSystemSpecificController;

    public static getInstance(): AvrcsSystemSpecificController {
        return InstanceService.getInstance(AvrcsSystemSpecificController);
    }

    public async getUsersByIds(req: Request, res: Response) {
        try {
            const ids = HelperService.normalizeQueryParams(req.query['ids']);
            const query = <any>await createQueryBuilder('UserRole')
                .select('UserRole.id')
                .leftJoin('UserRole.user', 'user')
                .leftJoin('UserRole.role', 'role')
                .addSelect('user.name')
                .addSelect('user.last_name')
                .addSelect('user.email')
                .addSelect('user.username')
                .addSelect('role.slug')
                .where(`UserRole.id in (:...ids)`, {ids: ids})
                .getMany();
            query.forEach((item) => {
                item['full_name'] = (item.user.name === item.user.last_name) ? item.user.name : item.user.name + item.user.last_name;
                item['role_slug'] = item.role.slug;
                item['email'] = item.user.email;
                item['username'] = item.user.username;
                delete item.user;
                delete item.role;
            });
            return res.status(200).json(query);
        } catch (e) {
            console.log(e);
            return res.status(500).json(e);
        }
    }

    public async getUserWithRole(req: Request, res: Response) {
        try {
            const vendorLicenseNumber = req.query['vendor_license_number'];
            if (vendorLicenseNumber) {
                const userVendor = <IUserRoleVendor | any>await Model(UserRoleVendor).findOne({vendor_license_number: vendorLicenseNumber});
                const userRole = <IUserRole | any>await Model(UserRole).findOne({id: userVendor.user_role_id});
                const user = <IUser | any>await Model(User).findOne({select: FIELDS, where: {id: userRole.user_id}});
                user.role = <IRole | any>await Model(Role).findOne({where: {id: userRole.role_id}});
                // sets the userId to userRoleId because current systems front ends uses userId but the actual user is determined by userRoleId
                user.id = userRole.id;
                if (!user) {
                    return res.status(404).json();
                }
                return res.status(200).json(user);

            }
        } catch (e) {
            return res.status(500).json(e);
        }
    }

    public async getVendorInformationApproval(req: Request, res: Response) {
        try {
            const role = <IRole | any>await Model(Role).findOne({where: {slug: 'avrcs-vendor-information-approval'}});
            const userRole = <IUserRole | any>await Model(UserRole).findOne({where: {role_id: role.id}});
            const user = <IUser | any>await Model(User).findOne({select: FIELDS, where: {id: userRole.user_id}});
            user.role = role;
            // sets the userId to userRoleId because current systems front ends uses userId but the actual user is determined by userRoleId
            user.id = userRole.id;
            if (!user) {
                return res.status(404).json();
            }
            return res.status(200).json(user);

        } catch (e) {
            return res.status(500).json(e);
        }
    }


    public async insertVendorLicenseNumber(req: Request, res: Response) {
        const queryRunner = await Database.QueryRunner();
        await queryRunner.instance.startTransaction();
        let statusCode = 409;
        try {
            const data: IUserRoleVendor = req.body;
            const userRoleVendorObject = <any>await queryRunner.model(UserRoleVendor).create(<any>data);
            const userRoleVendor = <any>await queryRunner.model(UserRoleVendor).save(<any>userRoleVendorObject);
            const role = <IRole>await queryRunner.model(Role).findOne({where: {slug: 'cpms-company'}});
            if (!role) {
                statusCode = 404;
                throw new Error('user_role not found!');
            }
            const userRole = <IUserRole>await queryRunner.model(UserRole).findOne({where: {id: req.body.user_role_id}});
            if (!userRole) {
                statusCode = 404;
                throw new Error('user id not found!');
            }
            await queryRunner.model(UserRole).insert(<IUserRole[]>[
                {
                    user_id: userRole.user_id,
                    role_id: role.id
                }
            ]);
            const vendorInformationApprovalRole = <IRole>await queryRunner.model(Role).findOne({where: {slug: 'avrcs-vendor-information-approval'}});
            const vendorInformationApprovalUserRole = <IUserRole>await queryRunner.model(UserRole).findOne({where: {role_id: vendorInformationApprovalRole.id}});
            await queryRunner.model(UserRoleRecord).insert(<IUserRoleRecord[]>[
                {
                    user_role_id: vendorInformationApprovalUserRole.id,
                    record_id: req.body.vendor_id
                }
            ]);
            await queryRunner.instance.commitTransaction();
            res.status(201).json(userRoleVendor.id);
        } catch (e) {
            console.log('in transaction...');
            console.log(e);
            const error = e.toString();
            await queryRunner.instance.rollbackTransaction();
            res.status(statusCode).json(error);
        } finally {
          return await queryRunner.instance.release();
        }
    }

    public async checkIfUserHasVendor(req: Request, res: Response) {
        try {
            const userRoleVendor = <IUserRoleVendor>await Model(UserRoleVendor).findOne({where: {user_role_id: req.params.user_role_id}});
            if (!userRoleVendor) {
                return res.status(404).json('No vendor found!');
            }
            return res.status(200).json();
        } catch (e) {
            return res.status(500).json(e);
        }
    }

}
