<div fxLayout="column">
    <mat-card class="exceptional-mat-button-wrapper">
        <mat-card-header class="main-page-bottom-border">
            {{'SYSTEM_GUIDE_HAS_THE_FOLLOWING_OPTIONS' | translate}}

        </mat-card-header>
        <mat-card-content fxLayout="column">
            <div fxFlex="100">
                <a mat-button
                   type="button"
                   routerLink="/{{lang}}/{{authService.user.value.role.slug}}/user-guide/system-information"
                >{{"SYSTEM_INFORMATION" | translate}}</a>
            </div>
            <div fxFlex="100">
                <a mat-button
                   type="button"
                   routerLink="/{{lang}}/{{authService.user.value.role.slug}}/user-guide/change-log"
                >{{"CHANGE_LOG" | translate }}</a>
            </div>
            <div fxFlex="100">
                <a mat-button
                   type="button"
                   routerLink="/{{lang}}/{{authService.user.value.role.slug}}/user-guide/contact-information"
                >{{ "CONTACT_INFORMATION" | translate }}</a>
            </div>
            <div fxFlex="100">
                <a mat-button
                   type="button"
                   routerLink="/{{lang}}/{{authService.user.value.role.slug}}/user-guide/user-manual"
                >{{"USER_MANUAL"  | translate}}</a>
            </div>
            <div fxFlex="100">
                <a mat-button
                   type="button"
                   routerLink="/{{lang}}/{{authService.user.value.role.slug}}/user-guide/reset-system"
                >{{ "RESET_SYSTEM" | translate }}</a>
            </div>
        </mat-card-content>
    </mat-card>
</div>
