import {JwtTokenService} from '../services/jwt-token.service';
import {Model} from '../config/database';
import {Session} from '../entity/session';
import * as env from 'dotenv';
import * as path from 'path';

env.load({path: path.resolve(__dirname, '../../.env')});

export class Authentication {
    public static async validateToken(req, res, next) {
        try {
            const secret = req.query['key'];
            const authorizationHeader = req.headers['authorization'];

            const session = JSON.parse(authorizationHeader);
            if (!session || !session.token) {
                return res.status(401).json('No session provided!');
            }

            const verified = <any>await JwtTokenService
                .verifyToken(session.token, secret ? 'systemToken' : 'access', secret);
            if (!verified['username'] &&
                !verified['role'] &&
                !verified['email'] &&
                !verified['user_role_id'] &&
                !verified['jti']) {
                return res.status(401).json('Token is modified!');
            }
            const updated = await Model(Session).update({
                    id: verified['jti'],
                    user_role_id: verified['user_role_id']
                },
                {client_signature: (<Request>req).headers['user-agent']}
            );
            if (updated.raw.affectedRows !== 1) {
                return res.status(401).json('Session not found!');
            }
            res.locals.user_role_id = verified['user_role_id'];
            res.locals.role = verified['role'];
            res.locals.username = verified['username'];
            res.locals.email = verified['email'];
            return next();
        } catch (e) {
            return res.status(401).json(e);
        }
    }
}
