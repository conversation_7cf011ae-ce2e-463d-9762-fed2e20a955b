import {Component, Inject, OnInit} from '@angular/core';
import {MAT_DIALOG_DATA, MatDialogRef} from '@angular/material';
import {AuthService} from '../../../services/auth.service';
import {IConfirmationAndJustificationDialogInput} from '../types/confirmation-and-justification.types';
import {FormBuilder, FormGroup} from '@angular/forms';
import {TranslateService} from '../services/translate.service';
import {FormValidationService} from '../../../services/form-validation.service';

@Component({
    selector: 'npa-confirmation-and-justification-dialog',
    templateUrl: './confirmation-and-justification-dialog.component.html',
    styleUrls: ['./confirmation-and-justification-dialog.component.styl']
})
export class ConfirmationAndJustificationDialogComponent implements OnInit {
    reasonText: string;
    rejectReasonText: string;
    isRejected = false;
    form: FormGroup;
    dir: string;

    constructor(public dialogRef: MatDialogRef<ConfirmationAndJustificationDialogComponent>,
                public authService: AuthService,
                @Inject(MAT_DIALOG_DATA) public data: IConfirmationAndJustificationDialogInput,
                private _formBuilder: FormBuilder,
                public formValidationService: FormValidationService,
                private translateService: TranslateService) {
    }

    ngOnInit() {
        this.dir = this.translateService.dir;
        this.initForm();
    }

    confirm() {
        if (!this.isValid()) {
            return false;
        }
        this.dialogRef.close({
            status: true,
            checkBox: this.isRejected,
            reason: this.rejectReasonText === undefined ? this.reasonText : this.rejectReasonText
        });
    }

    cancel() {
        this.dialogRef.close({
            status: false,
            reason: null
        });
    }

    initForm() {
        this.form = this._formBuilder.group({
            reason: ['',
                [
                    this.formValidationService.required.validator
                ],
            ],
            checkBox: [],
        });
    }

    isValid(): boolean {
        return this.form.valid;
    }
}
