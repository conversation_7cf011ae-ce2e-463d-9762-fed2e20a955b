import {Component, OnInit} from '@angular/core';
import {MatDialog} from '@angular/material';
import {NPA_COMMON_DIALOG_WIDTH} from '../../shared/consts/sizes';
import {NpaDatabase, NpaDataSource} from '../../shared/classes/npa-table';
import {IAlert, IAlertListItem} from '../alert.types';
import {AlertViewDialogComponent} from '../alert-view-dialog/alert-view-dialog.component';

@Component({
    selector: 'npa-archived',
    templateUrl: './archived.component.html',
    styleUrls: ['./archived.component.styl']
})
export class ArchivedComponent implements OnInit {
    displayedColumns = ['id', 'generation_date', 'type', 'title', 'options'];
    dataSource: NpaDataSource;
    database: NpaDatabase;

    constructor(public dialog: MatDialog) {
    }

    ngOnInit() {
        this.database = new NpaDatabase(listData);
        this.dataSource = new NpaDataSource(this.database);
    }

    openViewDialog(id: number): void {
        this.dialog.open(AlertViewDialogComponent, {
            width: NPA_COMMON_DIALOG_WIDTH,
            data: itemData
        });
    }
}

const itemData: IAlert = undefined;

const listData: IAlertListItem[] = [];

