import {<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>To<PERSON>ne, PrimaryGeneratedColumn} from 'typeorm';
import {Role} from './role';
import {Context} from './context';
import {Operation} from './operation';

@Entity('context_accesses')
export class ContextAccess extends BaseEntity implements IContext {

    @PrimaryGeneratedColumn()
    id: number;

    @Column({name: 'is_enabled'})
    is_enabled: boolean;

    @Column()
    role_id: number;

    @ManyToOne(type => Role, role => role.id)
    @JoinColumn({name: 'role_id'})
    role: Role;

    @Column()
    context_id: number;

    @ManyToOne(type => Context, context => context.id)
    @JoinColumn({name: 'context_id'})
    context: Context;

    @Column()
    operation_id: number;

    @ManyToOne(type => Operation, operation => operation.id)
    @JoinColumn({name: 'operation_id'})
    operation: Operation;
}

export interface IContext {
    id: number;
    is_enabled: boolean;
    role_id: number;
    operation_id: number;
    context_id: number;
    role: Role | any;
    context: Context | any;
    operation: Operation | any;
}



