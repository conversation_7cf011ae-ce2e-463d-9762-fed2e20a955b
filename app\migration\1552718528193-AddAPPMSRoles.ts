import {MigrationInterface, QueryRunner} from 'typeorm';

export class AddAPPMSRoles1552718528193 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<any> {
        await queryRunner.query(`
            INSERT INTO
              roles (id,name, slug, party, can_be_acted, can_act, is_pe_based, is_sector_based, is_vendor_based, is_record_based, is_threshold_based, system_id)
            VALUES
              (12, 'procurement-plan-manager', 'appms-procurement-plan-manager', 'pe', true, false, true, true, false, true, true, 4),
              (13, 'award-authority','appms-award-authority', 'pe', true, false, true, true, false, true, true, 4),
              (14, 'specialist','appms-specialist', 'npa', true, false, true, true, false, true, true, 4),
              (15, 'procurement-policy-manager','appms-procurement-policy-manager', 'npa', true, false, false, true, false, false, false, 4),
              (16, 'procurement-policy-director','appms-procurement-policy-director', 'npa', true, false, false, false, false, false, false, 4),
              (17, 'cpmd-director','appms-contract-progress-monitoring-director', 'npa', false, true, false, true, false, false, false, 4),
              (18, 'cpmd-system-development','appms-system-development', 'npa', false, true, false, true, false, false, false, 4)
              `);
    }

    public async down(queryRunner: QueryRunner): Promise<any> {
    }
}
