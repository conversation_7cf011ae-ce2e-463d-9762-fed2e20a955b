<div class="scorecard">
    <mat-card class="card" [ngClass]="{'rotate' : isFlipped}">
        <div fxLayout="row-reverse" class="header">
            <a mat-icon-button class="flip" *ngIf="!isFlipped" (click)="isFlipped = !isFlipped">
                <mat-icon>more_vert</mat-icon>
            </a>
            <a mat-icon-button class="close" *ngIf="isFlipped" (click)="isFlipped = !isFlipped">
                <mat-icon>close</mat-icon>
            </a>
            <h1 mat-card-title class="title front" *ngIf="!isFlipped">
                <ng-content select="[title]"></ng-content>
            </h1>
        </div>
        <mat-card-content >
            <div fxLayout="column" class="contents" [ngClass]="{'bottom':smallSize}" *ngIf="!isFlipped">
                <div *ngIf="text !== null && text !== undefined" class="value-container"><span class="value npa-number"  [style.fontSize]="size">{{text}}</span></div>
                <div *ngIf="text !== null && text !== undefined" class="info npa-number"><span>{{subText}}</span></div>
                <div *ngIf="text === null || text === undefined" class="value-container"><span class="value">-</span></div>
            </div>
            <div *ngIf="isFlipped" class="back-side-content back">
                <span>
                    <ng-content select="[back]"></ng-content>
                </span>
            </div>
        </mat-card-content>
        <div class="footer" *ngIf="!isFlipped">
            <ng-content select="[footer]"></ng-content>
        </div>
    </mat-card>
</div>