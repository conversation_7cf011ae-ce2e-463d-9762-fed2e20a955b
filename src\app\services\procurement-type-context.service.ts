import {Injectable} from '@angular/core';
import {Subject, BehaviorSubject} from 'rxjs';

@Injectable()
export class ProcurementTypeContextService {
    procurementTypeContextSubject: BehaviorSubject<any> = new BehaviorSubject({
        procurement_type: undefined
    });

    constructor() {
        if (!this._context && localStorage.getItem('context') && !localStorage.getItem('context').length) {
            console.log('[INFO] PROCUREMENT_TYPE_CONTEXT INITIALIZING FROM LOCAL STORAGE', localStorage.getItem('context'));
            this.context = localStorage.getItem('context');
        }
    }

    private _context: string;
    public contextSubject: Subject<string> = new Subject();

    public set context(v: string) {
        this._context = v;
        localStorage.setItem('context', v);
        this.contextSubject.next(this._context);
    }

    public get context() {
        return this._context;
    }

    switchProcurementType(value: string) {
        const currentProcurement = this.procurementTypeContextSubject.getValue();
        currentProcurement.procurement_type = value;
        this.procurementTypeContextSubject.next(currentProcurement);
    }
}
