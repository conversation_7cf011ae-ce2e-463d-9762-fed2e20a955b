import {Component, OnInit} from '@angular/core';
import {AbstractControl, FormBuilder, FormControl, FormGroup, ValidatorFn, Validators} from '@angular/forms';
import {AuthService} from '../../../services/auth.service';
import {NPA_ALERTS} from '../../shared/consts/messages';
import {HttpErrorResponse} from '@angular/common/http';
import {NotificationsService} from '../../shared/services/notifications.service';
import {HttpService} from '../../../services/http.service';
import {TranslateService} from '../../shared/services/translate.service';
import {MatDialogRef} from '@angular/material';

@Component({
    selector: 'npa-reset-password',
    templateUrl: './reset-password.component.html',
    styleUrls: ['./reset-password.component.styl']
})
export class ResetPasswordComponent implements OnInit {
    form: FormGroup;

    constructor(private formBuilder: FormBuilder,
                private _authService: AuthService,
                private _httpService: HttpService,
                public translateService: TranslateService,
                public dialogRef: MatDialogRef<ResetPasswordComponent>,
                public notificationsService: NotificationsService) {
    }

    ngOnInit() {
        this.initResetPasswordForm();
    }

    initResetPasswordForm() {
        this.form = this.formBuilder.group({
            password: new FormControl('', [Validators.required]),
            new_password: new FormControl('', [Validators.required, Validators.pattern(this._authService.passwordPattern)]),
            repeat_password: new FormControl('', [Validators.required, Validators.pattern(this._authService.passwordPattern), this.RepeatPassword('new_password')])
        });
    }


    RepeatPassword(fieldName: string): ValidatorFn {
        return (control: AbstractControl): { [key: string]: any } | null => {
            const otherControl: AbstractControl = control.root.get(fieldName);
            return (otherControl && control.value !== otherControl.value) ? {match: true} : null;
        };
    }

    resetPassword() {
        this.notificationsService.startLoading();
        this._httpService._put(`api/users/reset-password?user_role_id=${this._authService.loggedInUser.id}`, this.form.value)
            .subscribe((response: any) => {
                this.notificationsService.success(this.translateService.translateKey('SUCCESSFULLY_DONE'));
                this.dialogRef.close({
                    data: {},
                    status: true
                });
            }, (error: HttpErrorResponse) => {
                if (error.error.password) {
                    this.notificationsService.error(this.translateService.translateKey('PASSWORD_ERROR_MESSAGE'));
                } else {
                    this.notificationsService.error(NPA_ALERTS.ERROR);
                }
                console.error(error);
            });
    }
}
