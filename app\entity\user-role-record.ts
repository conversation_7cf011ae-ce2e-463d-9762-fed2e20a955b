import {BaseEntity, Column, Entity, Index, JoinColumn, ManyToOne, PrimaryGeneratedColumn} from 'typeorm';
import {UserRole} from './user-role';

@Entity('user_role_records')
@Index('unique_record_id_and_user_role_id', ['record_id', 'user_role_id'], {unique: true})
export class UserRoleRecord extends BaseEntity implements IUserRoleRecord {

    @PrimaryGeneratedColumn()
    id: number;

    @Column({name: 'record_id'})
    record_id: number;

    @Column({name: 'user_role_id'})
    user_role_id: number;

    @ManyToOne(type => UserRole, userRole => userRole.id, { onDelete: 'CASCADE' })
    @JoinColumn({name: 'user_role_id'})
    userRole: UserRole;

}

export interface IUserRoleRecord {
    id?: number;
    record_id: number;
    user_role_id: number;
    userRole: any | UserRole;
}


