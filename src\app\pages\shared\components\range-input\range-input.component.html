<!--<div>-->
    <!--<div class="container" *ngIf="config">-->
        <!--<label class="range">-->
            <!--&lt;!&ndash;{{ label |translate}}&ndash;&gt;-->
            <!--{{ label }}-->
        <!--</label>-->
        <!--<div class="slider">-->
            <!--<nouislider [config]="config"-->
                        <!--[step]="1"-->
                        <!--[ngModel]="npaModel"-->
                        <!--(ngModelChange)="onChangeInput($event)"-->
                        <!--[name]="name"-->
                        <!--[tooltips]="tooltips">-->

            <!--</nouislider>-->
        <!--</div>-->
    <!--</div>-->
<!--</div>-->
