import {BaseEntity, Column, Entity, Index, JoinColumn, ManyToOne, OneToMany, PrimaryGeneratedColumn} from 'typeorm';
import {System} from './system';
import {UserRole} from './user-role';
import {ContextAccess} from './context-access';

@Entity('roles')
@Index('unique_slug', ['slug'], {unique: true})

export class Role extends BaseEntity implements IRole {

    @PrimaryGeneratedColumn()
    id: number;

    @Column()
    name: string;

    @Column({unique: true})
    slug: string;

    @Column({type: 'enum', enum: ['npa', 'vendor', 'pe', 'other']})
    party: string;

    @Column({name: 'can_be_acted'})
    can_be_acted: boolean;

    @Column({name: 'can_act'})
    can_act: boolean;

    @Column({name: 'system_id'})
    system_id: number;

    @Column({name: 'is_pe_based', default: false})
    is_pe_based: boolean;

    @Column({name: 'is_sector_based', default: false})
    is_sector_based: boolean;

    @Column({name: 'is_vendor_based', default: false})
    is_vendor_based: boolean;

    @Column({name: 'is_record_based', default: false})
    is_record_based: boolean;

    @Column({name: 'is_threshold_based', default: false})
    is_threshold_based: boolean;

    @ManyToOne(type => System, system => system.id)
    @JoinColumn({name: 'system_id'})
    system: System;

    @OneToMany(type => ContextAccess, contextAccess => contextAccess.role)
    contextAccess: ContextAccess[];

    @OneToMany(type => UserRole, userRole => userRole.role)
    userRole: UserRole[];

}

export interface IRole {
    id?: number;
    name: string;
    slug: string;
    party: string;
    can_be_acted: boolean;
    can_act: boolean;
    is_pe_based: boolean;
    is_sector_based: boolean;
    is_vendor_based: boolean;
    is_record_based: boolean;
    is_threshold_based: boolean;
    system_id: number | System;
}



