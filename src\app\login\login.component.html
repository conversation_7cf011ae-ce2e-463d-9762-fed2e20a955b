<div class="H2SoFe LZgQXe TFhTPc">
    <div class="RAYh1e LZgQXe" id="initialView">
        <div class="xkfVF" role="presentation" tabindex="null">
            <div class="fctIrd"
                 data-can-enhance="true" data-oauth-third-party-logo-url="" aria-hidden="true">
                <div id="logo" class="SSBsw" title="AGEOPS">
                    <div class="qZp31e">
                        <a [routerLink]="['/', lang, 'login']"> <img width="94" height="57" src="assets/images/ageopsLogoG.svg"></a>
                    </div>
                </div>
            </div>
            <div id="view_container" class="JhUD8d SQNfcc vLGJgb">
                <div class="DRS7Fe bxPAYd k6Zj8d" role="presentation">
                    <div class="jXeDnc">
                        <h1 data-a11y-title-piece="" id="headingText">
                            <content>{{ "WELCOME" | translate }}</content>
                        </h1>
                        <div class="Y4dIwd" id="headingSubtext">
                            <content>{{ "TO_AGEOPS" | translate }}</content>
                        </div>
                        <form [formGroup]="form" novalidate (ngSubmit)="login(form.value)" fxLayout="column"
                              fxLayoutAlign="center">
                            <div fxFlex="100" fxLayout="column">
                                <mat-form-field class="inputs" fxFlex="100">
                                    <input type="email" matInput [placeholder]=' "USER_NAME" | translate'
                                           formControlName="username"
                                           lang="en">
                                    <mat-error *ngIf="form.get('username')?.errors?.required">
                                        {{formValidationService.required.message()}}
                                    </mat-error>
                                </mat-form-field>
                                <mat-form-field class="inputs" fxFlex="100">
                                    <input type="password" matInput [placeholder]=' "PASSWORD" | translate'
                                           formControlName="password"
                                           lang="en">
                                    <mat-error>
                                        {{formValidationService.required.message()}}
                                    </mat-error>
                                </mat-form-field>
                            </div>
                            <div class="action" fxFlex="100" fxLayout="column" fxLayoutAlign="center">
                                <button fxFlex="100" class="margin-top" mat-raised-button type="submit"
                                        [disabled]="!form.valid || isLoading"
                                        color="primary">{{ "LOGIN" | translate }}
                                </button>
                                <button fxFlex="100" class="margin-top" mat-button type="button" [disabled]="isLoading" (click)="goToAGEOPS()"
                                        color="primary">{{ "AGEOPS_HOME_PAGE" | translate }}
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        <div class="links">
            <!-- <a routerLink="/{{translate.lang}}/vendor-registration-form">{{ "REGISTER_VENDOR" | translate }}</a> -->
            <a routerLink="/{{translate.lang}}/auth/recover-password">{{ "RECOVER_PASSWORD" | translate }}</a>
        </div>
        <footer class="RwBngc">
            <ul class="Bgzgmd">
                <li><a (click)="changeLocalStorageLang('prs')" href="prs/login">دری</a></li>
                <li><a (click)="changeLocalStorageLang('ps')" href="ps/login">پښتو</a></li>
                <li><a (click)="changeLocalStorageLang('en')" href="en/login">English</a></li>
            </ul>
        </footer>
    </div>
</div>

