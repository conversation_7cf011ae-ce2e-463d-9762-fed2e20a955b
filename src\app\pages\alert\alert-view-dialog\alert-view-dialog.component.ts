import {Component, Inject, OnInit} from '@angular/core';
import {MAT_DIALOG_DATA, MatDialogRef} from '@angular/material';
import {IAlertListItem} from '../alert.types';
import {AlertService} from '../alert.service';
import {HttpErrorResponse} from '@angular/common/http';
import {DropDownsService} from '../../../services/dropDowns.service';
import {IDropdown} from '../../shared/types/general.types';
import {FormValidationService} from '../../../services/form-validation.service';

@Component({
    selector: 'npa-alert-view-dialog',
    templateUrl: './alert-view-dialog.component.html',
    styleUrls: ['./alert-view-dialog.component.styl']
})
export class AlertViewDialogComponent implements OnInit {

    alert: IAlertListItem;

    constructor(public dialogRef: MatDialogRef<AlertViewDialogComponent>,
                @Inject(MAT_DIALOG_DATA) public alertInstanceId: number,
                public formValidationService: FormValidationService,
                private _alertService: AlertService,
                private _dropDownsService: DropDownsService) {
    }

    ngOnInit() {
        this._alertService.show(this.alertInstanceId).subscribe((alert: IAlertListItem) => {
                const matchedStatus: IDropdown = this._dropDownsService.get(
                    'alertsAndRemarks/alertStatus',
                    alert.status_id
                );
                alert.created_at = (<any>alert.created_at).date;
                alert.status_name_da = matchedStatus.name_da;
                alert.status_slug = matchedStatus.slug;

                this.alert = alert;
            }, (error: HttpErrorResponse) => {
                console.error(error);
            }
        );
    }

    close() {
        this.dialogRef.close(this.alert);
    }
}
