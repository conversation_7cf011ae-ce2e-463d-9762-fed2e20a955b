import {Component, Inject, OnInit} from '@angular/core';
import {MAT_DIALOG_DATA, MatDialog, MatDialogRef} from '@angular/material';
import {FormBuilder, FormGroup} from '@angular/forms';
import {NotificationsService} from '../../../shared/services/notifications.service';
import {FormService} from '../../../shared/services/form.service';
import {NPA_ALERTS} from '../../../shared/consts/messages';
import {UserListService} from '../user-list.service';
import {IComplaintList} from '../user-list.types';
import {FormValidationService} from '../../../../services/form-validation.service';
import { HttpErrorResponse, HttpResponse } from '@angular/common/http';
import { EntityDialogComponent } from '../entity-dialog/entity-dialog.component';
import {NPA_COMMON_DIALOG_WIDTH} from '../../../shared/consts/sizes';

@Component({
    selector: 'npa-add-edit-dialog',
    templateUrl: './add-edit-dialog.component.html',
    styleUrls: ['./add-edit-dialog.component.styl']
})
export class AddEditDialogComponent implements OnInit {
    form: FormGroup;
    roles = []
    systemRoles = []
    systems = []
    entities = []
    sectors = []
    selectedSectors = []
    formattedRoles = []
    selectedRoles = []
    selectedSystem = null
    isEditing = false
    isCpmdUser = false
    isObserver = false
    constructor(private dialogRef: MatDialogRef<AddEditDialogComponent>,
                public formValidationService: FormValidationService,
                @Inject(MAT_DIALOG_DATA) public data,
                private formBuilder: FormBuilder,
                private dialog: MatDialog,
                public notificationsService: NotificationsService,
                public formService: FormService,
                private userListService: UserListService) {
    }

    ngOnInit() {
      this._load()
    }

    private _load() {
      this.notificationsService.startLoading();
      this.loadSystems()
      this.loadSectors()
    }

    loadSystems(){
      this.userListService.systems().subscribe(
        (data: HttpResponse<any>) => {
            this.systems = data.body
            this.loadRoles()
        },
        (error: HttpErrorResponse) => {
          this.notificationsService.error(NPA_ALERTS.ERROR);
          console.error(error);
        }
      );
    }

    loadRoles(){
      this.userListService.roles().subscribe(
        (data: HttpResponse<any>) => {
          // this.roles = data.body.filter(r => r.system_id == 2 || r.system_id == 4)
            this.roles = data.body.filter(r => r.system_id == 2)
            this.formattedRoles = this.roles.reduce((acc, role) => {
              // const systemName = role.system_id === 2 ? "ACPMS" : "APPMS";
              const systemName = "ACPMS";
              const existingSystem = acc.find(item => item.system === systemName);
              if (existingSystem) {
                existingSystem.role.push({ id: role.id, name: role.name, slug: role.slug });
              } else {
                acc.push({ system: systemName, role: [{ id: role.id, name: role.name, slug: role.slug }] });
              }
              return acc;
            }, []);
            if (this.data && this.data.id) {
              this.initForm(this.data);
              this.isEditing = true
            } else {
                this.initForm(undefined);
            }
            this.notificationsService.dismissLoading()
        },
        (error: HttpErrorResponse) => {
          this.notificationsService.error(NPA_ALERTS.ERROR);
          console.error(error);
        }
      );
    }

    loadSectors() {
      this.userListService.sectors().subscribe(
        (data: HttpResponse<any>) => {
          this.sectors = data.body
          this.sectors.push({
            id: 0,
            name_da: 'انتخاب همه',
            slug: 'select-all'
          })
          this.sectors = this.sectors.sort((a,b) => a.id-b.id)
          this.notificationsService.dismissLoading();
        },
        (error: HttpErrorResponse) => {
          this.notificationsService.error(NPA_ALERTS.ERROR)
          console.error(error)
        }
      )
    }

    initForm(data: IComplaintList | any) {
        if(data) {
          this.selectedRoles = data.userRole.filter(role => role.role.slug !== 'user').map(role => role.role.slug );
          const userRole = data.userRole.filter(ur => ur.role_id !== 9 )[0];
          const peIds = userRole.userRolePe.map(pe => pe.procurement_entity_id)
          this.setEntities(peIds)
          const sectorIds = userRole.userRoleSector.map(sector => sector.sector_id)
          this.setSectors(sectorIds)
        }
        this.form = this.formBuilder.group({
            id: data && data.id,
            username: [data && data.username, [this.formValidationService.required.validator, this.formValidationService.username.validator]],
            name: [data && data.name, this.formValidationService.required.validator],
            last_name: [data && data.last_name, this.formValidationService.required.validator],
            phone_number: [data && data.phone_number, [this.formValidationService.phoneNumberAfg.validator, this.formValidationService.required.validator]],
            email: [data && data.email, [this.formValidationService.required.validator, this.formValidationService.emailPattern.validator]],
            role_slug: [data && data.userRole && this.selectedRoles, this.formValidationService.required.validator],
            // system: [data && system, this.formValidationService.required.validator],
        });

        this.isCpmdUser = this.form.get('role_slug').value && this.form.get('role_slug').value.some(role => ['cpms-specialist', 'cpms-cpm-manager'].includes(role));
        this.isObserver = this.form.get('role_slug').value && this.form.get('role_slug').value.includes('cpms-observer');
    }

    setEntities(peIds = []){
      this.userListService.entities().subscribe(
        (data: HttpResponse<any>) => {
            const entities = data.body
            this.entities = entities.filter(e => peIds.includes(e.id))
            this.notificationsService.dismissLoading()
        },
        (error: HttpErrorResponse) => {
          this.notificationsService.error(NPA_ALERTS.ERROR);
          console.error(error);
        }
      );
    }

    setSectors(sectorIds = []) {
      if (sectorIds.length > 0) {
        this.userListService.sectors().subscribe(
          (data: HttpResponse<any>) => {
            const sectors = data.body
            this.selectedSectors = sectors.filter(s => sectorIds.includes(s.id))
            this.notificationsService.dismissLoading()
          },
          (error: HttpErrorResponse) => {
            this.notificationsService.error(NPA_ALERTS.ERROR)
            console.error(error)
          }
        )
      }
    }

    sectorsCompareWithFn = (a: any, b: any) => a && b && a.id === b.id;

    sectorSelect(selectedOptions) {
      const selectAll = selectedOptions.some(sector => sector.slug === 'select-all')
      if (selectAll) {
        this.selectedSectors = this.sectors.filter(s => s.slug !== 'not-available' && s.slug !== 'select-all')
      } else {
        this.selectedSectors = [...this.selectedSectors];
        selectedOptions.forEach(sector => {
          if (sector.slug !== 'not-available') {
            const exists = this.selectedSectors.some(s => s.id === sector.id)
            if (!exists) {
              this.selectedSectors.push(sector)
            }
          } else {
            this.selectedSectors = []
          }
        });
      }
      this.selectedSectors = Array.from(new Set(this.selectedSectors.map(s => s.id))).map(id => this.selectedSectors.find(s => s.id === id))
    }

    roleSelect(selectedRoles: string[]) {
      const roles = selectedRoles.map(role => role.toLowerCase());
      this.isCpmdUser = roles.includes('cpms-specialist') || roles.includes('cpms-cpm-manager');
      this.isObserver = roles.includes('cpms-observer');
    }

    submit(data) {
      let entities = this.entities.map(e => ({procurement_entity_id: e.id}))
      let sectors = this.selectedSectors.map(s => ({sector_id: s.id}))
      this.notificationsService.startLoading();
      if (data.id == null) {
          this.userListService.storeUser({
            ...data,
            api_password: "AABBCC!!@@##112233",
            status_slug: "active",
            role_data: {
              procurement_entities: entities,
              sectors: sectors
            }
          }).subscribe(
              (response: IComplaintList) => {
                  this.notificationsService.dismissLoading();
                  this.notificationsService.success(NPA_ALERTS.UPDATE_SUCCESS);
                  this.dialogRef.close(data);
              },
              (error: HttpErrorResponse) => {
                if (error.error && error.error.message && typeof error.error.message === 'string') {
                    if (error.error.message.includes('ER_DUP_ENTRY') || error.error.message.includes('unique_email')) {
                        this.notificationsService.error(NPA_ALERTS.RECORD_EXIST);
                    } else if (error.error.message.includes('username_error')) {
                      this.notificationsService.error(NPA_ALERTS.USERNAME_ERROR);
                    } else {
                      this.notificationsService.error(NPA_ALERTS.ERROR);
                    }
                } else {
                    this.notificationsService.error(error);
                    console.error(error);
                }
              },
            )
      } else {
          this.userListService.update({
            user_data: {
              id: data.id,
              username: data.username,
              name: data.name,
              phone_number: data.phone_number,
              email: data.email,
            },
            ...data,
            role_data: {
              procurement_entities: entities,
              sectors: sectors
            }
          }, this.data && this.data.id )
              .subscribe(
                response => {
                    this.notificationsService.dismissLoading();
                    this.notificationsService.success(NPA_ALERTS.UPDATE_SUCCESS);
                    this.dialogRef.close({...this.data, ...data});
                },
                (error: HttpErrorResponse) => {
                  if (error.error && error.error.message && typeof error.error.message === 'string') {
                      if (error.error.message.includes('username_error')) {
                          this.notificationsService.error(NPA_ALERTS.USERNAME_ERROR);
                      } else {
                        this.notificationsService.error(NPA_ALERTS.ERROR);
                      }
                  } else {
                      this.notificationsService.error(error);
                      console.error(error);
                  }
                },
              );
      }
    }

    isValid(): boolean {
        let isValid = this.form.valid
        if (this.form.get('role_slug').value && (this.form.get('role_slug').value.includes('cpms-specialist') || this.form.get('role_slug').value.includes('cpms-cpm-manager'))) {
          isValid = this.form.valid && this.selectedSectors.length > 0
        } else if (this.form.get('role_slug').value && this.form.get('role_slug').value.includes('cpms-observer')) {
          isValid = this.form.valid && this.entities.length > 0 && this.selectedSectors.length > 0;
        } else {
          isValid = this.form.valid && this.entities.length > 0;
        }
        return isValid
    }

    openEntityDialog(data = {}) {
      const dialogRef = this.dialog.open(EntityDialogComponent, {
        width: NPA_COMMON_DIALOG_WIDTH,
        data: data,
        disableClose: true,
      });
      dialogRef.afterClosed().subscribe(result => {
        if (Object.keys(result).length === 0) {
          return;
        }
        if(result && result.length > 0)
          this.entities = result
      });
    }
}
