import {Injectable} from '@angular/core';
import {HttpService} from '../services/http.service';
import {Observable} from 'rxjs';

@Injectable()
export class RecoverPasswordFormService {

    constructor(private _http: HttpService) {
    }

    update(data: any): Observable<any> {
        return this._http._put('api/users/change-password', data);
    }

    isLinkValid(token): Observable<any> {
        return this._http.get('api/verify/recover-password-token?token=' + token);
    }

}
