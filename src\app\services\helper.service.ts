import {Injectable} from '@angular/core';
import {environment as localEnvironment} from '../../environments/environment';
import {environment as productionEnvironment} from '../../environments/environment.prod';
import {BehaviorSubject} from 'rxjs';

@Injectable()

export class HelperService {
    public static env = localEnvironment || productionEnvironment;
    public static redirectedUrl: string;
    public static redirectedSystemBaseUrl: string;
    public static hasRedirectedSystemAccess = new BehaviorSubject<boolean>(false);

    public static redirectTo(url) {
        window.location.href = this.env.production ? this.env.baseUrl.backend.main + `/${localStorage.getItem('lang')}/login` :
            this.env.baseUrl.frontEnd.main + `${localStorage.getItem('lang')}/${url}`;
    }

    public static navigatedIfUnauthorized() {
        const url = window.location.href;
        const urlArray = url.split('/');
        const LoginIndex = urlArray.indexOf('login');
        console.log(this.redirectedUrl);
        if (LoginIndex) {
            return;
        }

        localStorage.removeItem('session');
        this.redirectTo('login');
    }

    getUrlParameterByName(name, url) {
        name = name.replace(/[\[\]]/g, '\\$&');
        const regex = new RegExp('[?&]' + name + '(=([^&#]*)|&|#|$)'),
            results = regex.exec(url);
        if (!results) {
            return null;
        }
        if (!results[2]) {
            return '';
        }
        return decodeURIComponent(results[2].replace(/\+/g, ' '));
    }
}
