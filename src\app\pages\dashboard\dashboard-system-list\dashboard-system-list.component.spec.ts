import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { DashboardSystemListComponent } from './dashboard-system-list.component';

describe('DashboardSystemListComponent', () => {
  let component: DashboardSystemListComponent;
  let fixture: ComponentFixture<DashboardSystemListComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ DashboardSystemListComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(DashboardSystemListComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
