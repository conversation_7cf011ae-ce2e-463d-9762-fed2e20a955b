export const SIMPLE_PIE = {
    title: {
        text: '',
        x: 'center',
        textStyle: {
            fontFamily: 'XB Niloofar',
            color: '#333',
            fontWeight: 'normal',
            fontSize: 16,
            lineHeight: 20,
        }
    },
    color: ['#595b5d', '#e2d0ad', '#b3b3ff', '#000066'],
    tooltip: {
        trigger: 'item',
        formatter: '{b} <br/>{a} :{c} ({d}%)',
        axisPointer: {
            type: 'shadow'
        },
        textStyle: {
            fontFamily: 'XB Niloofar',
        },
    },
    toolbox: {
        show: true,
        feature: {
            mark: {
                show: true,
                title: {
                    mark: 'Add Guide',
                    markUndo: 'Remove Guide',
                    markClear: 'Remove all Guide',
                },
            },
            dataView: {
                show: true,
                readOnly: false,
                optionToContent: null,
                contentToOption: null,
                title: 'Data View',
                lang: [],
                buttonColor: '#01579B'
            },
            magicType: {
                show: true,
                type: ['pie', 'funnel'],
                title: {pie: 'pie', funnel: 'funnel'}
            },
            restore: {
                show: true,
                title: 'Reset'
            },
            saveAsImage: {
                show: true,
                title: 'Save As Image'
            }
        }
    },
    legend: {
        orient: 'vertical',
        y: 'bottom',
        x: 'right',
        textStyle: {
            fontFamily: 'XB Niloofar',
        },
        data: []
    },
    calculable: true,
    series: [
        {
            name: '',
            type: 'pie',
            radius: '55%',
            center: ['50%', '60%'],
            data: [],
            itemStyle: {
                normal: {
                    label: {
                        textStyle: {
                            fontFamily: 'XB Niloofar',
                        },
                    },
                }
            },
        }
    ]
};

export const PIE_NESTED = {
    title: {
        text: '',
        x: 'center',
        textStyle: {
            fontFamily: 'XB Niloofar',
            color: '#333',
            fontWeight: 'normal',
            fontSize: 16,
            lineHeight: 20,
        }
    },
    color: ['#595b5d', '#e2d0ad', '#b3b3ff'],
    tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    toolbox: {
        show: true,
        feature: {
            mark: {
                show: true,
                title: {
                    mark: 'Add Guide',
                    markUndo: 'Remove Guide',
                    markClear: 'Remove all Guide',
                },
            },
            dataView: {
                show: true,
                readOnly: false,
                optionToContent: null,
                contentToOption: null,
                title: 'Data View',
                lang: ['Data View', 'Shutdown', 'Refresh'],
                buttonColor: '#01579B'
            },
            magicType: {
                show: true,
                type: ['pie', 'funnel'],
                title: {pie: 'pie', funnel: 'funnel'}
            },
            restore: {
                show: true,
                title: 'Reset'
            },
            saveAsImage: {
                show: true,
                title: 'Save As Image'
            }
        }
    },
    legend: {
        orient: 'vertical',
        y: 'bottom',
        x: 'right',
        textStyle: {
            fontFamily: 'XB Niloofar',
        },
        data: []
    },
    series: [
        {
            name: 'GROUP A',
            type: 'pie',
            radius: ['30%', '50%'],
            label: {
                normal: {
                    position: 'inner'
                }
            },
            labelLine: {
                normal: {
                    show: false
                }
            },
            itemStyle: {
                normal: {
                    label: {
                        textStyle: {
                            fontFamily: 'XB Niloofar',
                        },
                    },
                }
            },
            data: []
        },
        {
            name: 'GROUP A',
            type: 'pie',
            radius: ['55%', '70%'],
            label: {

                normal: {
                    show: false,
                    formatter: '{a|{a}}{abg|}\n{hr|}\n  {b|{b}：}{c}  {per|{d}%}  ',
                    backgroundColor: '#eee',
                    borderColor: '#aaa',
                    borderWidth: 1,
                    borderRadius: 4,
                    rich: {
                        a: {
                            color: '#999',
                            lineHeight: 22,
                            align: 'center'
                        },
                        hr: {
                            borderColor: '#aaa',
                            width: '100%',
                            borderWidth: 0.5,
                            height: 0
                        },
                        b: {
                            fontSize: 16,
                            lineHeight: 33
                        },
                        per: {
                            color: '#eee',
                            backgroundColor: '#334455',
                            padding: [2, 4],
                            borderRadius: 2
                        }
                    }
                }
            },
            itemStyle: {
                normal: {
                    label: {
                        textStyle: {
                            fontFamily: 'XB Niloofar',
                        },
                    },
                }
            },
            data: []
        }
    ]
};
