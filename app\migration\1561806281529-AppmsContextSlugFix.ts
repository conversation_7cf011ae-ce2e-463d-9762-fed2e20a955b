import {MigrationInterface, QueryRunner} from 'typeorm';

export class AppmsContextSlugFix1561806281529 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<any> {
        await queryRunner.query(`update contexts set slug =  'five-most-contingency-results-predicted'
                                        where  slug = 'five-most-contingency-results-predicted-widget'`);
        await queryRunner.query(`update contexts set slug =  'five-most-frequent-cpv-divisions'
                                        where  slug = 'five-most-frequent-cpv-divisions-widget'`);
        await queryRunner.query(`update contexts set slug =  'plan-vs-actual-project-number-and-amount-based-on-budget-type'
                                        where  slug = 'plan-and-actual-project-number-and-amount-based-on-budget-type'`);
        await queryRunner.query(`update contexts set slug =  'plan-vs-actual-project-number-and-amount-based-on-contract-type'
                                        where  slug = 'plan-and-actual-project-number-and-amount-based-on-contract-type'`);
        await queryRunner.query(`update contexts set slug =  'plan-vs-actual-project-number-and-amount-based-on-procurement-preference'
                                        where  slug = 'plan-and-actual-project-number-and-amount-based-on-procurement-preference'`);
        await queryRunner.query(`update contexts set slug =  'plan-vs-actual-project-number-and-amount-based-on-selection-method'
                                        where  slug = 'plan-and-actual-project-number-and-amount-based-on-selection-method'`);
        await queryRunner.query(`update contexts set slug =  'plan-vs-actual-project-number-and-amount-based-on-procurement-method'
                                        where  slug = 'plan-and-actual-project-number-and-amount-based-on-procurement-method'`);
        await queryRunner.query(`update contexts set slug =  'plan-vs-actual-project-number-and-amount-based-on-plan-status'
                                        where  slug = 'plan-and-actual-project-number-and-amount-based-on-plan-status'`);
        await queryRunner.query(`update contexts set slug =  'plan-vs-amount-project-based-on-province'
                                        where  slug = 'plan-and-amount-project-based-on-province'`);
        await queryRunner.query(`update contexts set slug =  'plan-vs-actual-project-amount-based-on-sector'
                                        where  slug = 'plan-and-actual-project-amount-based-on-sector'`);
        await queryRunner.query(`update contexts set slug =  'plan-vs-actual-project-number-based-on-sector'
                                        where  slug = 'plan-and-actual-project-number-based-on-sector'`);

    }

    public async down(queryRunner: QueryRunner): Promise<any> {
        await queryRunner.query(`update contexts set slug = 'five-most-contingency-results-predicted-widget'
                                        where slug =  'five-most-contingency-results-predicted'`);
        await queryRunner.query(`update contexts set slug = 'five-most-frequent-cpv-divisions-widget'
                                        where  slug ='five-most-frequent-cpv-divisions'`);
        await queryRunner.query(`update contexts set slug = 'plan-and-actual-project-number-and-amount-based-on-budget-type'
                                        where  slug = 'plan-vs-actual-project-number-and-amount-based-on-budget-type' `);
        await queryRunner.query(`update contexts set slug =  'plan-and-actual-project-number-and-amount-based-on-contract-type'
                                        where  slug = 'plan-vs-actual-project-number-and-amount-based-on-contract-type'`);
        await queryRunner.query(`update contexts set slug =   'plan-and-actual-project-number-and-amount-based-on-procurement-preference'
                                        where  slug = 'plan-vs-actual-project-number-and-amount-based-on-procurement-preference'`);
        await queryRunner.query(`update contexts set slug =  'plan-and-actual-project-number-and-amount-based-on-selection-method'
                                        where  slug = 'plan-vs-actual-project-number-and-amount-based-on-selection-method'`);
        await queryRunner.query(`update contexts set slug = 'plan-and-actual-project-number-and-amount-based-on-procurement-method'
                                        where  slug = 'plan-vs-actual-project-number-and-amount-based-on-procurement-method'`);
        await queryRunner.query(`update contexts set slug = 'plan-and-actual-project-number-and-amount-based-on-plan-status'
                                        where  slug = 'plan-vs-actual-project-number-and-amount-based-on-plan-status'`);
        await queryRunner.query(`update contexts set slug = 'plan-and-amount-project-based-on-province'
                                        where  slug = 'plan-vs-amount-project-based-on-province'`);
        await queryRunner.query(`update contexts set slug =  'plan-and-actual-project-amount-based-on-sector'
                                        where  slug ='plan-vs-actual-project-amount-based-on-sector'`);
        await queryRunner.query(`update contexts set slug = 'plan-and-actual-project-number-based-on-sector'
                                        where  slug ='plan-vs-actual-project-number-based-on-sector' `);

    }

}
