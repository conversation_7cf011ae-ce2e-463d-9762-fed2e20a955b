<div class="container" fxLayout="column" #container *ngFor="let other of otherFiles" class="other" fxFlex="25">
    <div class="buttons">
        <button type="button" mat-mini-fab class="close"
                *ngIf="other === showAddButton && other !==1 && other !== 1000 && uploadNumber"
                (click)="cancelFileUpload(other + 1)">
            <mat-icon>close</mat-icon>
        </button>
        <button type="button" mat-mini-fab class="add"
                *ngIf="other === showAddButton && other !== 1000 && uploadNumber"
                (click)="addNewFileUpload(other + 1)">
            <mat-icon>add</mat-icon>
        </button>
    </div>
    <div class="npa-label">
        <ng-content select="label"></ng-content>
    </div>
    <div fxLayout="column">
        <div class="npa-controls" fxLayout="row" (click)=" language[other].markAsTouched()">
            <button
                    type="button"
                    [disabled]="!uploadLogs"
                    mat-icon-button
                    (click)="openDialog()"
                    matTooltip="{{'SAVED_DOCUMENTS'|translate}}">
                <mat-icon>history</mat-icon>
            </button>
            <input
                    *ngIf="language[other].valid"
                    [id]="'inputFile_' + other"
                    type="file"
                    hidden
                    (change)="onFileChange($event, other)"
                    [multiple]="isMultiple">
            <button
                    mat-mini-fab
                    type="button"
                    (click)="selectFile(other,uploadNumber)"
                    matTooltip="{{'UPLOAD'|translate}}"
                    *ngIf="!npaForm?.get('other_' + other)?.value"
                    [disabled]="!language[other].valid">
                <mat-icon>file_upload</mat-icon>
            </button>

            <mat-chip-list>
                <mat-chip *ngIf="npaForm?.get('other_' + other)?.value">
                    <div class="npa-file-name" [style.width]="getFileNameWidth(container)">
                        {{ npaForm?.get('other_' + other)?.value?.name}}
                    </div>
                    <mat-icon matTooltip="{{'DELETE'|translate}}" (click)="deleteUploadedFile(other)">close
                    </mat-icon>
                </mat-chip>
            </mat-chip-list>
        </div>
        <div>
            <mat-form-field class="language" *ngIf="isControlSet">
                <mat-select [formControl]="language[other]" placeholder="{{'SELECT_DOCUMENT_LANG'|translate}}">
                    <mat-option *ngFor="let language of languages" [value]="language?.id">{{language?.name_da}}
                    </mat-option>
                </mat-select>
                <mat-error *ngIf="language[other].invalid">
                    <span>{{'SELECT_DOCUMENT_LANG_REQUIRED'|translate}}!</span>
                </mat-error>
            </mat-form-field>
        </div>
    </div>


</div>