import {BaseEntity, <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, OneToOne, PrimaryGeneratedColumn} from 'typeorm';
import {UserRolePe} from './user-role-pe';

@Entity('pe_resource_not_applicables')

export class PeResourceNotApplicable extends BaseEntity implements IPeResourceNotApplicable {

    @PrimaryGeneratedColumn()
    id: number;

    @Column()
    user_role_pe_id: number;

    @OneToOne(type => UserRolePe, userRolePe => userRolePe.id)
    @JoinColumn({name: 'user_role_pe_id'})
    userRolePe: UserRolePe;
}

export interface IPeResourceNotApplicable {
    id: number;
    user_role_pe_id: number;
    userRolePe: any | UserRolePe;
}



