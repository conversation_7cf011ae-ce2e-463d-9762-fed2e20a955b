@import '../../../../styles/definitions.styl'
.right-sidebar
  position relative
  z-index 5
  box-shadow 0 7px 8px -4px rgba(0,0,0,.2), 0 12px 17px 2px rgba(0,0,0,.14), 0 5px 22px 4px rgba(0,0,0,.12)
  .header
    -webkit-flex 1 1 auto
    flex 1 1 auto
    font-size 20px
    font-weight 500
    line-height 24px
    padding-left 0
    width auto
    word-wrap break-word
    margin 26px 32px 26px 12px
  .comment
    border-bottom 1px solid rgba(0, 0, 0, 0.1)
    padding-bottom 15px
    padding-top 10px
    .summary
      color rgba(0, 0, 0, 0.54)
  .actions
    padding-top 15px

.right-sidebar.open
  position fixed
  top sizeHeaderHeight
  left 0
  bottom 0
  width sizeSidebarUtilitiesOpen
  background-color white
  transition all .2s linear
  mat-expansion-panel
    margin-left 15px
    margin-right 15px

.right-sidebar.close
  width 0
  overflow hidden
  transition max-height 0.5s ease-in

.mat-expansion-panel-header-description
  white-space nowrap
  overflow hidden
  text-overflow ellipsis

.remarks-wrapper
  overflow-y scroll
  height 100%
  .mini-container
    .remark
      border-bottom 1px dotted colorTextLighter
      .summary
        color colorTextLighter
        margin-top 5px
        margin-bottom 7px
      .content
        text-align justify
    .remark:last-child
      border none

    .npa-remark-title1
      border-top 1px double colorTextLighter
      border-bottom 1px dotted colorTextLighter
    ::ng-deep .npa-label
      margin-top 0 !important

  ::ng-deep .spacer
    display block
    height 110px
