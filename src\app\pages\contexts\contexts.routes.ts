import {RouterModule, Routes} from '@angular/router';
import {ContextsComponent} from './contexts.component';
import {ContextListComponent} from './context-list/context-list.component';

export const contextsComponents = [
  ContextListComponent,
  ContextsComponent
];
const routes: Routes = [
  {
    path: '',
    component: ContextsComponent
  },
  {
    path: 'contexts-list',
    component: ContextListComponent
  }
];

export const ContextsRoutes = RouterModule.forChild(routes);
