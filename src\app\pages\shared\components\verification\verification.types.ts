import {BehaviorSubject} from 'rxjs';

export interface IVerificationDatabase {
    confirmation_timestamp: Date;
    approval_timestamp: Date;
    planning_general_confirmation_timestamp?: Date;
    planning_general_approval_timestamp?: Date;
}
export interface IVerificationBagDatabase {
    confirmation_timestamp: Date;
    approval_timestamp: Date;
}
export enum VERIFICATION_STYLES {
    SEPARATE_CARD,
    IN_CARD,
    IN_ROW
}

export interface IVerificationGeneral {
    id: number;
    url: string;
    style?: VERIFICATION_STYLES;
}

export interface IServiceVerifiable {
    verificationStateObserver: BehaviorSubject<IVerificationBagDatabase>;
    verificationUpdateState: (state: IVerificationBagDatabase) => void;
    verificationUrl: string;
}
