<div class="header no-select">
    <div class="sidebar-header-main"
         [ngClass]="isSidebarHeaderMainClose ? 'close': 'open'">
        <div class="logo" (click)="toggleSidebarMain()">
            <mat-icon>code</mat-icon>
        </div>
        <h1 class="app-title" lang="en">USM</h1>
        <button mat-icon-button class="sidebar-toggle-button">
            <mat-icon
                    class="mat-24"
                    (click)="toggleSidebarMain()">menu
            </mat-icon>
        </button>
    </div>

    <div class="header-remaining-items"
         [ngClass]="isSidebarHeaderUtilitiesClose ? 'close': 'open'">
        <!--<mat-form-field *ngIf="roles" class="role">-->
            <!--<mat-select placeholder="{{'CHOOSE_ROLE'|translate}}" #roleDropDown [value]="auth.user.value.role"-->
                        <!--(change)="switchRole($event.value)"-->
            <!--&gt;-->
                <!--<mat-option *ngFor="let role of roles" [value]="role">-->
                    <!--{{role.name}}-->
                <!--</mat-option>-->
            <!--</mat-select>-->
        <!--</mat-form-field>-->

        <mat-form-field>
            <mat-select placeholder="{{'SELECT_LANGUAGE'|translate}}" (selectionChange)="changeLang(language.value)"
                        #language [(value)]="lang">
                <mat-option value="en">English</mat-option>
                <mat-option value="prs">دری</mat-option>
                <mat-option value="ps">پښتو</mat-option>
            </mat-select>
        </mat-form-field>

        <a mat-icon-button routerLink="alert" *ngIf="auth.user.value.role.slug === 'admin'">
            <span class="new-alerts-marker" [ngClass]="{active: hasReceivedNewAlerts}"></span>
            <mat-icon>txtsms</mat-icon>
        </a>
        <button mat-icon-button [matMenuTriggerFor]="userMenu">
            <mat-icon>person</mat-icon>
        </button>
        <mat-menu #userMenu="matMenu">
            <div class="menu">
                <button mat-menu-item (click)="navigateTo()">
                    <mat-icon>account_circle</mat-icon>
                    {{'PROFILE'|translate}}
                </button>
                <button mat-menu-item (click)="logout()">
                    <mat-icon>exit_to_app</mat-icon>
                    {{'LOGOUT'|translate}}
                </button>
            </div>
        </mat-menu>
        <button mat-icon-button (click)="toggleSidebarUtilities()" class="sidebar-toggle-button"
                *ngIf="auth.user.value.role.slug === 'admin'"
        >
            <mat-icon class="to-close">{{lang === 'en' ? 'format_indent_increase': 'format_indent_decrease'}}</mat-icon>
            <mat-icon class="to-open">{{lang === 'en' ? 'format_indent_decrease': 'format_indent_increase'}}</mat-icon>
        </button>
    </div>

</div>
