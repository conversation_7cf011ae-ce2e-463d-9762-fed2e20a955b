import {MigrationInterface, QueryRunner} from 'typeorm';

export class AlterAPPMSContextAccess1564291508513 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<any> {
        await queryRunner.query(`
            update context_accesses
                set operation_id = 3
                where id in (
                    2139,
                    2140
                    )
              `);
    }

    public async down(queryRunner: QueryRunner): Promise<any> {
        await queryRunner.query(`
            update context_accesses
                set operation_id = 19
                where id = 2139
              `);
        await queryRunner.query(`
            update context_accesses
                set operation_id = 21
                where id = 2140
              `);
    }

}
