import {<PERSON><PERSON>out<PERSON>} from '../app.routes';
import {<PERSON><PERSON>ontroller} from './role.controller';
import {Authentication} from '../middlewares/authentication';

export const ROLE_ROUTES: IRoute[] = [
    {
        url: '',
        httpMethod: 'get',
        controllerMethod: RoleController.getInstance().index,
        // middleware: [Authentication.validateToken]
    },
    {
        url: '',
        httpMethod: 'post',
        controllerMethod: RoleController.getInstance().store,
        // middleware: [Authentication.validateToken]
    },
    {
        url: '/:id',
        httpMethod: 'get',
        controllerMethod: RoleController.getInstance().show,
        // middleware: [Authentication.validateToken]
    },
    {
        url: '/:id',
        httpMethod: 'put',
        controllerMethod: RoleController.getInstance().update,
        // middleware: [Authentication.validateToken]
    },
    {
        url: '/:id',
        httpMethod: 'delete',
        controllerMethod: RoleController.getInstance().destroy,
        // middleware: [Authentication.validateToken]
    },

];
