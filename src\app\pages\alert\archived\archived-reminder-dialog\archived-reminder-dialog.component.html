<button mat-icon-button [mat-dialog-close]="true" matTooltip="بستن" class="dialog-close-button">
    <mat-icon>close</mat-icon>
</button>
<form [formGroup]="archivedReminderDialog">
    <h1 mat-dialog-title>یادآوری</h1>
    <mat-dialog-content class="dialog" fxLayout="row wrap" fxLayoutAlign="start">
        <npa-view-element fxFlex="50">
            <div class="npa-label">ای دی :</div>
            <div class="npa-value">xxx</div>
        </npa-view-element>
        <npa-view-element fxFlex="50">
            <div class="npa-label">عنوان :</div>
            <div class="npa-value">xxx</div>
        </npa-view-element>
        <mat-form-field fxFlex="50">
            <input matInput [matDatepicker]="date1" placeholder="تاریخ شمسی" formControlName="Jala<PERSON>">
            <mat-datepicker-toggle matSuffix [for]="date1"></mat-datepicker-toggle>
            <mat-datepicker #date1></mat-datepicker>
            <mat-error *ngIf="archivedReminderDialog && archivedReminderDialog.get('Jalali').invalid
                && archivedReminderDialog.get('Jalali').errors.required">
                {{formValidationService.required.message()}}
            </mat-error>
        </mat-form-field>
        <mat-form-field fxFlex="50">
            <input input matInput [matDatepicker]="date2" placeholder="تاریخ میلادی" [matDatepicker]="date2"
                   formControlName="Gregorian">
            <mat-datepicker-toggle matSuffix [for]="date2"></mat-datepicker-toggle>
            <mat-datepicker #date2></mat-datepicker>
            <mat-error *ngIf="archivedReminderDialog && archivedReminderDialog.get('Gregorian').invalid
                 && archivedReminderDialog.get('Gregorian').errors.required">
                {{formValidationService.required.message()}}
            </mat-error>
        </mat-form-field>
    </mat-dialog-content>
    <mat-dialog-actions>
        <button mat-raised-button type="submit">ثبت</button>
        <button mat-raised-button [mat-dialog-close]="true">لغو</button>
    </mat-dialog-actions>
</form>

