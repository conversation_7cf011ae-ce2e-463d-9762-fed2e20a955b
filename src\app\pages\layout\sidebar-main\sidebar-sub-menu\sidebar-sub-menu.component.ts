import {Component, Input, OnInit} from '@angular/core';
import {AuthService} from '../../../../services/auth.service';
import {Location} from '@angular/common';
import {ComplaintService, IComplaintService} from '../../../../services/complaint.service';
import {NavigationEnd, Router} from '@angular/router';
import {TranslateService} from '../../../shared/services/translate.service';
import {CONTEXTS_LIST} from '../../../../../environments/environment.const';
import {environment} from '../../../../../environments/environment';

// import {ProcurementEntityResponseService} from '../../../complaint/procurement-entity-response/procurement-entity-response.service';

@Component({
    selector: 'npa-sidebar-sub',
    templateUrl: './sidebar-sub-menu.component.html',
    styleUrls: ['./sidebar-sub-menu.component.styl']
})

export class SidebarSubMenuComponent implements OnInit {
    path = '';
    selectedPath = '';
    contextsList = [];
    isComplaintSelected = false;
    @Input() isSidebarMainClose;
    lang: string;
    arrowIcon: string;
    EntityResponseLastURL: any;

    constructor(public auth: AuthService,
                private _location: Location,
                private _complaintService: ComplaintService,
                // private _procurementEntityResponse: ProcurementEntityResponseService,
                private _router: Router,
                private translate: TranslateService) {
    }


    ngOnInit() {
        this.lang = this.translate.lang;
        this._complaintService.complaintInformation.subscribe((data: IComplaintService) => {
            this.isComplaintSelected = data.isComplaintSelected;
        });

        this.selectedPath = this._location.prepareExternalUrl(this._location.path());
        const urlArray = this.selectedPath.split('/');
        // this.path = urlArray.slice(0, urlArray.indexOf(this.auth.user.value.role.slug) + 3).join('/');
        this.path = urlArray[urlArray.length - 1];
        this.setActiveTab(urlArray);
        this._router.events.subscribe((event) => {
            if (!(event instanceof NavigationEnd)) {
                return;
            }
            const clickedUrlArray = event.url.split('/');
            this.setActiveTab(clickedUrlArray);
        });

        if (!environment.loginThroughDataBase) {
            this.auth.getUserContexts(this.auth.user.value.role.id).subscribe(contexts => {
                this.contextsList = contexts;
            });
            this.auth.user.subscribe(() => {
                this.auth.getUserContexts(this.auth.user.value.role.id).subscribe(contexts => {
                    this.contextsList = contexts;
                });
            });
        } else {
            this.contextsList = this.auth.user.value.role.contexts;
        }
        this.contextsList = this.auth.user.value.role.slug === 'dev-ops' ? CONTEXTS_LIST[0].accessibleContexts : CONTEXTS_LIST[1].accessibleContexts;
        this.arrowIcon = this.lang === 'prs' || 'ps' ? 'keyboard_arrow_left' : 'keyboard_arrow_right';
    }

    hasAccess(path: string): boolean {
        for (let i = 0; i < this.contextsList.length; i++) {
            // if (this.contextsList[i].slug === path) {
            if (this.contextsList[i].name === path) {
                return true;
            }
        }
        return false;
    }

    setActiveTab(array) {
        if ((array.length - 1) <= array.indexOf('user-list') || array.indexOf('user-list') === -1) {
            this.isComplaintSelected = false;
        } else {
            this.isComplaintSelected = true;
        }
        if (this.auth.user.value) {
            // this.path = array.length > 4 ?
            //   array.slice(0, array.indexOf(this.auth.user.value.role.slug) + 2).join('/') :
            //   array.slice(0, array.indexOf(this.auth.user.value.role.slug) + 2).join('/');
            this.selectedPath = array.length > 4 ?
                array.slice(0, array.indexOf(this.auth.user.value.role.slug) + 3).join('/') :
                array.slice(0, array.indexOf(this.auth.user.value.role.slug) + 3).join('/');
        }

    }

    onTabSelection(pathString, isPathAvailable = true) {
        if (isPathAvailable) {
            this.path = pathString;
        }
        this.selectedPath = pathString;
    }

}
