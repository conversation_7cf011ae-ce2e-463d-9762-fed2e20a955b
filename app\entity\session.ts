import {<PERSON><PERSON><PERSON><PERSON>, Colum<PERSON>, CreateDate<PERSON><PERSON><PERSON>n, En<PERSON><PERSON>, Jo<PERSON>Colum<PERSON>, ManyToOne, PrimaryGeneratedColumn, UpdateDateColumn} from 'typeorm';
import {UserRole} from './user-role';

@Entity('sessions')
export class Session extends BaseEntity implements ISession {

    @PrimaryGeneratedColumn('uuid')
    id: string;

    @Column({name: 'client_signature'})
    client_signature: string;

    @Column()
    revoked: boolean;

    @UpdateDateColumn({name: 'updated_at'})
    updated_at: Date;

    @CreateDateColumn({name: 'created_at'})
    created_at: Date;

    @Column()
    user_role_id: number;

    @ManyToOne(type => UserRole, userRole => userRole.id, { onDelete: 'CASCADE' })
    @JoinColumn({name: 'user_role_id'})
    user_role: UserRole;

}

export interface ILogin {
    username: string;
    password: string;
}

export interface ISession {
    id?: string;
    client_signature: string | string[];
    revoked: boolean;
    updated_at?: Date | any;
    created_at?: Date | any;
    user_role_id: number | UserRole;

}





