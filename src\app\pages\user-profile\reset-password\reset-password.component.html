<div class="container" fxLayout="row wrap" fxFlexAlign="start">
    <div fxFlex="84">{{'RESET_PASSWORD'|translate}}</div>
    <button mat-icon-button matTooltip="{{'CLOSE'|translate}}" [mat-dialog-close]="false" class="dialog-close-button">
        <mat-icon>close</mat-icon>
    </button>
    <form *ngIf="form" [formGroup]="form" (ngSubmit)="resetPassword()" fxLayout="row wrap" fxFlexAlign="start">
        <mat-form-field fxFlex="100">
            <input type="password" formControlName="password" matInput
                   placeholder="{{'CURRENT_PASSWORD'|translate}} ">
            <mat-error *ngIf="form?.get('password')?.errors?.required">
                {{'REQUIRED_FIELD_MESSAGE'|translate}}
            </mat-error>
            <mat-error *ngIf="!form?.get('password')?.valid && !form?.get('password')?.errors?.required">
                {{'PASSWORD_PATTERN_INVALID_MESSAGE'|translate}}
            </mat-error>
        </mat-form-field>
        <mat-form-field fxFlex="100">
            <input type="password" formControlName="new_password" matInput
                   placeholder="{{'NEW_PASSWORD'|translate}} ">
            <mat-error *ngIf="form?.get('new_password')?.errors?.required">
                {{'REQUIRED_FIELD_MESSAGE'|translate}}
            </mat-error>
            <mat-error
                    *ngIf="!form?.get('new_password')?.valid && !form?.get('new_password')?.errors?.required">
                {{'PASSWORD_PATTERN_INVALID_MESSAGE'|translate}}
            </mat-error>
        </mat-form-field>
        <mat-form-field fxFlex="100">
            <input type="password" formControlName="repeat_password" matInput
                   placeholder="{{'CONFIRM_NEW_PASSWORD'|translate}} ">
            <mat-error *ngIf="form.get('repeat_password')?.errors?.required">
                {{'REQUIRED_FIELD_MESSAGE'|translate}}
            </mat-error>
            <mat-error
                    *ngIf="form?.get('repeat_password')?.errors?.match && !form.get('repeat_password').errors.required">
                {{'PASSWORD_NOT_MATCH_MESSAGE'|translate}}
            </mat-error>
        </mat-form-field>
        <br><br>
        <button class="submit" mat-button type="submit"
                [disabled]="!form.valid || notificationsService.isLoading">{{'SAVING'|translate}}</button>
    </form>
</div>
