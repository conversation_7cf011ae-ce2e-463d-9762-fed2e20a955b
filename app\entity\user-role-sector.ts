import {BaseEntity, Column, Entity, Index, JoinColumn, ManyToOne, OneToOne, PrimaryGeneratedColumn} from 'typeorm';
import {UserRole} from './user-role';
import {SectorResourceNotApplicable} from './sector-resource-not-applicable';

@Entity('user_role_sectors')
@Index('unique_sector_id_and_user_role_id', ['sector_id', 'user_role_id'], {unique: true})
export class UserRoleSector extends BaseEntity implements IUserRoleSector {

    @PrimaryGeneratedColumn()
    id: number;

    @Column({name: 'sector_id'})
    sector_id: number;

    @Column({name: 'user_role_id'})
    user_role_id: number;

    @ManyToOne(type => UserRole, userRole => userRole.id, {onDelete: 'CASCADE'})
    @JoinColumn({name: 'user_role_id'})
    userRole: UserRole;

    @OneToOne(type => SectorResourceNotApplicable,
        sectorResourceNotApplicable => sectorResourceNotApplicable.userRoleSector) // specify inverse side as a second parameter
    sectorResourceNotApplicable: SectorResourceNotApplicable;
}

export interface IUserRoleSector {
    id?: number;
    sector_id: number;
    user_role_id: number;
    userRole: any | UserRole;
    sectorResourceNotApplicable: SectorResourceNotApplicable | any;
}


