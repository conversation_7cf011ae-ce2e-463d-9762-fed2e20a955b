import {Router} from 'express';
import {USER_ROUTES} from './user/user.routes';
import {Middleware} from './middlewares/middleware';
import {ROLE_ROUTES} from './role/role.routes';
import {SESSION_ROUTES} from './session/session.routes';
import {SYSTEM_ROUTES} from './system/system.routes';
import {STATUS_ROUTES} from './status/status.routes';
import {RESOURCE_ROUTES} from './resource/user.routes';
import {SYSTEM_SPECIFIC_ROUTES} from './system-specific/system-specific.routes';
import {CONTEXT_ROUTES} from './context/context.routes';

export interface IRoute {
    url: string;
    httpMethod: string;
    controllerMethod: any;
    validatorsList?: any[];
    middleware?: any[];
}

export class AppRoutes {
    private router = Router();
    routes = [];

    constructor() {
        this.init();
    }

    init() {
        this.routes = [
            this.router.use('/api', Middleware.directTo(Router(), SESSION_ROUTES)),
            this.router.use('/api/users', Middleware.directTo(Router(), USER_ROUTES)),
            this.router.use('/api/roles', Middleware.directTo(Router(), ROLE_ROUTES)),
            this.router.use('/api/systems', Middleware.directTo(Router(), SYSTEM_ROUTES)),
            this.router.use('/api/statutes', Middleware.directTo(Router(), STATUS_ROUTES)),
            this.router.use('/api/resources', Middleware.directTo(Router(), RESOURCE_ROUTES)),
            this.router.use('/api/system-specific', Middleware.directTo(Router(), SYSTEM_SPECIFIC_ROUTES)),
            this.router.use('/api/contexts', Middleware.directTo(Router(), CONTEXT_ROUTES)),
        ];
    }

}

export const Routes = (new AppRoutes()).routes;





