import {NgModule} from '@angular/core';
import {CommonModule} from '@angular/common';
import {
    MatAutocompleteModule,
    MatButtonModule,
    MatCardModule,
    MatCheckboxModule,
    MatDatepickerModule,
    MatDialogModule,
    MatExpansionModule,
    MatFormFieldModule,
    MatIconModule,
    MatInputModule,
    MatNativeDateModule,
    MatPaginatorModule,
    MatRadioModule,
    MatSelectModule,
    MatTableModule,
    MatTabsModule,
    MatToolbarModule,
    MatTooltipModule
} from '@angular/material';
import {SharedModule} from '../shared/shared.module';
import {TranslateService} from '../shared/services/translate.service';
import {MaterialModule} from '../../../material.module';
import {RouterModule} from '@angular/router';
import {FormsModule, ReactiveFormsModule} from '@angular/forms';
import {FlexLayoutModule} from '@angular/flex-layout';
import {reportsComponents, reportsRoutes} from './reports.route';

@NgModule({
    imports: [
        CommonModule,
        CommonModule,
        FormsModule,
        MatCardModule,
        MatTableModule,
        MatToolbarModule,
        MatIconModule,
        MatButtonModule,
        MatExpansionModule,
        FlexLayoutModule,
        MatFormFieldModule,
        MatInputModule,
        MatRadioModule,
        MatSelectModule,
        MatCheckboxModule,
        MatPaginatorModule,
        MatNativeDateModule,
        MatDatepickerModule,
        ReactiveFormsModule,
        MatTooltipModule,
        MatDialogModule,
        MatTabsModule,
        MaterialModule,
        MatAutocompleteModule,
        RouterModule,
        reportsRoutes,
        SharedModule
    ],
    declarations: [
        ...reportsComponents,
    ],
    entryComponents: [],
    providers: [
        TranslateService
    ]
})
export class ReportsModule {
}
