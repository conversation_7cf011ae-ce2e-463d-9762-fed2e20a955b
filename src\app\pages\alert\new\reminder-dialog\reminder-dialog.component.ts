import {Component, Inject, OnInit} from '@angular/core';
import {FormBuilder, FormGroup} from '@angular/forms';
import {MAT_DIALOG_DATA, MatDialogRef} from '@angular/material';
import {FormValidationService} from '../../../../services/form-validation.service';

@Component({
    selector: 'npa-reminder-dialog',
    templateUrl: './reminder-dialog.component.html',
    styleUrls: ['./reminder-dialog.component.styl']
})
export class ReminderDialogComponent implements OnInit {
    reminderDialog: FormGroup;

    constructor(public dialogRef: MatDialogRef<ReminderDialogComponent>,
                @Inject(MAT_DIALOG_DATA) public data: any,
                private formBuilder: FormBuilder,
                public formValidationService: FormValidationService) {
        this.createForm();
    }

    createForm() {
        this.reminderDialog = this.formBuilder.group({
            reminder_date: ['', this.formValidationService.required.validator],
       });
    }

    save() {
        this.dialogRef.close(this.data);
    }

    ngOnInit() {
    }
}
