<ul class="main" [ngClass]=" isSidebarMainClose  ? 'main-sidebar-closed' : ''" [lang]="lang">
    <li
        [ngClass]=" path ===  'dashboard' ? 'expand' : ''">
        <a href="#" mat-ripple [routerLink]="'dashboard'"
           (click)="onTabSelection('dashboard')">
            <mat-icon [ngClass]=" selectedPath ===  'dashboard' ? 'selected' : ''">
                dashboard
            </mat-icon>
            <span *ngIf="!isSidebarMainClose">{{ "DASHBOARD" | translate }}</span>
            <mat-icon *ngIf="!isSidebarMainClose" class="menu-arrow">{{ arrowIcon }}</mat-icon>
        </a>
        <ul>
            <li *ngIf="hasAccess('charts') ">
                <a href="#" mat-ripple [routerLink]="'dashboard/charts'"
                   (click)="onTabSelection('dashboard/charts', false)">
                    <mat-icon
                            [ngClass]=" selectedPath === 'dashboard/charts' ? 'selected' : ''">
                        dashboard
                    </mat-icon>
                    <span *ngIf="!isSidebarMainClose">{{ "CHARTS" | translate }}</span>
                </a>
            </li>
            <li *ngIf="hasAccess('user-list') ">
                <a href="#" mat-ripple [routerLink]="'dashboard/user-list'"
                   (click)="onTabSelection('dashboard/user-list', false)">
                    <mat-icon
                            [ngClass]=" selectedPath === 'dashboard/user-list' ? 'selected' : ''">
                        dashboard
                    </mat-icon>
                    <span *ngIf="!isSidebarMainClose">{{ "USER_LIST" | translate }}</span>
                </a>
            </li>
        </ul>
    </li>
    <!--<li
            [ngClass]=" path === 'dashboard' ? 'expand' : ''">
        <a href="#" mat-ripple [routerLink]="'dashboard'"
           (click)="onTabSelection('dashboard')">
            <mat-icon [ngClass]=" selectedPath === 'dashboard' ? 'selected' : ''">
                dashboard
            </mat-icon>
            <span *ngIf="!isSidebarMainClose">{{ "DASHBOARD" | translate }}</span>
        </a>
    </li>
    <li *ngIf="hasAccess('systems') "
        [ngClass]=" path === 'systems' ? 'expand' : ''">
        <a href="#" mat-ripple [routerLink]="'systems'"
           (click)="onTabSelection('systems')">
            <mat-icon [ngClass]=" selectedPath ===  'systems' ? 'selected' : ''">
                dashboard
            </mat-icon>
            <span *ngIf="!isSidebarMainClose">{{ "SYSTEMS" | translate }}</span>
            <mat-icon *ngIf="!isSidebarMainClose" class="menu-arrow">{{ arrowIcon }}</mat-icon>
        </a>
        <ul>
            <li *ngIf="hasAccess('systems-list')">
                <a href="#" mat-ripple [routerLink]=" 'systems'"
                   (click)="onTabSelection('systems', false)">
                    <mat-icon
                            [ngClass]=" selectedPath ===  'systems' ? 'selected' : ''">
                        dashboard
                    </mat-icon>
                    <span *ngIf="!isSidebarMainClose">{{ "SYSTEMS_LIST" | translate }}</span>
                </a>
            </li>
        </ul>
    </li>
    <li *ngIf="hasAccess('contexts') "
        [ngClass]=" path ===  'contexts' ? 'expand' : ''">
        <a href="#" mat-ripple [routerLink]=" 'contexts'"
           (click)="onTabSelection('contexts')">
            <mat-icon [ngClass]=" selectedPath ===   'contexts' ? 'selected' : ''">
                dashboard
            </mat-icon>
            <span *ngIf="!isSidebarMainClose">{{ "CONTEXTS" | translate }}</span>
            <mat-icon *ngIf="!isSidebarMainClose" class="menu-arrow">{{ arrowIcon }}</mat-icon>
        </a>
        <ul>
            <li *ngIf="hasAccess('contexts-list')">
                <a href="#" mat-ripple [routerLink]=" 'contexts/contexts-list'"
                   (click)="onTabSelection('contexts/contexts-list', false)">
                    <mat-icon
                            [ngClass]=" selectedPath ===  'contexts/contexts-list' ? 'selected' : ''">
                        dashboard
                    </mat-icon>
                    <span *ngIf="!isSidebarMainClose">{{ "CONTEXTS_LIST" | translate }}</span>
                </a>
            </li>
        </ul>
    </li>
    <li *ngIf="hasAccess('roles') "
        [ngClass]=" path ===  'roles' ? 'expand' : ''">
        <a href="#" mat-ripple [routerLink]=" 'roles'"
           (click)="onTabSelection('roles')">
            <mat-icon [ngClass]=" selectedPath ===   'roles' ? 'selected' : ''">
                dashboard
            </mat-icon>
            <span *ngIf="!isSidebarMainClose">{{ "ROLES" | translate }}</span>
            <mat-icon *ngIf="!isSidebarMainClose" class="menu-arrow">{{ arrowIcon }}</mat-icon>
        </a>
        <ul>
            <li *ngIf="hasAccess('roles-list')">
                <a href="#" mat-ripple [routerLink]=" 'roles/roles-list'"
                   (click)="onTabSelection('roles/roles-list', false)">
                    <mat-icon
                            [ngClass]=" selectedPath === 'roles/roles-list' ? 'selected' : ''">
                        dashboard
                    </mat-icon>
                    <span *ngIf="!isSidebarMainClose">{{ "ROLES_LIST" | translate }}</span>
                </a>
            </li>
        </ul>
    </li>
    <li *ngIf="hasAccess('user-guide')"
        [ngClass]=" path ===  'user-guide' ? 'expand' : 'shrink'">
        <a href="#" mat-ripple [routerLink]=" 'user-guide'"
           (click)="onTabSelection('user-guide')">
            <mat-icon [ngClass]=" selectedPath ===  'user-guide' ? 'selected' : ''">help
            </mat-icon>
            <span *ngIf="!isSidebarMainClose">{{"SYSTEM_GUIDE"| translate}}</span>
            <mat-icon *ngIf="!isSidebarMainClose" class="menu-arrow">{{ arrowIcon }}</mat-icon>
        </a>
        <ul>
            <li *ngIf="hasAccess('system-information')"
                (click)="onTabSelection('user-guide/system-information', false)">
                <a href="#" mat-ripple [routerLink]=" 'user-guide/system-information'"
                >
                    <mat-icon
                            [ngClass]=" selectedPath ===  'user-guide/system-information' ? 'selected' : ''">
                        help
                    </mat-icon>
                    <span *ngIf="!isSidebarMainClose">{{"SYSTEM_SPECIFICATIONS"| translate}}</span>
                </a>
            </li>
            <li *ngIf="hasAccess('change-log')" (click)="onTabSelection('user-guide/change-log', false)">
                <a href="#" mat-ripple [routerLink]=" 'user-guide/change-log'">
                    <mat-icon
                            [ngClass]=" selectedPath ===  'user-guide/change-log' ? 'selected' : ''">
                        help
                    </mat-icon>
                    <span *ngIf="!isSidebarMainClose">{{'CHANGE_LOG' | translate}}</span>
                </a>
            </li>
            <li *ngIf="hasAccess('contact-information')"
                (click)="onTabSelection('user-guide/contact-information', false)">
                <a href="#" mat-ripple [routerLink]=" 'user-guide/contact-information'">
                    <mat-icon
                            [ngClass]=" selectedPath ===  'user-guide/contact-information' ? 'selected' : ''">
                        help
                    </mat-icon>
                    <span *ngIf="!isSidebarMainClose">{{'CONTACT_INFORMATION' | translate}}</span>
                </a>
            </li>
            <li *ngIf="hasAccess('user-manual')" (click)="onTabSelection('user-guide/user-manual', false)">
                <a href="#" mat-ripple [routerLink]=" 'user-guide/user-manual'">
                    <mat-icon
                            [ngClass]=" selectedPath ===  'user-guide/user-manual' ? 'selected' : ''">
                        help
                    </mat-icon>
                    <span *ngIf="!isSidebarMainClose">{{'USER_MANUAL' | translate}}</span>
                </a>
            </li>
            <li *ngIf="hasAccess('reset-system')" (click)="onTabSelection('user-guide/reset-system', false)">
                <a href="#" mat-ripple [routerLink]=" 'user-guide/reset-system'">
                    <mat-icon
                            [ngClass]=" selectedPath ===  'user-guide/reset-system' ? 'selected' : ''">
                        help
                    </mat-icon>
                    <span *ngIf="!isSidebarMainClose">{{'RESET_SYSTEM' | translate}}</span>
                </a>
            </li>
        </ul>
    </li> -->

    <!--<li *ngIf="hasAccess('sessions')"-->
    <!--[ngClass]=" path ===  'sessions' ? 'expand' : ''">-->
    <!--<a href="#" mat-ripple [routerLink]=" 'sessions'"-->
    <!--(click)="path = selectedPath =  'sessions'">-->
    <!--<mat-icon [ngClass]=" selectedPath ===   'sessions' ? 'selected' : ''">-->
    <!--dashboard-->
    <!--</mat-icon>-->
    <!--<span *ngIf="!isSidebarMainClose">{{ "SESSIONS" | translate }}</span>-->
    <!--<mat-icon *ngIf="!isSidebarMainClose" class="menu-arrow">{{ arrowIcon }}</mat-icon>-->
    <!--</a>-->
    <!--<ul>-->
    <!--<li *ngIf="hasAccess('sessions-list')">-->
    <!--<a href="#" mat-ripple [routerLink]=" 'sessions/sessions-list'"-->
    <!--(click)="selectedPath =  'sessions/sessions-list'">-->
    <!--<mat-icon-->
    <!--[ngClass]=" selectedPath ===  'sessions/sessions-list' ? 'selected' : ''">-->
    <!--dashboard-->
    <!--</mat-icon>-->
    <!--<span *ngIf="!isSidebarMainClose">{{ "SESSIONS_LIST" | translate }}</span>-->
    <!--</a>-->
    <!--</li>-->
    <!--</ul>-->
    <!--</li>-->
</ul>
