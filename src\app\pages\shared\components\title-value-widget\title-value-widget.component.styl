@import '.././../../../../styles/definitions.styl'

.container
  width 100%
  mat-card
    padding 0 !important
    .title
      padding 5px 24px 5px 35px
      height 35px
      background colorDark
      display flex
      align-items center
      justify-content center
      color #fff
      text-align center
      font-size 14px
    .value
      height 30px
      padding 5px 24px
      color #333
      display flex
      justify-content center
      align-items center

      font-size 14px

.card
  overflow hidden
  transition transform .5s ease-out 0s, visibility 0s ease-in .2s, opacity 0s ease-in .2s, -webkit-transform .5s ease-out 0s
  .back
    display none

.rotate
  transform rotateX(180deg)
  -webkit-transform rotateY(180deg)
  .front
    visibility hidden

  .back
    display block
    visibility visible
    transform rotateX(-180deg)
    -webkit-transform rotateY(-180deg)
    position absolute
    top 30px
    left 0
    margin 5px 15px
    font-size 11px
    text-align justify

.action
  position absolute
  z-index 1
  left 1px
  top 1px

.frontcolor
  color #fff !important

mat-card {
  margin: 5px !important;
}