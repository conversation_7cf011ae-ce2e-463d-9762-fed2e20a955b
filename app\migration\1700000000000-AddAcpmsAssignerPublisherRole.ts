import {MigrationInterface, QueryRunner} from 'typeorm';

export class AddAcpmsAssignerPublisherRole1700000000000 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<any> {
        try {
            // First, let's check if cpms-contract-director role exists and get its ID
            // If it doesn't exist, we'll create it first
            const contractDirectorCheck = await queryRunner.query(`
                SELECT id FROM roles WHERE slug = 'cpms-contract-director'
            `);
            
            let contractDirectorRoleId: number;
            
            if (contractDirectorCheck.length === 0) {
                // Create the cpms-contract-director role if it doesn't exist
                await queryRunner.query(`
                    INSERT INTO roles (id, name, slug, party, can_be_acted, can_act, is_pe_based, is_sector_based, is_vendor_based, is_record_based, system_id)
                    VALUES (24, 'contract-director', 'cpms-contract-director', 'pe', true, false, true, false, false, false, 2)
                `);
                contractDirectorRoleId = 24;
            } else {
                contractDirectorRoleId = contractDirectorCheck[0].id;
            }

            // Create the new composite role acpms-assigner-publisher
            await queryRunner.query(`
                INSERT INTO roles (name, slug, party, can_be_acted, can_act, is_pe_based, is_sector_based, is_vendor_based, is_record_based, system_id)
                VALUES ('assigner-publisher', 'acpms-assigner-publisher', 'pe', true, false, true, false, false, false, 2)
            `);

            // Get the ID of the newly created role
            const newRoleResult = await queryRunner.query(`
                SELECT id FROM roles WHERE slug = 'acpms-assigner-publisher'
            `);
            const newRoleId = newRoleResult[0].id;

            // Copy all context accesses from cpms-award-authority (role ID 2) to the new role
            await queryRunner.query(`
                INSERT INTO context_accesses (operation_id, is_enabled, role_id, context_id)
                SELECT operation_id, is_enabled, ${newRoleId}, context_id
                FROM context_accesses 
                WHERE role_id = 2 AND is_enabled = 1
            `);

            // Copy all context accesses from cpms-contract-director to the new role
            // Only add permissions that don't already exist to avoid duplicates
            await queryRunner.query(`
                INSERT INTO context_accesses (operation_id, is_enabled, role_id, context_id)
                SELECT DISTINCT cd.operation_id, cd.is_enabled, ${newRoleId}, cd.context_id
                FROM context_accesses cd
                WHERE cd.role_id = ${contractDirectorRoleId} 
                AND cd.is_enabled = 1
                AND NOT EXISTS (
                    SELECT 1 FROM context_accesses existing 
                    WHERE existing.role_id = ${newRoleId} 
                    AND existing.operation_id = cd.operation_id 
                    AND existing.context_id = cd.context_id
                )
            `);

            console.log(`Created composite role 'acpms-assigner-publisher' with ID ${newRoleId}`);
            console.log(`Inherited permissions from 'cpms-award-authority' (ID: 2) and 'cpms-contract-director' (ID: ${contractDirectorRoleId})`);

        } catch (e) {
            console.error('Error creating acpms-assigner-publisher role:', e);
            throw e;
        }
    }

    public async down(queryRunner: QueryRunner): Promise<any> {
        try {
            // Get the role ID first
            const roleResult = await queryRunner.query(`
                SELECT id FROM roles WHERE slug = 'acpms-assigner-publisher'
            `);
            
            if (roleResult.length > 0) {
                const roleId = roleResult[0].id;
                
                // Delete all context accesses for this role
                await queryRunner.query(`
                    DELETE FROM context_accesses WHERE role_id = ${roleId}
                `);
                
                // Delete any user roles associated with this role
                await queryRunner.query(`
                    DELETE FROM user_roles WHERE role_id = ${roleId}
                `);
                
                // Delete the role itself
                await queryRunner.query(`
                    DELETE FROM roles WHERE slug = 'acpms-assigner-publisher'
                `);
            }
            
        } catch (e) {
            console.error('Error rolling back acpms-assigner-publisher role:', e);
            throw e;
        }
    }
}
