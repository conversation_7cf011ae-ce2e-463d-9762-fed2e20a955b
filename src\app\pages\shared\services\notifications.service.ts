import {Injectable} from '@angular/core';
import {MatDialog, MatSnackBar} from '@angular/material';
import {SnackbarMessageComponent} from '../snackbar-message/snackbar-message.component';
import {NotificationDialogComponent} from '../notification-dialog/notification-dialog.component';
import {environment} from '../../../../environments/environment';
import {TranslateService} from './translate.service';

@Injectable()
export class NotificationsService {

    isLoading: boolean;

    constructor(public snackBar: MatSnackBar,
                public translate: TranslateService,
                private _dialog: MatDialog) {

    }

    startLoading() {
        setTimeout(() => {
            this.isLoading = true;
            this.snackBar.openFromComponent(SnackbarMessageComponent);
        }, 0.0);
    }

    dismissLoading(message = null, statusCode = null) {
        setTimeout(() => {
            this.isLoading = false;
            this.snackBar.dismiss();
            switch (true) {
                case +statusCode >= 200 && +statusCode < 300:
                    this.error(message);
                    break;
                case +statusCode >= 400 && +statusCode < 600 : // +statusCode == 0 must be applied if No connection
                    this.error(message);
                    break;
                default:
                    break;
            }
        }, 0.0);

    }

    error(message) {
        if (!message) {
            message = this.translate.translateKey('ERROR');
        }
        if (message.length > 100) {
            this.openDialog(message, this.translate.translateKey('ERROR'), 'error');
            return;
        }
        this.openSnackbarComponent(message, 'error', 'close');
        this.isLoading = false;
    }

    warn(message) {
        if (message.length > 100) {
            this.openDialog(message, this.translate.translateKey('WARNING'), 'warn');
            return;
        }
        this.openSnackbarComponent(message, 'warn', 'warning');
        this.isLoading = false;
    }

    info(message) {
        if (message.length > 100) {
            this.openDialog(message, this.translate.translateKey('INFORMATION'), 'info');
            return;
        }
        this.openSnackbarComponent(message, 'info', 'info');
        this.isLoading = false;
    }

    success(message) {
        if (!message) {
            message = this.translate.translateKey('SUCCESSFULLY_DONE');
        }
        if (message.length > 100) {
            this.openDialog(message, this.translate.translateKey('SUCCESSFULLY_DONE'), 'success');
            return;
        }
        this.openSnackbarComponent(message, 'success', 'check');
        this.isLoading = false;
    }

    openSnackbarComponent(message, status, icon) {

        this.snackBar.openFromComponent(SnackbarMessageComponent, {
            data: {
                message: message,
                status: status,
                icon: icon
            },
            duration: 3000
        });
    }

    openDialog(message, title, type) {
        this._dialog.open(NotificationDialogComponent, {
            width: '500px',
            data: {title: title, message: message, type: type}
        });
    }

    registerDesktopNotification() {
        if (!Notification) {
            return;
        }

        if ((<any>Notification).permission !== 'granted') {
            Notification.requestPermission().catch((error: Error) => {
                this.error(this.error(error.message));
                console.error(error);
            });
        }
    }

    infoDesktop(message: string, url?: string) {
        const notification = new Notification('APPMS', {
            icon: `${environment.baseUrl.backend.main}logo.png`,
            body: message,
        });

        if (url) {
            notification.onclick = () => {
                window.open(url);
                notification.close();
            };
        }
    }
}
