import {MigrationInterface, QueryRunner} from 'typeorm';

export class AppmsContextAccessFix1561886591548 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<any> {
        await queryRunner.query(`Delete from context_accesses where role_id in (12, 13, 14, 15, 16, 17, 18)`);
        await queryRunner.query(`
            Delete from operations where id in (19, 20, 21)`);
        await queryRunner.query(`
            INSERT INTO operations (id, slug, name, description)
            VALUES
                (19, 'list-procurement-plan-managers', '', ''),
                (20, 'list-specialists', '', ''),
                (21, 'assign-specialist', '', '')
              `);
        // removes the incorrect accesses first
        /**-------------------------------------
         * Procurement plan manager
         */
        await queryRunner.query(`
            insert into
              context_accesses (operation_id, is_enabled, role_id, context_id)
            select 1, 1, 12, id
            from contexts where slug in (
              'created-plans-count-widget',
              'confirmed-plans-count-widget',
              'published-plans-count-widget',
              'transferred-plans-count-widget',
              'implemented-plans-count-widget',
              'cancelled-plans-count-widget',
              'confirmed-plans-vs-created-plans-percentage-widget',
              'published-plans-vs-confirmed-plans-percentage-widget',
              'five-most-contingency-risks-predicted',
              'five-most-contingency-results-predicted',
              'five-most-frequent-cpv-divisions',
              'five-most-cpv-based-on-amount',
              'plan-vs-amount-project-based-on-province',
              'plan-vs-actual-projects-number-based-on-timeline-non-consultancy',
              'plan-vs-actual-projects-number-based-on-timeline-consultancy',
              'plan-vs-actual-project-number-and-amount-based-on-procurement-type',
              'plan-vs-actual-project-number-and-amount-based-on-contract-type',
              'plan-vs-actual-project-number-and-amount-based-on-budget-type',
              'plan-vs-actual-project-number-and-amount-based-on-procurement-preference',
              'plan-vs-actual-project-number-and-amount-based-on-selection-method',
              'plan-vs-actual-project-number-and-amount-based-on-procurement-method',
              'project-number-and-amount-based-on-award-authority-threshold',
              'plan-vs-actual-project-number-and-amount-based-on-plan-status',
              'project',
              'lot',
              'project-planning',
              'project-planning-procurement-plan-general-details',
              'project-planning-cpv',
              'project-planning-location-domestic',
              'project-planning-location-foreign',
              'project-planning-procurement-process-progress-timeline',
              'project-planning-potential-risk',
              'project-planning-document',
              'project-planning-challenge',
              'project-planning-analysis',
              'project-implementation',
              'project-implementation-procurement-plan-general-details',
              'project-implementation-cpv',
              'project-implementation-location-domestic',
              'project-implementation-location-foreign',
              'project-implementation-procurement-process-progress-timeline',
              'project-implementation-potential-risk',
              'project-implementation-document',
              'project-implementation-challenge',
              'project-implementation-analysis'
              )`);
        await queryRunner.query(`
            insert into
              context_accesses (operation_id, is_enabled, role_id, context_id)
            select 2, 1, 12, id
            from contexts where slug in (
              'project-planning-cpv',
              'project-planning-location-domestic',
              'project-planning-location-foreign',
              'project-planning-potential-risk',
              'project-planning-challenge',
              'project-implementation-cpv',
              'project-implementation-location-domestic',
              'project-implementation-location-foreign',
              'project-implementation-potential-risk',
              'project-implementation-challenge',
              'lot'
              )`);
        await queryRunner.query(`
            insert into
              context_accesses (operation_id, is_enabled, role_id, context_id)
            select 3, 1, 12, id
            from contexts where slug in (
              'project',
              'lot',
              'project-planning-cpv',
              'project-planning-location-domestic',
              'project-planning-location-foreign',
              'project-planning-potential-risk',
              'project-planning-challenge',
              'project-implementation-cpv',
              'project-implementation-location-domestic',
              'project-implementation-location-foreign',
              'project-implementation-potential-risk',
              'project-implementation-challenge'
              )`);
        await queryRunner.query(`
            insert into
              context_accesses (operation_id, is_enabled, role_id, context_id)
            select 4, 1, 12, id
            from contexts where slug in (
              'project-planning',
              'project-planning-procurement-plan-general-details',
              'project-planning-cpv',
              'project-planning-location-domestic',
              'project-planning-location-foreign',
              'project-planning-procurement-process-progress-timeline',
              'project-planning-potential-risk',
              'project-planning-document',
              'project-planning-challenge',
              'project-planning-analysis',
              'project-implementation',
              'project-implementation-procurement-plan-general-details',
              'project-implementation-cpv',
              'project-implementation-location-domestic',
              'project-implementation-location-foreign',
              'project-implementation-procurement-process-progress-timeline',
              'project-implementation-potential-risk',
              'project-implementation-document',
              'project-implementation-challenge',
              'project-implementation-analysis'
              )`);
        await queryRunner.query(`
            insert into
              context_accesses (operation_id, is_enabled, role_id, context_id)
            select 6, 1, 12, id
            from contexts where slug in (
              'project-planning-procurement-plan-general-details',
              'project-planning-cpv',
              'project-planning-location-domestic',
              'project-planning-location-foreign',
              'project-planning-procurement-process-progress-timeline',
              'project-planning-potential-risk',
              'project-planning-document',
              'project-planning-challenge',
              'project-implementation-procurement-plan-general-details',
              'project-implementation-cpv',
              'project-implementation-location-domestic',
              'project-implementation-location-foreign',
              'project-implementation-procurement-process-progress-timeline',
              'project-implementation-potential-risk',
              'project-implementation-document',
              'project-implementation-challenge'
              )`);
        await queryRunner.query(`
            insert into
              context_accesses (operation_id, is_enabled, role_id, context_id)
            select 9, 1, 12, id
            from contexts where slug in (
              'project',
              'lot',
              'project-planning',
              'project-implementation'
              )`);
        await queryRunner.query(`
            insert into
              context_accesses (operation_id, is_enabled, role_id, context_id)
            select 11, 1, 12, id
            from contexts where slug in (
              'project',
              'lot',
              'project-planning-procurement-plan-general-details',
              'project-planning-cpv',
              'project-planning-location-domestic',
              'project-planning-location-foreign',
              'project-planning-procurement-process-progress-timeline',
              'project-planning-potential-risk',
              'project-planning-document',
              'project-planning-challenge',
              'project-implementation-procurement-plan-general-details',
              'project-implementation-cpv',
              'project-implementation-location-domestic',
              'project-implementation-location-foreign',
              'project-implementation-procurement-process-progress-timeline',
              'project-implementation-potential-risk',
              'project-implementation-document',
              'project-implementation-challenge'
              )`);
        await queryRunner.query(`
            insert into
              context_accesses (operation_id, is_enabled, role_id, context_id)
            select 12, 1, 12, id
            from contexts where slug in (
              'lot',
              'project-planning-procurement-plan-general-details',
              'project-planning-cpv',
              'project-planning-location-domestic',
              'project-planning-location-foreign',
              'project-planning-procurement-process-progress-timeline',
              'project-planning-potential-risk',
              'project-planning-document',
              'project-planning-challenge',
              'project-implementation-procurement-plan-general-details',
              'project-implementation-cpv',
              'project-implementation-location-domestic',
              'project-implementation-location-foreign',
              'project-implementation-procurement-process-progress-timeline',
              'project-implementation-potential-risk',
              'project-implementation-document',
              'project-implementation-challenge'
              )`);
        await queryRunner.query(`
            insert into
            context_accesses (operation_id, is_enabled, role_id, context_id)
            values (13, 1, 12, (select id from contexts where slug = "project")),
            (15, 1, 12, (select id from contexts where slug = "project")),
            (17, 1, 12, (select id from contexts where slug = "project"))
            `);
        await queryRunner.query(`
            insert into
            context_accesses (operation_id, is_enabled, role_id, context_id)
            values (13, 1, 12, (select id from contexts where slug = "lot")),
            (15, 1, 12, (select id from contexts where slug = "lot")),
            (17, 1, 12, (select id from contexts where slug = "lot"))
            `);
        /**-------------------------------------
         * Award authority
         */
        await queryRunner.query(`
            insert into
              context_accesses (operation_id, is_enabled, role_id, context_id)
            select 1, 1, 13, id
            from contexts where slug in (
              'created-plans-count-widget',
              'confirmed-plans-count-widget',
              'published-plans-count-widget',
              'transferred-plans-count-widget',
              'implemented-plans-count-widget',
              'cancelled-plans-count-widget',
              'confirmed-plans-vs-created-plans-percentage-widget',
              'published-plans-vs-confirmed-plans-percentage-widget',
              'five-most-contingency-risks-predicted',
              'five-most-contingency-results-predicted',
              'five-most-frequent-cpv-divisions',
              'five-most-cpv-based-on-amount',
              'plan-vs-amount-project-based-on-province',
              'plan-vs-actual-projects-number-based-on-timeline-non-consultancy',
              'plan-vs-actual-projects-number-based-on-timeline-consultancy',
              'plan-vs-actual-project-number-and-amount-based-on-procurement-type',
              'plan-vs-actual-project-number-and-amount-based-on-contract-type',
              'plan-vs-actual-project-number-and-amount-based-on-budget-type',
              'plan-vs-actual-project-number-and-amount-based-on-procurement-preference',
              'plan-vs-actual-project-number-and-amount-based-on-selection-method',
              'plan-vs-actual-project-number-and-amount-based-on-procurement-method',
              'project-number-and-amount-based-on-award-authority-threshold',
              'plan-vs-actual-project-number-and-amount-based-on-plan-status',
              'project',
              'lot',
              'project-planning',
              'project-planning-procurement-plan-general-details',
              'project-planning-cpv',
              'project-planning-location-domestic',
              'project-planning-location-foreign',
              'project-planning-procurement-process-progress-timeline',
              'project-planning-potential-risk',
              'project-planning-document',
              'project-planning-challenge',
              'project-planning-analysis',
              'project-implementation',
              'project-implementation-procurement-plan-general-details',
              'project-implementation-cpv',
              'project-implementation-location-domestic',
              'project-implementation-location-foreign',
              'project-implementation-procurement-process-progress-timeline',
              'project-implementation-potential-risk',
              'project-implementation-document',
              'project-implementation-challenge',
              'project-implementation-analysis'
              )`);
        await queryRunner.query(`
            insert into
              context_accesses (operation_id, is_enabled, role_id, context_id)
            select 3, 1, 13, id
            from contexts where slug in (
              'project',
              'lot',
              'project-planning-cpv',
              'project-planning-location-domestic',
              'project-planning-location-foreign',
              'project-planning-potential-risk',
              'project-planning-challenge',
              'project-implementation-cpv',
              'project-implementation-location-domestic',
              'project-implementation-location-foreign',
              'project-implementation-potential-risk',
              'project-implementation-challenge'
              )`);
        await queryRunner.query(`
            insert into
              context_accesses (operation_id, is_enabled, role_id, context_id)
            select 4, 1, 13, id
            from contexts where slug in (
              'project-planning',
              'project-planning-procurement-plan-general-details',
              'project-planning-cpv',
              'project-planning-location-domestic',
              'project-planning-location-foreign',
              'project-planning-procurement-process-progress-timeline',
              'project-planning-potential-risk',
              'project-planning-document',
              'project-planning-challenge',
              'project-planning-analysis',
              'project-implementation',
              'project-implementation-procurement-plan-general-details',
              'project-implementation-cpv',
              'project-implementation-location-domestic',
              'project-implementation-location-foreign',
              'project-implementation-procurement-process-progress-timeline',
              'project-implementation-potential-risk',
              'project-implementation-document',
              'project-implementation-challenge',
              'project-implementation-analysis'
              )`);
        await queryRunner.query(`
            insert into
              context_accesses (operation_id, is_enabled, role_id, context_id)
            select 5, 1, 13, id
            from contexts where slug in (
              'project-planning-procurement-plan-general-details',
              'project-planning-cpv',
              'project-planning-location-domestic',
              'project-planning-location-foreign',
              'project-planning-procurement-process-progress-timeline',
              'project-planning-potential-risk',
              'project-planning-document',
              'project-planning-challenge',
              'project-implementation-procurement-plan-general-details',
              'project-implementation-cpv',
              'project-implementation-location-domestic',
              'project-implementation-location-foreign',
              'project-implementation-procurement-process-progress-timeline',
              'project-implementation-potential-risk',
              'project-implementation-document',
              'project-implementation-challenge'
              )`);
        await queryRunner.query(`
            insert into
              context_accesses (operation_id, is_enabled, role_id, context_id)
            select 8, 1, 13, id
            from contexts where slug in (
              'project',
              'lot',
              'project-planning',
              'project-implementation'
              )`);
        await queryRunner.query(`
            insert into
            context_accesses (operation_id, is_enabled, role_id, context_id)
            values (14, 1, 13, (select id from contexts where slug = "project")),
            (16, 1, 13, (select id from contexts where slug = "project")),
            (18, 1, 13, (select id from contexts where slug = "project"))
            `);
        await queryRunner.query(`
            insert into
            context_accesses (operation_id, is_enabled, role_id, context_id)
            values (14, 1, 13, (select id from contexts where slug = "lot")),
            (16, 1, 13, (select id from contexts where slug = "lot")),
            (18, 1, 13, (select id from contexts where slug = "lot"))
            `);
        await queryRunner.query(`
            insert into
            context_accesses (operation_id, is_enabled, role_id, context_id)
            values (19, 1, 13, (select id from contexts where slug = "project"))
            `);
        /**-------------------------------------
         * Specialist
         */
        await queryRunner.query(`
            insert into
              context_accesses (operation_id, is_enabled, role_id, context_id)
            select 1, 1, 14, id
            from contexts where slug in (
              'published-plans-count-widget',
              'implemented-plans-count-widget',
              'cancelled-plans-count-widget',
              'assigned-plans-count-widget',
              'planning-analyzed-plans-count-widget',
              'confirmed-analyzed-plans-count-widget',
              'implementation-analyzed-plans-count-widget',
              'analyzed-plans-vs-assigned-plans-percentage-widget',
              'five-most-contingency-risks-predicted',
              'five-most-contingency-results-predicted',
              'five-most-frequent-cpv-divisions',
              'five-most-cpv-based-on-amount',
              'plan-vs-amount-project-based-on-province',
              'plan-vs-actual-projects-number-based-on-timeline-non-consultancy',
              'plan-vs-actual-projects-number-based-on-timeline-consultancy',
              'plan-vs-actual-project-number-and-amount-based-on-procurement-type',
              'plan-vs-actual-project-number-and-amount-based-on-contract-type',
              'plan-vs-actual-project-number-and-amount-based-on-budget-type',
              'plan-vs-actual-project-number-and-amount-based-on-procurement-preference',
              'plan-vs-actual-project-number-and-amount-based-on-selection-method',
              'plan-vs-actual-project-number-and-amount-based-on-procurement-method',
              'project-number-and-amount-based-on-award-authority-threshold',
              'plan-vs-actual-project-number-and-amount-based-on-plan-status',
              'plan-vs-actual-project-number-based-on-sector',
              'plan-vs-actual-project-amount-based-on-sector',
              'project',
              'lot',
              'project-planning',
              'project-planning-procurement-plan-general-details',
              'project-planning-cpv',
              'project-planning-location-domestic',
              'project-planning-location-foreign',
              'project-planning-procurement-process-progress-timeline',
              'project-planning-potential-risk',
              'project-planning-document',
              'project-planning-challenge',
              'project-planning-analysis',
              'project-implementation',
              'project-implementation-procurement-plan-general-details',
              'project-implementation-cpv',
              'project-implementation-location-domestic',
              'project-implementation-location-foreign',
              'project-implementation-procurement-process-progress-timeline',
              'project-implementation-potential-risk',
              'project-implementation-document',
              'project-implementation-challenge',
              'project-implementation-analysis'
              )`);
        await queryRunner.query(`
            insert into
              context_accesses (operation_id, is_enabled, role_id, context_id)
            select 3, 1, 14, id
            from contexts where slug in (
              'project',
              'lot',
              'project-planning-cpv',
              'project-planning-location-domestic',
              'project-planning-location-foreign',
              'project-planning-potential-risk',
              'project-planning-challenge',
              'project-implementation-cpv',
              'project-implementation-location-domestic',
              'project-implementation-location-foreign',
              'project-implementation-potential-risk',
              'project-implementation-challenge'
              )`);
        await queryRunner.query(`
            insert into
              context_accesses (operation_id, is_enabled, role_id, context_id)
            select 4, 1, 14, id
            from contexts where slug in (
              'project-planning',
              'project-planning-procurement-plan-general-details',
              'project-planning-cpv',
              'project-planning-location-domestic',
              'project-planning-location-foreign',
              'project-planning-procurement-process-progress-timeline',
              'project-planning-potential-risk',
              'project-planning-document',
              'project-planning-challenge',
              'project-planning-analysis',
              'project-implementation',
              'project-implementation-procurement-plan-general-details',
              'project-implementation-cpv',
              'project-implementation-location-domestic',
              'project-implementation-location-foreign',
              'project-implementation-procurement-process-progress-timeline',
              'project-implementation-potential-risk',
              'project-implementation-document',
              'project-implementation-challenge',
              'project-implementation-analysis'
              )`);
        await queryRunner.query(`
            insert into
              context_accesses (operation_id, is_enabled, role_id, context_id)
            select 6, 1, 14, id
            from contexts where slug in (
              'project-implementation-analysis'
              )`);

        await queryRunner.query(`
            insert into
            context_accesses (operation_id, is_enabled, role_id, context_id)
            values (19, 1, 14, (select id from contexts where slug = "project"))
            `);
        /**-------------------------------------
         * Procurement policy manager
         */
        await queryRunner.query(`
            insert into
              context_accesses (operation_id, is_enabled, role_id, context_id)
            select 1, 1, 15, id
            from contexts where slug in (
              'created-plans-count-widget',
              'published-plans-count-widget',
              'implemented-plans-count-widget',
              'cancelled-plans-count-widget',
              'transferred-plans-count-widget',
              'planning-analyzed-plans-count-widget',
              'implementation-analyzed-plans-count-widget',
              'five-most-contingency-risks-predicted',
              'five-most-contingency-results-predicted',
              'five-most-frequent-cpv-divisions',
              'five-most-cpv-based-on-amount',
              'plan-vs-amount-project-based-on-province',
              'plan-vs-actual-projects-number-based-on-timeline-non-consultancy',
              'plan-vs-actual-projects-number-based-on-timeline-consultancy',
              'plan-vs-actual-project-number-and-amount-based-on-procurement-type',
              'plan-vs-actual-project-number-and-amount-based-on-contract-type',
              'plan-vs-actual-project-number-and-amount-based-on-budget-type',
              'plan-vs-actual-project-number-and-amount-based-on-procurement-preference',
              'plan-vs-actual-project-number-and-amount-based-on-selection-method',
              'plan-vs-actual-project-number-and-amount-based-on-procurement-method',
              'project-number-and-amount-based-on-award-authority-threshold',
              'plan-vs-actual-project-number-and-amount-based-on-plan-status',
              'plan-vs-actual-project-number-based-on-sector',
              'plan-vs-actual-project-amount-based-on-sector',
              'project',
              'lot',
              'project-planning',
              'project-planning-procurement-plan-general-details',
              'project-planning-cpv',
              'project-planning-location-domestic',
              'project-planning-location-foreign',
              'project-planning-procurement-process-progress-timeline',
              'project-planning-potential-risk',
              'project-planning-document',
              'project-planning-challenge',
              'project-planning-analysis',
              'project-implementation',
              'project-implementation-procurement-plan-general-details',
              'project-implementation-cpv',
              'project-implementation-location-domestic',
              'project-implementation-location-foreign',
              'project-implementation-procurement-process-progress-timeline',
              'project-implementation-potential-risk',
              'project-implementation-document',
              'project-implementation-challenge',
              'project-implementation-analysis'
              )`);
        await queryRunner.query(`
            insert into
              context_accesses (operation_id, is_enabled, role_id, context_id)
            select 3, 1, 15, id
            from contexts where slug in (
              'project',
              'lot',
              'project-planning-cpv',
              'project-planning-location-domestic',
              'project-planning-location-foreign',
              'project-planning-potential-risk',
              'project-planning-challenge',
              'project-implementation-cpv',
              'project-implementation-location-domestic',
              'project-implementation-location-foreign',
              'project-implementation-potential-risk',
              'project-implementation-challenge '
              )`);
        await queryRunner.query(`
            insert into
              context_accesses (operation_id, is_enabled, role_id, context_id)
            select 4, 1, 15, id
            from contexts where slug in (
              'project-planning',
              'project-planning-procurement-plan-general-details',
              'project-planning-cpv',
              'project-planning-location-domestic',
              'project-planning-location-foreign',
              'project-planning-procurement-process-progress-timeline',
              'project-planning-potential-risk',
              'project-planning-document',
              'project-planning-challenge',
              'project-planning-analysis',
              'project-implementation',
              'project-implementation-procurement-plan-general-details',
              'project-implementation-cpv',
              'project-implementation-location-domestic',
              'project-implementation-location-foreign',
              'project-implementation-procurement-process-progress-timeline',
              'project-implementation-potential-risk',
              'project-implementation-document',
              'project-implementation-challenge',
              'project-implementation-analysis'
              )`);
        await queryRunner.query(`
            insert into
              context_accesses (operation_id, is_enabled, role_id, context_id)
            select 5, 1, 15, id
            from contexts where slug in (
              'project-implementation-analysis'
              )`);

        await queryRunner.query(`
            insert into
            context_accesses (operation_id, is_enabled, role_id, context_id)
            values (19, 1, 15, (select id from contexts where slug = "project"))
            `);

        await queryRunner.query(`
            insert into
            context_accesses (operation_id, is_enabled, role_id, context_id)
            values (20, 1, 15, (select id from contexts where slug = "project"))
            `);
        /**-------------------------------------
         * Procurement policy director
         */
        await queryRunner.query(`
            insert into
              context_accesses (operation_id, is_enabled, role_id, context_id)
            select 1, 1, 16, id
            from contexts where slug in (
              'created-plans-count-widget',
              'confirmed-plans-count-widget',
              'published-plans-count-widget',
              'implemented-plans-count-widget',
              'cancelled-plans-count-widget',
              'transferred-plans-count-widget',
              'confirmed-plans-vs-created-plans-percentage-widget',
              'published-plans-vs-confirmed-plans-percentage-widget',
              'five-most-contingency-risks-predicted',
              'five-most-contingency-results-predicted',
              'five-most-frequent-cpv-divisions',
              'five-most-cpv-based-on-amount',
              'plan-vs-amount-project-based-on-province',
              'plan-vs-actual-projects-number-based-on-timeline-non-consultancy',
              'plan-vs-actual-projects-number-based-on-timeline-consultancy',
              'plan-vs-actual-project-number-and-amount-based-on-procurement-type',
              'plan-vs-actual-project-number-and-amount-based-on-contract-type',
              'plan-vs-actual-project-number-and-amount-based-on-budget-type',
              'plan-vs-actual-project-number-and-amount-based-on-procurement-preference',
              'plan-vs-actual-project-number-and-amount-based-on-selection-method',
              'plan-vs-actual-project-number-and-amount-based-on-procurement-method',
              'project-number-and-amount-based-on-award-authority-threshold',
              'plan-vs-actual-project-number-and-amount-based-on-plan-status',
              'plan-vs-actual-project-number-based-on-sector',
              'plan-vs-actual-project-amount-based-on-sector',
              'project',
              'lot',
              'project-planning',
              'project-planning-procurement-plan-general-details',
              'project-planning-cpv',
              'project-planning-location-domestic',
              'project-planning-location-foreign',
              'project-planning-procurement-process-progress-timeline',
              'project-planning-potential-risk',
              'project-planning-document',
              'project-planning-challenge',
              'project-planning-analysis',
              'project-implementation',
              'project-implementation-procurement-plan-general-details',
              'project-implementation-cpv',
              'project-implementation-location-domestic',
              'project-implementation-location-foreign',
              'project-implementation-procurement-process-progress-timeline',
              'project-implementation-potential-risk',
              'project-implementation-document',
              'project-implementation-challenge',
              'project-implementation-analysis'
              )`);
        await queryRunner.query(`
            insert into
              context_accesses (operation_id, is_enabled, role_id, context_id)
            select 3, 1, 16, id
            from contexts where slug in (
              'project',
              'lot',
              'project-planning-cpv',
              'project-planning-location-domestic',
              'project-planning-location-foreign',
              'project-planning-potential-risk',
              'project-planning-challenge',
              'project-implementation-cpv',
              'project-implementation-location-domestic',
              'project-implementation-location-foreign',
              'project-implementation-potential-risk',
              'project-implementation-challenge'
              )`);
        await queryRunner.query(`
            insert into
              context_accesses (operation_id, is_enabled, role_id, context_id)
            select 4, 1, 16, id
            from contexts where slug in (
              'project-planning',
              'project-planning-procurement-plan-general-details',
              'project-planning-cpv',
              'project-planning-location-domestic',
              'project-planning-location-foreign',
              'project-planning-procurement-process-progress-timeline',
              'project-planning-potential-risk',
              'project-planning-document',
              'project-planning-challenge',
              'project-planning-analysis',
              'project-implementation',
              'project-implementation-procurement-plan-general-details',
              'project-implementation-cpv',
              'project-implementation-location-domestic',
              'project-implementation-location-foreign',
              'project-implementation-procurement-process-progress-timeline',
              'project-implementation-potential-risk',
              'project-implementation-document',
              'project-implementation-challenge',
              'project-implementation-analysis'
              )`);

        await queryRunner.query(`
            insert into
            context_accesses (operation_id, is_enabled, role_id, context_id)
            values (19, 1, 16, (select id from contexts where slug = "project"))
            `);
        /**-------------------------------------
         * CPMD Director
         */
        await queryRunner.query(`
            insert into
              context_accesses (operation_id, is_enabled, role_id, context_id)
            select 1, 1, 17, id
            from contexts where slug in (
              'created-plans-count-widget',
              'confirmed-plans-count-widget',
              'published-plans-count-widget',
              'implemented-plans-count-widget',
              'cancelled-plans-count-widget',
              'transferred-plans-count-widget',
              'confirmed-plans-vs-created-plans-percentage-widget',
              'published-plans-vs-confirmed-plans-percentage-widget',
              'five-most-contingency-risks-predicted',
              'five-most-contingency-results-predicted',
              'five-most-frequent-cpv-divisions',
              'five-most-cpv-based-on-amount',
              'plan-vs-amount-project-based-on-province',
              'plan-vs-actual-projects-number-based-on-timeline-non-consultancy',
              'plan-vs-actual-projects-number-based-on-timeline-consultancy',
              'plan-vs-actual-project-number-and-amount-based-on-procurement-type',
              'plan-vs-actual-project-number-and-amount-based-on-contract-type',
              'plan-vs-actual-project-number-and-amount-based-on-budget-type',
              'plan-vs-actual-project-number-and-amount-based-on-procurement-preference',
              'plan-vs-actual-project-number-and-amount-based-on-selection-method',
              'plan-vs-actual-project-number-and-amount-based-on-procurement-method',
              'project-number-and-amount-based-on-award-authority-threshold',
              'plan-vs-actual-project-number-and-amount-based-on-plan-status',
              'plan-vs-actual-project-number-based-on-sector',
              'plan-vs-actual-project-amount-based-on-sector',
              'project',
              'lot',
              'project-planning',
              'project-planning-procurement-plan-general-details',
              'project-planning-cpv',
              'project-planning-location-domestic',
              'project-planning-location-foreign',
              'project-planning-procurement-process-progress-timeline',
              'project-planning-potential-risk',
              'project-planning-document',
              'project-planning-challenge',
              'project-planning-analysis',
              'project-implementation',
              'project-implementation-procurement-plan-general-details',
              'project-implementation-cpv',
              'project-implementation-location-domestic',
              'project-implementation-location-foreign',
              'project-implementation-procurement-process-progress-timeline',
              'project-implementation-potential-risk',
              'project-implementation-document',
              'project-implementation-challenge',
              'project-implementation-analysis'
              )`);
        await queryRunner.query(`
            insert into
              context_accesses (operation_id, is_enabled, role_id, context_id)
            select 3, 1, 17, id
            from contexts where slug in (
              'project',
              'lot',
              'project-planning-cpv',
              'project-planning-location-domestic',
              'project-planning-location-foreign',
              'project-planning-potential-risk',
              'project-planning-challenge',
              'project-implementation-cpv',
              'project-implementation-location-domestic',
              'project-implementation-location-foreign',
              'project-implementation-potential-risk',
              'project-implementation-challenge'
              )`);

        await queryRunner.query(`
            insert into
            context_accesses (operation_id, is_enabled, role_id, context_id)
            values (19, 1, 17, (select id from contexts where slug = "project"))
            `);

        await queryRunner.query(`
            insert into
            context_accesses (operation_id, is_enabled, role_id, context_id)
            values (21, 1, 17, (select id from contexts where slug = "project"))
            `);
        /**-------------------------------------
         * SD Directorate
         */
        await queryRunner.query(`
            insert into
              context_accesses (operation_id, is_enabled, role_id, context_id)
            select 1, 1, 18, id
            from contexts where slug in (
              'created-plans-count-widget',
              'confirmed-plans-count-widget',
              'published-plans-count-widget',
              'implemented-plans-count-widget',
              'cancelled-plans-count-widget',
              'transferred-plans-count-widget',
              'confirmed-plans-vs-created-plans-percentage-widget',
              'published-plans-vs-confirmed-plans-percentage-widget',
              'five-most-contingency-risks-predicted',
              'five-most-contingency-results-predicted',
              'five-most-frequent-cpv-divisions',
              'five-most-cpv-based-on-amount',
              'plan-vs-amount-project-based-on-province',
              'plan-vs-actual-projects-number-based-on-timeline-non-consultancy',
              'plan-vs-actual-projects-number-based-on-timeline-consultancy',
              'plan-vs-actual-project-number-and-amount-based-on-procurement-type',
              'plan-vs-actual-project-number-and-amount-based-on-contract-type',
              'plan-vs-actual-project-number-and-amount-based-on-budget-type',
              'plan-vs-actual-project-number-and-amount-based-on-procurement-preference',
              'plan-vs-actual-project-number-and-amount-based-on-selection-method',
              'plan-vs-actual-project-number-and-amount-based-on-procurement-method',
              'project-number-and-amount-based-on-award-authority-threshold',
              'plan-vs-actual-project-number-and-amount-based-on-plan-status',
              'plan-vs-actual-project-number-based-on-sector',
              'plan-vs-actual-project-amount-based-on-sector',
              'project',
              'lot',
              'project-planning',
              'project-planning-procurement-plan-general-details',
              'project-planning-cpv',
              'project-planning-location-domestic',
              'project-planning-location-foreign',
              'project-planning-procurement-process-progress-timeline',
              'project-planning-potential-risk',
              'project-planning-document',
              'project-planning-challenge',
              'project-planning-analysis',
              'project-implementation',
              'project-implementation-procurement-plan-general-details',
              'project-implementation-cpv',
              'project-implementation-location-domestic',
              'project-implementation-location-foreign',
              'project-implementation-procurement-process-progress-timeline',
              'project-implementation-potential-risk',
              'project-implementation-document',
              'project-implementation-challenge',
              'project-implementation-analysis'
              )`);
        await queryRunner.query(`
            insert into
              context_accesses (operation_id, is_enabled, role_id, context_id)
            select 3, 1, 18, id
            from contexts where slug in (
              'project',
              'lot',
              'project-planning-cpv',
              'project-planning-location-domestic',
              'project-planning-location-foreign',
              'project-planning-potential-risk',
              'project-planning-challenge',
              'project-implementation-cpv',
              'project-implementation-location-domestic',
              'project-implementation-location-foreign',
              'project-implementation-potential-risk',
              'project-implementation-challenge'
              )`);

        await queryRunner.query(`
            insert into
            context_accesses (operation_id, is_enabled, role_id, context_id)
            values (19, 1, 18, (select id from contexts where slug = "project"))
            `);
    }

    public async down(queryRunner: QueryRunner): Promise<any> {
    }

}
