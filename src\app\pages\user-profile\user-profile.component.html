<mat-card>
    <mat-card-header>
        <p>{{'USER_PROFILE_INFO'|translate}}</p>
    </mat-card-header>
    <hr>
    <mat-card-content fxLayout="row wrap" fxFlexAlign="start">
        <npa-view-element fxFlex="33">
            <div class="npa-label">{{'FIRST_NAME'|translate}}:</div>
            <div class="npa-value">{{data?.name}}</div>
        </npa-view-element>
        <npa-view-element fxFlex="33">
            <div class="npa-label">{{'LAST_NAME'|translate}}:</div>
            <div class="npa-value">{{data?.last_name}}</div>
        </npa-view-element>
        <npa-view-element fxFlex="33">
            <div class="npa-label">{{'PHONE_NUMBER'|translate}}:</div>
            <div class="npa-value">{{data?.phone_number}}</div>
        </npa-view-element>
        <npa-view-element fxFlex="33">
            <div class="npa-label">{{'USERNAME'|translate}}:</div>
            <div class="npa-value">{{data?.username}}</div>
        </npa-view-element>
        <npa-view-element fxFlex="33">
            <div class="npa-label">{{'EMAIL_ADDRESS'|translate}}:</div>
            <div class="npa-value">{{data?.email}}</div>
        </npa-view-element>
    </mat-card-content>
    <mat-card-actions fxLayout="row" fxLayoutAlign="end">
        <button mat-button type="button" (click)="openPasswordResetDialog()"
                [disabled]="notificationsService.isLoading">
            {{'RESET_PASSWORD'|translate}}
        </button>
        <button mat-button type="button" (click)="openUserProfileDialog()" [disabled]="notificationsService.isLoading">
            {{'UPDATE_PROFILE'|translate}}
        </button>
    </mat-card-actions>
</mat-card>

