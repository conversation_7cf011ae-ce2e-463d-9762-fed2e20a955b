import {<PERSON><PERSON><PERSON><PERSON>, <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne, OneToMany, PrimaryGeneratedColumn} from 'typeorm';
import {User} from './user';
import {Role} from './role';
import {IUserRoleSector, UserRoleSector} from './user-role-sector';
import {IUserRolePe, UserRolePe} from './user-role-pe';
import {IUserRoleRecord, UserRoleRecord} from './user-role-record';

@Entity('user_roles')
export class UserRole extends BaseEntity implements IUserRole {

    @PrimaryGeneratedColumn()
    id: number;

    @Column()
    user_id: number;

    @Column()
    role_id: number;

    @ManyToOne(type => User, user => user.id, {onDelete: 'CASCADE'})
    @JoinColumn({name: 'user_id'})
    user: User;

    @ManyToOne(type => Role, role => role.id, {onDelete: 'CASCADE'})
    @JoinColumn({name: 'role_id'})
    role: Role;

    @OneToMany(type => UserRoleSector, userRoleSector => userRoleSector.userRole)
    userRoleSector: UserRoleSector[];
    @OneToMany(type => UserRolePe, userRolePe => userRolePe.userRole)
    userRolePe: UserRolePe[];
    @OneToMany(type => UserRoleRecord, userRoleRecord => userRoleRecord.userRole)
    userRoleRecord: UserRoleRecord[];
}

export interface IUserRole {
    id?: number;
    user_id: number;
    role_id: number;
    user?: User;
    role?: Role;
    userRoleSector?: IUserRoleSector[];
    userRolePe?: IUserRolePe[];
    userRoleRecord?: IUserRoleRecord[];
}



