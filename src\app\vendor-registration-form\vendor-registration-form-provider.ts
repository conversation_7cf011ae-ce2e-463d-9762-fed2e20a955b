import {Injectable} from '@angular/core';
import {Observable} from 'rxjs';
import {HttpService} from '../services/http.service';

@Injectable()

export class VendorRegistrationFormProvider {
    PostUrl = 'api/users/create-vendor';
    url = '';

    constructor(private _http: HttpService) {
    }

    store(data): Observable<any> {
        return this._http._post(this.PostUrl, data, {observe: 'response'});
    }

    show(url): Observable<any> {
        return this._http.get(url);
    }

    update(data, id): Observable<any> {
        return this._http.put(`${this.url}/${id}`, data);
    }

    destroy(id): Observable<any> {
        return this._http.delete(`${this.url}/${id}`);
    }
}
