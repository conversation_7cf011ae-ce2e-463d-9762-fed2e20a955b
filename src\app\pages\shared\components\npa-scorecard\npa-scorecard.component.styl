@import "./../../../../../styles/definitions.styl"
.scorecard
  .card
    overflow hidden
    height 230px
    transition transform .5s ease-out 0s, visibility 0s ease-in .2s, opacity 0s ease-in .2s, -webkit-transform .5s ease-out 0s
    .header
      justify-content space-between
      .title
        font-size 16px
        align-items center
        width 80%
        overflow hidden
        white-space nowrap
        text-overflow ellipsis
        //font-weight  bold !important
      .close
      .flip
        right 15px
        bottom 14px
      .close
        mat-icon
          font-size 20px
    .footer
    //color colorTextLighter
    //background-color #fafafa
      color colorSidebarText
      background colorBanner url("../../../../../assets/images/banner.png") repeat top
      display flex
      width 100%
      position absolute
      height 55px
      right 0
      bottom 0px
      border-top 1px solid rgba(0,0,0,.12)
      align-items center
      justify-content left
      padding-right 27px

  .rotate
    transform rotateX(180deg)
    -webkit-transform rotateY(180deg)
    .contents
      margin-bottom 30px
      overflow hidden
    .back
      transform rotateX(-180deg)
      -webkit-transform rotateY(-180deg)
    .back-side-content
      width 100%
      position relative
      top -20px
      text-align justify
      font-size 12px
      margin-bottom 20px
  .contents
    align-items  center
    justify-content center
    bottom 15px
    position relative
    .info
      position relative
      font-size 16px
      color colorTextLighter
      bottom 15px
    .value
      font-size 60px
      opacity 0.9
      overflow hidden
      text-overflow ellipsis
      color #607d8b
      position relative
      top 10px
      text-align center
      label
        width 50%
    div
      text-align center
  .bottom
    bottom -15px

.value-container
  height 140px !important
  display flex
  align-items center