import {Injectable} from '@angular/core';
import {IComplaintList, ITableHeader} from './dashboard-system-list.types';
import {BehaviorSubject, Observable} from 'rxjs';
import {HttpService} from '../../../services/http.service';
import {AuthService} from '../../../services/auth.service';

@Injectable()

export class DashboardSystemListService {
    private _slugLabelMap: any = {};
    private _slugClassMap: any = {};
    private _slugWidthMultiplicationFactorMap: any = {};
    data = new BehaviorSubject<IComplaintList>(undefined);

    constructor(private _httpService: HttpService,
                public authService: AuthService) {
    }

    index(): Observable<any> {
        return this._httpService.get(`api/systems/list`, {
            observe: 'response'
        });
    }

    store(data: IComplaintList): Observable<IComplaintList> {
        // data.user_identification_number = 'AA-BB-CC' + this.index++;
        this.data.next(data);
        return this.data;
    }

    show(): Observable<IComplaintList> {
        return this.data;

    }

    update(data: IComplaintList): Observable<IComplaintList> {
        this.data.next(data);
        return this.data;
    }

    getClass(slug: string) {
        if (!this._slugClassMap.init_marker) {
            CONTRACT_LIST_TITLES.forEach((current: ITableHeader) => {
                this._slugClassMap[current.slug] = `widthUnit${current.width_multiplication_factor}`;
            });
        }
        return this._slugClassMap[slug];
    }

    getLabel(slug: string) {
        if (!this._slugLabelMap.init_marker) {
            CONTRACT_LIST_TITLES.forEach((current: ITableHeader) => {
                this._slugLabelMap[current.slug] = current.label_da;
            });
        }
        return this._slugLabelMap[slug];
    }

    getWidthMultiplicationFactor(slug: string) {
        if (!this._slugWidthMultiplicationFactorMap.init_marker) {
            CONTRACT_LIST_TITLES.forEach((current: ITableHeader) => {
                this._slugWidthMultiplicationFactorMap[current.slug] = current.width_multiplication_factor;
            });
        }
        return this._slugWidthMultiplicationFactorMap[slug];
    }

    getDisplayedColumns(userRole: string, loggedInUserRole: string) {
        return [
            'options',
            'system_icon',
            'system_slug',
            'system_url'
        ];
    }
}

export const CONTRACT_LIST_TITLES: ITableHeader[] = [
    {
        slug: 'init_marker',
        label_da: 'init_marker',
        width_multiplication_factor: 0
    },
    {
        slug: 'project_name',
        label_da: 'نام کاربر',
        width_multiplication_factor: 1
    },
    {
        slug: 'user_identification_number',
        label_da: 'نمبر تشخیصیه کاربر',
        width_multiplication_factor: 1
    },
    {
        slug: 'options',
        label_da: 'اختیارات',
        width_multiplication_factor: 1
    }
];
