import {Request, Response} from 'express';
import {InstanceService} from '../services/instance.service';
import {Model} from '../config/database';
import {IController} from '../types/controller';
import {ISystem, System} from '../entity/system';
import {IUserRole, UserRole} from '../entity/user-role';
import {createQueryBuilder} from 'typeorm';


export class SystemController implements IController {
  public static instance: SystemController;

  public static getInstance(): SystemController {
    return InstanceService.getInstance(SystemController);

  }

  public index(req: Request, res: Response) {
    Model(System).find().then(data => {
      data.length !== 0 ?
        res.status(200).json(data) :
        res.status(404).json(data);
    }).catch(error => {
      res.status(500).send(error);
    });
  }


  public async userRoleView(req: Request, res: Response) {
    try {
      const userRole = <IUserRole>await Model(UserRole).findOne({id: res.locals.user_role_id});
      const userSystems = await createQueryBuilder('UserRole')
          .select(['UserRole.id', 'role.id'])
        .innerJoin('UserRole.role', 'role')
        .innerJoinAndSelect('role.system', 'system')
        .where(`UserRole.user_id=:user_id`, {user_id: userRole.user_id})
        .groupBy('system.id')
        .getMany();
      let status: number;
      userSystems ?
        status = 200 :
        status = 404;
      return res.status(status).json(userSystems);
    } catch (e) {
      return res.status(500).json(e);
    }
  }


  public store(req: Request, res: Response) {
    const systemModel = Model(System);
    const data: ISystem = req.body;
    console.log(data);
    const system = systemModel.create(data);
    console.log(system);
    systemModel.save(system).then(() => {
      return res.status(201).json([]);
    }).catch(error => {
        return res.status(500).send(error);
      }
    );

  }

  public show(req: Request, res: Response) {
    Model(System).findOne(req.params.id).then(data => {
      data ?
        res.status(200).json(data) :
        res.status(404).json(data);
    }).catch(error => {
      res.status(500).send(error);
    });
  }

  public update(req: Request, res: Response) {
    Model(System).update({id: req.params.id}, req.body).then(result => {
      console.log(result);
      result.raw.affectedRows !== 0 ?
        res.status(204).json([]) :
        res.status(404).json(result);
    }).catch(error => {
        res.status(500).send(error);
      }
    );
  }

  public destroy(req: Request, res: Response) {
    Model(System).delete(req.params.id).then(result => {
      result.raw.affectedRows !== 0 ?
        res.status(204).json([]) :
        res.status(404).json(result);
    }).catch(error => {
      res.status(500).send(error);
    });
  }
}
