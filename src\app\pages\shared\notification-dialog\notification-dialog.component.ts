import {Component, Inject} from '@angular/core';
import {MAT_DIALOG_DATA} from '@angular/material';
import {TranslateService} from '../services/translate.service';

@Component({
    selector: 'npa-app-notification-dialog',
    templateUrl: './notification-dialog.component.html',
    styleUrls: ['./notification-dialog.component.styl']
})
export class NotificationDialogComponent {

    constructor(@Inject(MAT_DIALOG_DATA) public data: any,  public translate: TranslateService) {
    }

}
