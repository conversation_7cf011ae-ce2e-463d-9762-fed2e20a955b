import {Session<PERSON>ontroller} from './session.controller';
import {<PERSON><PERSON>out<PERSON>} from '../app.routes';
import {Valida<PERSON>} from '../services/validator.service';
import {Authentication} from '../middlewares/authentication';

export const SESSION_ROUTES: IRoute[] = [
    {
        url: '/login',
        httpMethod: 'post',
        controllerMethod: SessionController.getInstance().login,
        validatorsList: Validator.auth.onLogin,

    },
    {
        url: '/:system/token',
        httpMethod: 'get',
        controllerMethod: SessionController.getInstance().singleUseTokenAuthentication,
    },
    {
        url: '/token-verify',
        httpMethod: 'get',
        controllerMethod: SessionController.getInstance().systemUserTokenVerification,
        middleware: [Authentication.validateToken]
    },
    {
        url: '/:system/initiates',
        httpMethod: 'get',
        controllerMethod: SessionController.getInstance().systemInitiate,
        middleware: [Authentication.validateToken]
    },
    {
        url: '/single-use-token',
        httpMethod: 'get',
        controllerMethod: SessionController.getInstance().getSingleUseToken,
        middleware: [Authentication.validateToken]
    },
    {
        url: '/revoke-session',
        httpMethod: 'get',
        controllerMethod: SessionController.getInstance().revokeSession,
        middleware: [Authentication.validateToken]
    },
    {
        url: '/verify/recover-password-token',
        httpMethod: 'get',
        controllerMethod: SessionController.getInstance().verifyRecoverPasswordToken,
    },

];
