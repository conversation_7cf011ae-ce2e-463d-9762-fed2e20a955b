import {MigrationInterface, QueryRunner} from 'typeorm';

export class AddBelowThreshouldContextsToImplementationManager1573706657392 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<any> {

        await queryRunner.query(`
            delete from context_accesses where role_id = 20`);
        await queryRunner.query(`
            insert into
              context_accesses (operation_id, is_enabled, role_id, context_id)
            select 1, 1, 20, id
            from contexts where slug in (
                'cancelled-plans-count-widget',
                'confirmed-plans-count-widget',
                'confirmed-plans-vs-created-plans-percentage-widget',
                'created-plans-count-widget',
                'five-most-contingency-results-predicted',
                'five-most-contingency-risks-predicted',
                'five-most-cpv-based-on-amount',
                'five-most-frequent-cpv-divisions',
                'implemented-plans-count-widget',
                'lot',
                'plan-vs-actual-project-number-and-amount-based-on-budget-type',
                'plan-vs-actual-project-number-and-amount-based-on-contract-type',
                'plan-vs-actual-project-number-and-amount-based-on-plan-status',
                'plan-vs-actual-project-number-and-amount-based-on-procurement-method',
                'plan-vs-actual-project-number-and-amount-based-on-procurement-preference',
                'plan-vs-actual-project-number-and-amount-based-on-procurement-type',
                'plan-vs-actual-project-number-and-amount-based-on-selection-method',
                'plan-vs-actual-projects-number-based-on-timeline-consultancy',
                'plan-vs-actual-projects-number-based-on-timeline-non-consultancy',
                'plan-vs-amount-project-based-on-province',
                'project',
                'project-implementation',
                'project-implementation-analysis',
                'project-implementation-challenge',
                'project-implementation-cpv',
                'project-implementation-document',
                'project-implementation-location-domestic',
                'project-implementation-location-foreign',
                'project-implementation-potential-risk',
                'project-implementation-procurement-plan-general-details',
                'project-implementation-procurement-process-progress-timeline',
                'project-number-and-amount-based-on-award-authority-threshold',
                'project-planning',
                'project-planning-analysis',
                'project-planning-challenge',
                'project-planning-cpv',
                'project-planning-document',
                'project-planning-location-domestic',
                'project-planning-location-foreign',
                'project-planning-potential-risk',
                'project-planning-procurement-plan-general-details',
                'project-planning-procurement-process-progress-timeline',
                'published-plans-count-widget',
                'transferred-plans-count-widget',
                'published-plans-vs-confirmed-plans-percentage-widget'
              )`);
        await queryRunner.query(`
            insert into
              context_accesses (operation_id, is_enabled, role_id, context_id)
            select 2, 1, 20, id
            from contexts where slug in (
                'lot',
                'project-implementation-challenge',
                'project-implementation-cpv',
                'project-implementation-location-domestic',
                'project-implementation-location-foreign',
                'project-implementation-potential-risk',
                'project-planning-challenge',
                'project-planning-cpv',
                'project-planning-location-domestic',
                'project-planning-location-foreign',
                'project-planning-potential-risk'
              )`);
        await queryRunner.query(`
            insert into
              context_accesses (operation_id, is_enabled, role_id, context_id)
            select 3, 1, 20, id
            from contexts where slug in (
                'lot',
                'project',
                'project-implementation-challenge',
                'project-implementation-cpv',
                'project-implementation-location-domestic',
                'project-implementation-location-foreign',
                'project-implementation-potential-risk',
                'project-planning-challenge',
                'project-planning-cpv',
                'project-planning-location-domestic',
                'project-planning-location-foreign',
                'project-planning-potential-risk'
              )`);
        await queryRunner.query(`
            insert into
              context_accesses (operation_id, is_enabled, role_id, context_id)
            select 4, 1, 20, id
            from contexts where slug in (
                'project-implementation',
                'project-implementation-analysis',
                'project-implementation-challenge',
                'project-implementation-cpv',
                'project-implementation-document',
                'project-implementation-location-domestic',
                'project-implementation-location-foreign',
                'project-implementation-potential-risk',
                'project-implementation-procurement-plan-general-details',
                'project-implementation-procurement-process-progress-timeline',
                'project-planning',
                'project-planning-analysis',
                'project-planning-challenge',
                'project-planning-cpv',
                'project-planning-document',
                'project-planning-location-domestic',
                'project-planning-location-foreign',
                'project-planning-potential-risk',
                'project-planning-procurement-plan-general-details',
                'project-planning-procurement-process-progress-timeline'
              )`);
        await queryRunner.query(`
            insert into
              context_accesses (operation_id, is_enabled, role_id, context_id)
            select 6, 1, 20, id
            from contexts where slug in (
                'project-implementation-challenge',
                'project-implementation-cpv',
                'project-implementation-document',
                'project-implementation-location-domestic',
                'project-implementation-location-foreign',
                'project-implementation-potential-risk',
                'project-implementation-procurement-plan-general-details',
                'project-implementation-procurement-process-progress-timeline',
                'project-planning-challenge',
                'project-planning-cpv',
                'project-planning-document',
                'project-planning-location-domestic',
                'project-planning-location-foreign',
                'project-planning-potential-risk',
                'project-planning-procurement-plan-general-details',
                'project-planning-procurement-process-progress-timeline'
              )`);
        await queryRunner.query(`
            insert into
              context_accesses (operation_id, is_enabled, role_id, context_id)
            select 9, 1, 20, id
            from contexts where slug in (
                'lot',
                'project',
                'project-implementation',
                'project-planning'
              )`);
        await queryRunner.query(`
            insert into
              context_accesses (operation_id, is_enabled, role_id, context_id)
            select 11, 1, 20, id
            from contexts where slug in (
               'lot',
                'project',
                'project-implementation-challenge',
                'project-implementation-cpv',
                'project-implementation-document',
                'project-implementation-location-domestic',
                'project-implementation-location-foreign',
                'project-implementation-potential-risk',
                'project-implementation-procurement-plan-general-details',
                'project-implementation-procurement-process-progress-timeline',
                'project-planning-challenge',
                'project-planning-cpv',
                'project-planning-document',
                'project-planning-location-domestic',
                'project-planning-location-foreign',
                'project-planning-potential-risk',
                'project-planning-procurement-plan-general-details',
                'project-planning-procurement-process-progress-timeline'
              )`);
        await queryRunner.query(`
            insert into
              context_accesses (operation_id, is_enabled, role_id, context_id)
            select 12, 1, 20, id
            from contexts where slug in (
               'project-implementation-challenge',
                'project-implementation-cpv',
                'project-implementation-document',
                'project-implementation-location-domestic',
                'project-implementation-location-foreign',
                'project-implementation-potential-risk',
                'project-implementation-procurement-plan-general-details',
                'project-implementation-procurement-process-progress-timeline',
                'project-planning-challenge',
                'project-planning-cpv',
                'project-planning-document',
                'project-planning-location-domestic',
                'project-planning-location-foreign',
                'project-planning-potential-risk',
                'project-planning-procurement-plan-general-details',
                'project-planning-procurement-process-progress-timeline'
              )`);

        await queryRunner.query(`
            insert into
              context_accesses (operation_id, is_enabled, role_id, context_id)
            select 13, 1, 20, id
            from contexts where slug in (
                'lot',
                'project'
              )`);

        await queryRunner.query(`
            insert into
              context_accesses (operation_id, is_enabled, role_id, context_id)
            select 15, 1, 20, id
            from contexts where slug in (
                'lot',
                'project'
              )`);
        await queryRunner.query(`
            insert into
              context_accesses (operation_id, is_enabled, role_id, context_id)
            select 17, 1, 20, id
            from contexts where slug in (
                'lot',
                'project'
              )`);

// Implementation approvar
        await queryRunner.query(`
            delete from context_accesses where role_id = 21`);

        await queryRunner.query(`
            insert into
              context_accesses (operation_id, is_enabled, role_id, context_id)
            select 1, 1, 21, id
            from contexts where id in (
          select context_id from context_accesses where operation_id = 1 and role_id = 13
              )`);
        await queryRunner.query(`
            insert into
              context_accesses (operation_id, is_enabled, role_id, context_id)
            select 3, 1, 21, id
            from contexts where id in (
                select context_id from context_accesses where operation_id = 3 and role_id = 13
              )`);
        await queryRunner.query(`
            insert into
              context_accesses (operation_id, is_enabled, role_id, context_id)
            select 4, 1, 21, id
            from contexts where id in (
                   select context_id from context_accesses where operation_id = 4 and role_id = 13
              )`);
        await queryRunner.query(`
            insert into
              context_accesses (operation_id, is_enabled, role_id, context_id)
            select 5, 1, 21, id
            from contexts where id in (
            select context_id from context_accesses where operation_id = 5 and role_id = 13
              )`);
        await queryRunner.query(`
            insert into
              context_accesses (operation_id, is_enabled, role_id, context_id)
            select 8, 1, 21, id
            from contexts where id in (
              select context_id from context_accesses where operation_id = 8 and role_id = 13
              )`);

        await queryRunner.query(`
            insert into
              context_accesses (operation_id, is_enabled, role_id, context_id)
            select 14, 1, 21, id
            from contexts where id in (
               select context_id from context_accesses where operation_id = 14 and role_id = 13
              )`);
        await queryRunner.query(`
            insert into
              context_accesses (operation_id, is_enabled, role_id, context_id)
            select 16, 1, 21, id
            from contexts where id in (
              select context_id from context_accesses where operation_id = 16 and role_id = 13
              )`);

        await queryRunner.query(`
            insert into
              context_accesses (operation_id, is_enabled, role_id, context_id)
            select 18, 1, 21, id
            from contexts where id in (
               select context_id from context_accesses where operation_id = 18 and role_id = 13
              )`);
        await queryRunner.query(`
            insert into
              context_accesses (operation_id, is_enabled, role_id, context_id)
            select 19, 1, 21, id
            from contexts where id in (
               select context_id from context_accesses where operation_id = 19 and role_id = 13
              )`);
    }

    public async down(queryRunner: QueryRunner): Promise<any> {
        await queryRunner.query(`
            delete from context_accesses where role_id = 20`);
        await queryRunner.query(`
            insert into
              context_accesses (operation_id, is_enabled, role_id, context_id)
            select 1, 1, 20, id
            from contexts where slug in (
              'created-plans-count-widget',
              'confirmed-plans-count-widget',
              'published-plans-count-widget',
              'transferred-plans-count-widget',
              'implemented-plans-count-widget',
              'cancelled-plans-count-widget',
              'confirmed-plans-vs-created-plans-percentage-widget',
              'published-plans-vs-confirmed-plans-percentage-widget',
              'five-most-contingency-risks-predicted',
              'five-most-contingency-results-predicted',
              'five-most-frequent-cpv-divisions',
              'five-most-cpv-based-on-amount',
              'plan-vs-amount-project-based-on-province',
              'plan-vs-actual-projects-number-based-on-timeline-non-consultancy',
              'plan-vs-actual-projects-number-based-on-timeline-consultancy',
              'plan-vs-actual-project-number-and-amount-based-on-procurement-type',
              'plan-vs-actual-project-number-and-amount-based-on-contract-type',
              'plan-vs-actual-project-number-and-amount-based-on-budget-type',
              'plan-vs-actual-project-number-and-amount-based-on-procurement-preference',
              'plan-vs-actual-project-number-and-amount-based-on-selection-method',
              'plan-vs-actual-project-number-and-amount-based-on-procurement-method',
              'project-number-and-amount-based-on-award-authority-threshold',
              'plan-vs-actual-project-number-and-amount-based-on-plan-status',
              'project',
              'lot',
              'project-planning',
              'project-planning-procurement-plan-general-details',
              'project-planning-cpv',
              'project-planning-location-domestic',
              'project-planning-location-foreign',
              'project-planning-procurement-process-progress-timeline',
              'project-planning-potential-risks',
              'project-planning-documents',
              'project-planning-challenges',
              'project-planning-analysis',
              'project-implementation',
              'project-implementation-procurement-plan-general-details',
              'project-implementation-cpv',
              'project-implementation-location-domestic',
              'project-implementation-location-foreign',
              'project-implementation-procurement-process-progress-timeline',
              'project-implementation-potential-risk',
              'project-implementation-document',
              'project-implementation-challenge',
              'project-implementation-analysis'
              )`);
        await queryRunner.query(`
            insert into
              context_accesses (operation_id, is_enabled, role_id, context_id)
            select 2, 1, 20, id
            from contexts where slug in (
              'project-implementation-cpv',
              'project-implementation-location-domestic',
              'project-implementation-location-foreign',
              'project-implementation-potential-risk',
              'project-implementation-challenge'
              )`);
        await queryRunner.query(`
            insert into
              context_accesses (operation_id, is_enabled, role_id, context_id)
            select 3, 1, 20, id
            from contexts where slug in (
              'project',
              'lot',
              'project-planning-cpv',
              'project-planning-location-domestic',
              'project-planning-location-foreign',
              'project-planning-potential-risks',
              'project-planning-challenges',
              'project-implementation-cpv',
              'project-implementation-location-domestic',
              'project-implementation-location-foreign',
              'project-implementation-potential-risk',
              'project-implementation-challenge'
              )`);
        await queryRunner.query(`
            insert into
              context_accesses (operation_id, is_enabled, role_id, context_id)
            select 4, 1, 20, id
            from contexts where slug in (
              'project-implementation',
              'project-implementation-procurement-plan-general-details',
              'project-implementation-cpv',
              'project-implementation-location-domestic',
              'project-implementation-location-foreign',
              'project-implementation-procurement-process-progress-timeline',
              'project-implementation-potential-risk',
              'project-implementation-document',
              'project-implementation-challenge',
              'project-implementation-analysis'
              )`);
        await queryRunner.query(`
            insert into
              context_accesses (operation_id, is_enabled, role_id, context_id)
            select 6, 1, 20, id
            from contexts where slug in (
              'project-implementation-procurement-plan-general-details',
              'project-implementation-cpv',
              'project-implementation-location-domestic',
              'project-implementation-location-foreign',
              'project-implementation-procurement-process-progress-timeline',
              'project-implementation-potential-risk',
              'project-implementation-document',
              'project-implementation-challenge'
              )`);
        await queryRunner.query(`
            insert into
              context_accesses (operation_id, is_enabled, role_id, context_id)
            select 9, 1, 20, id
            from contexts where slug in (
              'project-implementation'
              )`);
        await queryRunner.query(`
            insert into
              context_accesses (operation_id, is_enabled, role_id, context_id)
            select 11, 1, 20, id
            from contexts where slug in (
              'project-implementation-procurement-plan-general-details',
              'project-implementation-cpv',
              'project-implementation-location-domestic',
              'project-implementation-location-foreign',
              'project-implementation-procurement-process-progress-timeline',
              'project-implementation-potential-risk',
              'project-implementation-document',
              'project-implementation-challenge'
              )`);
        await queryRunner.query(`
            insert into
              context_accesses (operation_id, is_enabled, role_id, context_id)
            select 12, 1, 20, id
            from contexts where slug in (
              'project-implementation-procurement-plan-general-details',
              'project-implementation-cpv',
              'project-implementation-location-domestic',
              'project-implementation-location-foreign',
              'project-implementation-procurement-process-progress-timeline',
              'project-implementation-potential-risk',
              'project-implementation-document',
              'project-implementation-challenge'
              )`);


// Implementation approvar
        await queryRunner.query(`
            delete from context_accesses where role_id = 21`);
        await queryRunner.query(`
            insert into
              context_accesses (operation_id, is_enabled, role_id, context_id)
            select 1, 1, 21, id
            from contexts where slug in (
              'created-plans-count-widget',
              'confirmed-plans-count-widget',
              'published-plans-count-widget',
              'transferred-plans-count-widget',
              'implemented-plans-count-widget',
              'cancelled-plans-count-widget',
              'confirmed-plans-vs-created-plans-percentage-widget',
              'published-plans-vs-confirmed-plans-percentage-widget',
              'five-most-contingency-risks-predicted',
              'five-most-contingency-results-predicted',
              'five-most-frequent-cpv-divisions',
              'five-most-cpv-based-on-amount',
              'plan-vs-amount-project-based-on-province',
              'plan-vs-actual-projects-number-based-on-timeline-non-consultancy',
              'plan-vs-actual-projects-number-based-on-timeline-consultancy',
              'plan-vs-actual-project-number-and-amount-based-on-procurement-type',
              'plan-vs-actual-project-number-and-amount-based-on-contract-type',
              'plan-vs-actual-project-number-and-amount-based-on-budget-type',
              'plan-vs-actual-project-number-and-amount-based-on-procurement-preference',
              'plan-vs-actual-project-number-and-amount-based-on-selection-method',
              'plan-vs-actual-project-number-and-amount-based-on-procurement-method',
              'project-number-and-amount-based-on-award-authority-threshold',
              'plan-vs-actual-project-number-and-amount-based-on-plan-status',
              'project',
              'lot',
              'project-planning',
              'project-planning-procurement-plan-general-details',
              'project-planning-cpv',
              'project-planning-location-domestic',
              'project-planning-location-foreign',
              'project-planning-procurement-process-progress-timeline',
              'project-planning-potential-risk',
              'project-planning-document',
              'project-planning-challenge',
              'project-planning-analysis',
              'project-implementation',
              'project-implementation-procurement-plan-general-details',
              'project-implementation-cpv',
              'project-implementation-location-domestic',
              'project-implementation-location-foreign',
              'project-implementation-procurement-process-progress-timeline',
              'project-implementation-potential-risk',
              'project-implementation-document',
              'project-implementation-challenge',
              'project-implementation-analysis'
              )`);
        await queryRunner.query(`
            insert into
              context_accesses (operation_id, is_enabled, role_id, context_id)
            select 3, 1, 21, id
            from contexts where slug in (
              'project',
              'lot',
              'project-planning-cpv',
              'project-planning-location-domestic',
              'project-planning-location-foreign',
              'project-planning-potential-risk',
              'project-planning-challenge',
              'project-implementation-cpv',
              'project-implementation-location-domestic',
              'project-implementation-location-foreign',
              'project-implementation-potential-risk',
              'project-implementation-challenge'
              )`);
        await queryRunner.query(`
            insert into
              context_accesses (operation_id, is_enabled, role_id, context_id)
            select 4, 1, 21, id
            from contexts where slug in (
              'project-planning',
              'project-planning-procurement-plan-general-details',
              'project-planning-cpv',
              'project-planning-location-domestic',
              'project-planning-location-foreign',
              'project-planning-procurement-process-progress-timeline',
              'project-planning-potential-risk',
              'project-planning-document',
              'project-planning-challenge',
              'project-planning-analysis',
              'project-implementation',
              'project-implementation-procurement-plan-general-details',
              'project-implementation-cpv',
              'project-implementation-location-domestic',
              'project-implementation-location-foreign',
              'project-implementation-procurement-process-progress-timeline',
              'project-implementation-potential-risk',
              'project-implementation-document',
              'project-implementation-challenge',
              'project-implementation-analysis'
              )`);
        await queryRunner.query(`
            insert into
              context_accesses (operation_id, is_enabled, role_id, context_id)
            select 5, 1, 21, id
            from contexts where slug in (
              'project-implementation-procurement-plan-general-details',
              'project-implementation-cpv',
              'project-implementation-location-domestic',
              'project-implementation-location-foreign',
              'project-implementation-procurement-process-progress-timeline',
              'project-implementation-potential-risk',
              'project-implementation-document',
              'project-implementation-challenge'
              )`);
        await queryRunner.query(`
            insert into
              context_accesses (operation_id, is_enabled, role_id, context_id)
            select 8, 1, 21, id
            from contexts where slug in (
              'project-implementation'
              )`);
    }

}
