import {Injectable} from '@angular/core';
import {BehaviorSubject, Observable} from 'rxjs';
import {HttpService} from './http.service';
import {IOneToOneFormsScanData} from './complaint.type';

export interface IComplaintService {
    id: number | any;
    user_identification_number: string | any;
    isComplaintSelected: boolean | any;

}


@Injectable()

export class ComplaintService {

    oneToOneScanData: IOneToOneFormsScanData;
    encodedProjectNpaIdentificationNumber: string | any;

    public complaintInformation = new BehaviorSubject<IComplaintService>(
        {
            id: undefined,
            user_identification_number: undefined,
            isComplaintSelected: false
        }
    );

    public complaintData = new BehaviorSubject<any>([]);

    constructor(private _httpService: HttpService) {
        this.complaintInformation.subscribe(
            (data: IComplaintService) => {
                this.encodedProjectNpaIdentificationNumber = encodeURIComponent(encodeURIComponent(data.user_identification_number));
            }, (error: Error) => {
                console.error(error);
            });
    }

    set complaintCreateData(data) {
        this.complaintData.next(data);
    }

    scanOneToOneRelations(lotId) {
        return this._httpService.get(`api/complaint/scan/${lotId}`);
    }

    public reset() {
        this.oneToOneScanData = undefined;
        this.complaintInformation.next({
            id: undefined,
            user_identification_number: undefined,
            isComplaintSelected: undefined,
        });
        this.encodedProjectNpaIdentificationNumber = undefined;
    }

    public getRoleUsersMap(lotId: number): Observable<any> {
        return this._httpService.get(`api/complaint/lot/${lotId}/role-users-map`);
    }
}
