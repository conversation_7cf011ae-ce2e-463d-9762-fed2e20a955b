<div fxLayout="column" class="exceptional-mat-button-wrapper">
    <div *ngIf="authService.user.value.role.slug !== 'dev-ops'"
         fxLayout="row wrap" class="scorecards-wrapper">
        <npa-scorecard iconName="person" fxFlex="25"
                       [text]=""
                       [subText]="''">
            <label title>{{'SYSTEM_LAST_GENERATED_REPORT_DATE' | translate}}
            </label>
            <label footer></label>
            <label back>
                <div>

                </div>
            </label>
        </npa-scorecard>
        <npa-scorecard iconName="alarm" fxFlex="25"
                       [text]=""
                       [subText]="''">
            <label title>{{'NUMBER_OF_GENERATED_REPORTS' | translate}}
            </label>
            <label footer></label>
            <label back>

            </label>
        </npa-scorecard>
        <npa-scorecard iconName="home" fxFlex="25"
                       [text]=""
                       [subText]="">
            <label title>{{'NUMBER_OF_DOWNLOADED_REPORTS' | translate}}
            </label>
            <label footer></label>
            <label back>

            </label>
        </npa-scorecard>
        <npa-scorecard iconName="sync_problem" fxFlex="25"
                       [text]=""
                       [subText]="">
            <label title>{{'NUMBER_OF_ALL_GENERATED_REPORTS' | translate}}
            </label>
            <label footer></label>
            <label back>
            </label>
        </npa-scorecard>
    </div>
    <mat-card fxFlex="100">
        <mat-card-header class="main-page-bottom-border">
            {{'REPORTS'|translate}}
        </mat-card-header>
        <mat-card-content fxLayout="column">
            <div fxFlex="100">
                <a mat-button
                   type="button"
                   routerLink="generate-and-download-reports"
                >{{'GENERATE_AND_DOWNLOAD_REPORTS'|translate}}</a>
            </div>
        </mat-card-content>
    </mat-card>
</div>