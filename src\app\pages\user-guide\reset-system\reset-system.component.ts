import {Component, OnInit} from '@angular/core';
import {NotificationsService} from '../../shared/services/notifications.service';
import {MatDialog} from '@angular/material';
import {ConfirmationDialogComponent} from '../../shared/confirmation-dialog/confirmation-dialog.component';
import {TranslateService} from '../../shared/services/translate.service';

@Component({
    selector: 'npa-reset-system',
    templateUrl: './reset-system.component.html',
    styleUrls: ['./reset-system.component.styl']
})
export class ResetSystemComponent implements OnInit {

    constructor(private _notificationsService: NotificationsService,
                private _dialog: MatDialog,
                public translate: TranslateService) {
    }

    ngOnInit() {
        this._notificationsService.dismissLoading();
    }

    resetSystem() {
        const dialogRef = this._dialog.open(ConfirmationDialogComponent, {
            width: '350px',
            data: {title: this.translate.translateKey('RESET_SYSTEM'), message: this.translate.translateKey('ARE_YOU_SURE_YOU_WANT_TO_RESET_THE_SYSTEM')},
            disableClose: true,
        });
    }
}
