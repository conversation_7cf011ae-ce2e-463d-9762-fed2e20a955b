@import '../../../../styles/definitions.styl'
.main-sidebar[lang=prs], .main-sidebar[lang=ps]
  position fixed
  top sizeHeaderHeight
  right 0
  bottom 0
  width sizeSidebarMainWidthOpen
  background-color colorDark
  transition all .2s linear
  color colorSidebarText
  white-space nowrap
  padding-top sizeMenuItemHeight
  overflow-y: hidden;
  overflow-x: hidden;
  z-index: 2

.main-sidebar[lang=en]
  position fixed
  top sizeHeaderHeight
  left 0
  bottom 0
  width sizeSidebarMainWidthOpen
  background-color colorDark
  transition all .2s linear
  color colorSidebarText
  white-space nowrap
  padding-top sizeMenuItemHeight
  overflow-y: hidden;
  overflow-x: hidden;
  z-index: 2

.main-sidebar.close
  width sizeSidebarMainWidthClose

@supports (-webkit-appearance:none)
  .main-sidebar
    &:hover
      overflow-y overlay !important

::-moz-tree-row(hover), .main-sidebar
  overflow-y auto !important

::-webkit-scrollbar
  width 5px
  transition all 1s linear

::-webkit-scrollbar-track
  background-color colorDark

::-webkit-scrollbar-thumb
  background #666666
  border-radius: 10px

::-webkit-scrollbar-thumb:hover
  background-color #000
